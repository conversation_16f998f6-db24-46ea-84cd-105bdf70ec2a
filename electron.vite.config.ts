import vue from '@vitejs/plugin-vue'
import { defineConfig, externalizeDepsPlugin } from 'electron-vite'
import { resolve } from 'path'
import vueDevTools from 'vite-plugin-vue-devtools'

export default defineConfig({
  main: {
    plugins: [externalizeDepsPlugin()],
    build: {
      rollupOptions: {
        external: ['electron']
      }
    }
  },
  preload: {
    plugins: [externalizeDepsPlugin()],
    build: {
      rollupOptions: {
        output: {
          format: 'cjs',
          entryFileNames: '[name].js'
        }
      }
    }
  },
  renderer: {
    root: '.',
    // 生产环境下使用相对路径
    base: './',
    plugins: [vue(), vueDevTools()],
    resolve: {
      alias: {
        '@': resolve('src/renderer')
      }
    },
    build: {
      rollupOptions: {
        input: resolve(__dirname, 'index.html')
      },
      // 确保静态资源正确处理
      assetsDir: 'assets'
    },
    server: {
      host: '127.0.0.1',
      port: 3000
    }
  }
})
