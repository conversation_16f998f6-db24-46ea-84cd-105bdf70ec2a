# 富文本编辑器发送功能调试指南

## 问题描述
1. Quill 初始化时报错：`Cannot register "bullet" specified in "formats" config`
2. Command+Enter 组合键无响应，无法发送消息

## 已修复的问题

### 1. Quill 格式配置错误
**问题**：在 `formats` 配置中包含了 `'bullet'`，这在 Quill 2.x 版本中不被支持。

**修复**：
- 从 `RichTextEditor.vue` 的 `formats` 数组中移除了 `'bullet'`
- 从 `MessageRenderer.vue` 的 `formats` 数组中移除了 `'bullet'`
- 只保留 `'list'` 格式，它包含了有序和无序列表

### 2. 键盘快捷键绑定问题
**问题**：Quill 的键盘绑定可能在初始化时机不正确或者绑定失败。

**修复**：
- 增加了初始化等待时间（100ms）
- 添加了双重键盘绑定策略：
  1. Quill 内置键盘绑定（主要方案）
  2. DOM 原生事件监听（备用方案）
- 添加了详细的调试日志来跟踪绑定过程

## 添加的调试日志

### 完整的调试链路
1. **键盘事件检测** - 检测按键是否被正确捕获
2. **编辑器内容获取** - 验证内容是否正确获取
3. **发送流程跟踪** - 跟踪整个发送链路
4. **API 调用详情** - 记录网络请求和响应
5. **错误处理** - 详细记录所有可能的错误

### 日志标识符说明
- 🔥 键盘快捷键检测
- 📤 RichTextEditor 发送流程
- 🚀 MessageInput 发送流程
- 🌐 Store 和 API 调用
- 📋 内容获取和处理
- 📝 内容变化监听
- ❌ 错误和阻止情况
- ✅ 成功完成的步骤

## 测试方法

### 1. 使用测试页面
访问 `/test` 路由，在"富文本编辑器测试"部分：
1. 在编辑器中输入一些内容
2. 按 `Cmd+Enter`（Mac）或 `Ctrl+Enter`（Windows/Linux）
3. 观察控制台日志和页面反馈

### 2. 使用调试按钮
在编辑器下方有两个调试按钮：
- **测试发送**：直接调用 handleSend 方法
- **测试键盘事件**：模拟键盘快捷键事件

### 3. 在实际应用中测试
访问 `/notes` 路由，在消息输入框中测试快捷键功能。

## 预期的调试输出

### 正常情况下的日志序列
```
🚀 开始初始化编辑器...
✅ Quill实例创建成功
🔧 准备添加键盘绑定
🔧 quill.keyboard 存在: true
🔧 quill.keyboard.addBinding 存在: true
✅ Cmd+Enter Quill绑定添加成功
✅ Ctrl+Enter Quill绑定添加成功
✅ DOM键盘事件监听添加成功
✅ 所有键盘绑定添加完成
```

### 按下快捷键时的日志序列
```
🎹 检测到按键: {key: "Enter", metaKey: true, ctrlKey: false, code: "Enter"}
🔥 DOM事件检测到快捷键
📤 RichTextEditor.handleSend 开始执行
📤 hasContent.value: true
📤 props.disabled: false
📋 getContent 开始执行
📋 quill 实例存在: true
📋 获取到的 delta: {...}
📋 序列化后的内容: {...}
📤 准备触发 send 事件
✅ send 事件已触发
```

## 故障排除

### 如果仍然没有日志输出
1. 检查浏览器控制台是否打开
2. 确认编辑器是否正确初始化（查找初始化日志）
3. 尝试点击"测试键盘事件"按钮验证事件处理器

### 如果有日志但发送失败
1. 查找 ❌ 标记的错误日志
2. 检查 `hasContent.value` 和 `props.disabled` 的值
3. 验证内容获取是否成功

### 如果 API 调用失败
1. 查找 🌐 标记的 API 相关日志
2. 检查网络连接和服务器状态
3. 验证认证 token 是否正确

## 清理调试代码

在生产环境中，建议移除以下调试元素：
1. 测试页面中的调试按钮
2. RichTextEditor.vue 中的调试控制面板
3. 所有 console.log 调试语句（可选）

## 联系支持

如果问题仍然存在，请提供：
1. 完整的控制台日志输出
2. 浏览器和操作系统信息
3. 重现问题的具体步骤
