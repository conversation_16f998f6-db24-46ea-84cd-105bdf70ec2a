package tools

import (
	"chat_note_api/pkg/vo"
	"encoding/json"
	"fmt"
	"testing"
)

func TestEncipher(t *testing.T) {
	a := vo.UserRes{
		Id: "0",

		UpdatedTime: 0,
		CreatedTime: 0,
		DeleteTime:  0,
	}
	azureLlmProvideAuth := a
	auth, err := json.<PERSON>(azureLlmProvideAuth)
	if err != nil {
		t.Error(err)
		return
	}
	encipher := Encipher(string(auth))
	fmt.Println(encipher)

	decipher := Decrypt(encipher)
	fmt.Println(decipher)
}

func TestDecrypt(t *testing.T) {
	str := "/3iaURohzlFYZUbat6fEtqyIFrt1x/0ec56ghsiP83nW4ljvdDf7DD6pSAbHU1SyjmWOXRwd3N03gaxDLDqXxO7fWPUmd7lWzrxBsXj+qrNrU1yCRQSdCgfLFNEy6wMxd9KXDqNStssgbFB6+9m47CcOlt3/cA1gHNCHL4UZZ/66sYyWaH45HaRDZngnHT4HKDTMKnFdOcmNJqP7IiqLbz9MM6Ji896lf/j64SJXSd8="

	fmt.Println(Decrypt(str))
}
