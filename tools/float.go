package tools

import (
	"fmt"
	"strconv"
)

func Float64ToFixed(f float64) float64 {

	formattedString := fmt.Sprintf("%.2f", f)

	convertedFloatValue, err := strconv.ParseFloat(formattedString, 64)
	if err != nil {
		fmt.Println("Error converting string to float64:", err)
		return float64(0)
	}

	return convertedFloatValue
}

// DivideAsMuchAsPossible 尽可能除尽被除数，返回商和余数
func DivideAsMuchAsPossible(dividend, divisor float64) (float64, float64) {
	quotient := dividend / divisor
	remainder := dividend - (quotient * divisor)
	return quotient, remainder
}
