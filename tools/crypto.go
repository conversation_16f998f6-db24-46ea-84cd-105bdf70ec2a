package tools

import (
	"os"

	"github.com/deatil/go-cryptobin/cryptobin/crypto"
)

const DEFAULT_KEY = "f1b775003ee885a1"

func Encipher(s string) string {
	key := get_secret_key()
	return crypto.
		FromString(s).
		SetKey(key).
		Aes().
		ECB().
		PKCS7Padding().
		Encrypt().
		ToBase64String()

}
func Decrypt(s string) string {
	key := get_secret_key()
	return crypto.
		FromBase64String(s).
		SetKey(key).
		Aes().
		ECB().
		PKCS7Padding().
		Decrypt().
		ToString()
}

func get_secret_key() string {
	key := os.Getenv("secret_key")
	if key == "" || len(key) != 16 {
		key = DEFAULT_KEY
	}
	return key
}
