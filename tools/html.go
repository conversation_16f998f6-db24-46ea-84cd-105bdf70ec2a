package tools

import (
	"bytes"
	"net/url"

	"github.com/PuerkitoBio/goquery"
)

func ParseHtml(html string) string {
	if html == "" {
		return ""
	}

	hdoc, err := goquery.NewDocumentFromReader(bytes.NewReader([]byte(html)))
	if err != nil {
		return ""
	}

	return hdoc.Text()
}

func ParseHtmlServiceGo(html string) string {
	if html == "" {
		return ""
	}

	hdoc, err := goquery.NewDocumentFromReader(bytes.NewReader([]byte(html)))
	if err != nil {
		return ""
	}
	result := ""
	hdoc.Find(".content-container").Each(func(i int, s *goquery.Selection) {
		result += s.Text()
	})
	return result
}

func UrlEncode(input string) string {
	return url.QueryEscape(input)
}
