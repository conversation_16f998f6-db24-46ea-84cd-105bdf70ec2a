package tools

import (
	"context"
	"time"

	"chat_note_api/constant"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/sirupsen/logrus"
)

type Log struct {
	*logrus.Entry
}

func GenContext(c context.Context) (context.Context, *Log) {
	newLog := func(cc context.Context) *Log {
		log := &Log{
			logrus.WithField(constant.MiddlewareKeyRequestId, cc.Value(constant.MiddlewareKeyRequestId)),
		}
		return log
	}

	switch realContext := c.(type) {
	case *gin.Context:
		contextBackground := context.Background()
		contextBackground = context.WithValue(contextBackground, constant.MiddlewareKeyRequestId, realContext.GetString(constant.MiddlewareKeyRequestId))

		contextBackground = context.WithValue(contextBackground, constant.MiddlewareKeyUserId, realContext.GetString(constant.MiddlewareKeyUserId))
		contextBackground = context.WithValue(contextBackground, constant.MiddlewareKeyUserEmail, realContext.GetString(constant.MiddlewareKeyUserEmail))

		log := newLog(realContext)

		if realContext.GetString(constant.MiddlewareKeyUserId) != "" {
			log.WithUserId(realContext.GetString(constant.MiddlewareKeyUserId))
		}
		contextBackground = context.WithValue(contextBackground, constant.MiddlewareKeyContextLogKey, log)
		return contextBackground, log
	case context.Context:
		value := realContext.Value(constant.MiddlewareKeyContextLogKey)
		if value != nil {
			return realContext, value.(*Log)
		}
		log := newLog(realContext)
		realContext = context.WithValue(realContext, constant.MiddlewareKeyContextLogKey, log)
		return realContext, log
	default:
		contextBackground := context.Background()
		contextBackground = context.WithValue(contextBackground, constant.MiddlewareKeyRequestId, uuid.New().String())
		return contextBackground, newLog(contextBackground)
	}
}

func (l *Log) WithUserId(userId string) *Log {
	l.Entry = l.WithField(constant.MiddlewareKeyUserId, userId)
	return l
}

func (l *Log) Field(key string, value interface{}) *Log {
	l.Entry = l.WithField(key, value)
	return l
}

func GetCompanyId(ctx context.Context) int64 {
	companyId := ctx.Value(constant.MiddlewareKeyCompanyId)

	switch companyId := companyId.(type) {
	case int64:
		return companyId
	case int:
		return int64(companyId)
	default:
		return 0
	}
}

func GetRequestId(ctx context.Context) string {
	requestId := ctx.Value(constant.MiddlewareKeyRequestId)
	return requestId.(string)
}

func GetUserId(ctx context.Context) string {
	userId := ctx.Value(constant.MiddlewareKeyUserId)
	switch userId := userId.(type) {
	case string:
		return userId
	default:
		return ""
	}
}

func GetUserEmail(ctx context.Context) string {
	userEmail := ctx.Value(constant.MiddlewareKeyUserEmail)
	if userEmail == nil {
		return ""
	}
	return userEmail.(string)
}

// GetDurationInMilliseconds takes a start time and returns a duration in milliseconds
func GetDurationInMilliseconds(start time.Time) float64 {
	end := time.Now()
	duration := end.Sub(start)
	milliseconds := float64(duration) / float64(time.Millisecond)
	rounded := float64(int(milliseconds*100+.5)) / 100
	return rounded
}
