package tools

import (
	"fmt"
	"hash/fnv"
	"os"

	"github.com/bwmarrin/snowflake"
	"github.com/google/uuid"
)

var node *snowflake.Node

func InitSnowflake() error {
	// 从环境变量获取podid （metadata.uid）
	pod_id := os.Getenv("POD_ID")
	if pod_id == "" {
		pod_id = uuid.NewString()
	}

	h := fnv.New64a()
	h.Write([]byte(pod_id))

	// snowflake 要求最大nodeID为1023  这里用pod uid 的hash值取余 1024 获得0-1023之间的数
	nodeId := h.Sum64() % 1024

	n, err := snowflake.NewNode(int64(nodeId))
	if err != nil {
		fmt.Println(err)
		return err
	}
	node = n
	return nil
}

func GenId() int64 {
	return node.Generate().Int64()
}
