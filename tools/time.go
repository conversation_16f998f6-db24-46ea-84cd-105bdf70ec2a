package tools

import (
	"errors"
	"fmt"
	"time"
)

const (
	dataDir = "2006/01/02"
)

func DataDir() string {
	return time.Now().Format(dataDir)
}

func TimestampToTime(timestamp int64) string {
	// 将时间戳转换为time.Time类型
	t := time.Unix(timestamp, 0)
	// 格式化时间
	return t.Format("2006-01-02 15:04:05")
}

func TimestampToMonthDay(timestamp int64) string {
	// 将时间戳转换为time.Time类型
	t := time.Unix(timestamp, 0)
	// 格式化时间
	return t.Format("01/02")
}

func TimestampToYMD(timestamp int64) string {
	// 将时间戳转换为time.Time类型
	t := time.Unix(timestamp, 0)
	// 格式化时间
	return t.Format("2006-01-02")
}

func FormatYmd(dateTimeString string) string {
	dateTime, err := time.Parse("2006-01-02T15:04:05+08:00", dateTimeString)
	if err != nil {
		fmt.Println("Error parsing date and time string:", err)
		return ""
	}
	return dateTime.Format("2006-01-02")
}

// getDaysData 函数接受开始和结束时间戳，并返回时间范围内的天维度数据
func GetDaysData(start, end int64) map[string]struct{} {
	daysData := make(map[string]struct{})

	// 计算时间范围内的天数
	days := (end-start)/86400 + 1

	// 迭代时间范围内的每一天，并将每天的 ymd 字符串存储在 map 中
	for i := int64(0); i < days; i++ {
		ymd := time.Unix(start+i*86400, 0).Format("2006-01-02")
		daysData[ymd] = struct{}{}
	}

	return daysData
}

// "2006-01-02" 转时间戳
func Date2Timestamp(dateStr string) int64 {
	// 使用 time.Parse 函数将字符串解析为 time.Time 类型
	t, err := time.Parse("2006-01-02 15:04:05", dateStr)
	if err != nil {
		return int64(0)
	}
	// 使用 Unix 方法将 time.Time 类型转换为时间戳
	// 系统使用 东八区的时间
	timestamp := t.Unix() - 8*60*60
	return timestamp
}

// 获取今天 0 点的毫秒时间戳
func GetMidnightTimestamp() int64 {
	now := time.Now()
	midnight := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())
	midnightTimestamp := midnight.UnixNano() / int64(time.Millisecond)

	return midnightTimestamp
}

// GetDatesInRange 返回在指定时间戳范围内的日期，日期格式为 "Ymd"
func GetDatesInRange(startTimestamp, endTimestamp int64) ([]string, error) {
	if startTimestamp == 0 || endTimestamp == 0 {
		return nil, errors.New("the parameter cannot be 0")
	}
	// 将毫秒时间戳转换为秒时间戳
	startSec := startTimestamp / 1000
	endSec := endTimestamp / 1000

	// 获取系统本地时间
	// 获取系统本地时间
	loc, _ := time.LoadLocation("Asia/Shanghai")

	startTime := time.Unix(startSec+3600*8, 0).In(loc)
	endTime := time.Unix(endSec+3600*8, 0).In(loc)

	// 检查时间范围的有效性
	if endTime.Before(startTime) {
		return nil, fmt.Errorf("end timestamp is before start timestamp")
	}

	var dates []string
	current := startTime

	// 迭代日期，直到达到结束时间的那一天的结束
	for !current.After(endTime) {
		// 检查是否是周末
		if current.Weekday() != time.Saturday && current.Weekday() != time.Sunday {
			dateStr := current.Format("2006-01-02")
			dates = append(dates, dateStr)
		}
		// 增加一天
		current = current.AddDate(0, 0, 1)
	}

	return dates, nil
}

func GetWeeksInRange(startTimestamp, endTimestamp int64) (map[string]string, []int64) {
	weeks := make(map[string]string, 20)

	// 将毫秒时间戳转换为秒时间戳
	startSec := startTimestamp / 1000
	endSec := endTimestamp / 1000

	// 获取系统本地时间
	loc, _ := time.LoadLocation("Asia/Shanghai")

	startTime := time.Unix(startSec, 0).In(loc)
	endTime := time.Unix(endSec, 0).In(loc)

	// 调整开始时间为当前周的周一
	for startTime.Weekday() != time.Monday {
		startTime = startTime.AddDate(0, 0, -1)
	}

	// 调整结束时间为结束周的周日
	for endTime.Weekday() != time.Sunday {
		endTime = endTime.AddDate(0, 0, 1)
	}

	// 初始化当前时间为开始时间
	current := startTime

	// 初始化周数为1
	weekNumber := 1

	// 遍历日期范围
	for current.Before(endTime) || current.Equal(endTime) {
		// 获取当前日期是周几
		weekday := current.Weekday()

		// 如果当前日期是周一，则记录周数
		if weekday == time.Monday {
			weekStartDate := current.Format("2006-01-02")
			weeks[weekStartDate] = current.AddDate(0, 0, 6).Format("2006-01-02")
		}

		// 增加一天
		// current = time.Date(current.Year(), current.Month(), current.Day()+1, 23, 59, 59, 0, current.Location())
		current = current.AddDate(0, 0, 1)
		// 如果当前日期是周日，则增加周数
		if weekday == time.Sunday {
			weekNumber++
		}
	}

	return weeks, []int64{startTime.UTC().Unix() * 1000, time.Date(endTime.Year(),
		endTime.Month(), endTime.Day(), 23, 59, 59, 0, endTime.Location()).Unix() * 1000,
	}
}

func GetMonthsInRange(startTimestamp, endTimestamp int64) ([]string, []int64) {
	// 将毫秒时间戳转换为秒时间戳
	startSec := startTimestamp / 1000
	endSec := endTimestamp / 1000

	// 获取系统本地时间
	loc, _ := time.LoadLocation("Asia/Shanghai")

	startTime := time.Unix(startSec+3600*8, 0).In(loc)
	endTime := time.Unix(endSec+3600*8, 0).In(loc)

	// 初始化结果切片
	var months []string

	// 初始化当前时间为开始时间所在的月份的第一天零点整
	current := time.Date(startTime.Year(), startTime.Month(), 1, 0, 0, 0, 0, time.UTC)

	// 遍历时间范围内的每一个月
	for current.Before(endTime) || current.Equal(endTime) {
		// 格式化当前月份的第一天为字符串
		monthFirstDay := current.Format("2006-01-02")
		// 将该月份的第一天添加到结果切片中
		months = append(months, monthFirstDay)

		// 增加一个月
		current = current.AddDate(0, 1, 0)
	}

	return months, []int64{
		time.Date(startTime.Year(), startTime.Month(), 1, 0, 0, 0, 0, time.UTC).Unix()*1000 - 3600*8*1000,
		time.Date(endTime.Year(), endTime.Month()+1, 0, 23, 59, 59, 0, time.UTC).Unix()*1000 - 3600*8*1000,
	}
}

// 判断两个时间是否属于同一周
func IsSameWeek(start, end string) (bool, error) {
	startTime, err := time.Parse("2006-01-02", start)
	if err != nil {
		return false, err
	}

	endTime, err := time.Parse("2006-01-02", end)
	if err != nil {
		return false, err
	}

	// 获取开始日期所在周的年和周数
	startYear, startWeek := startTime.ISOWeek()

	// 获取结束日期所在周的年和周数
	endYear, endWeek := endTime.ISOWeek()

	// 判断年和周数是否相同
	return (startYear == endYear && startWeek == endWeek), nil
}

func IsSameMonth(time1, time2 string) (bool, error) {
	t1, err := time.Parse("2006-01-02", time1)
	if err != nil {
		return false, err
	}

	t2, err := time.Parse("2006-01-02", time2)
	if err != nil {
		return false, err
	}

	// 比较年和月份是否相同
	return (t1.Year() == t2.Year() && t1.Month() == t2.Month()), nil
}

// GetDatesInRange 返回在指定时间戳范围内的日期，日期格式为 "Ymd"，忽略法定节假日
func GetDatesInRangeIgnoreHolidays(startTimestamp, endTimestamp int64) ([]string, error) {
	if startTimestamp == 0 || endTimestamp == 0 {
		return nil, errors.New("the parameter cannot be 0")
	}
	// 将毫秒时间戳转换为秒时间戳
	startSec := startTimestamp / 1000
	endSec := endTimestamp / 1000

	// 获取系统本地时间
	loc, _ := time.LoadLocation("Asia/Shanghai")

	startTime := time.Unix(startSec+3600*8, 0).In(loc)
	endTime := time.Unix(endSec+3600*8, 0).In(loc)

	// 检查时间范围的有效性
	if endTime.Before(startTime) {
		return nil, fmt.Errorf("end timestamp is before start timestamp")
	}

	var dates []string
	current := startTime

	holidays := GetCurrentYearHolidays()

	// 迭代日期，直到达到结束时间的那一天的结束
	for !current.After(endTime) {
		dateStr := current.Format("2006-01-02")

		y := current.Format("2006")
		if hl, ok := holidays[y]; ok {
			if holiday, ok := hl[dateStr]; ok {
				// 不是节假日需要补班
				if !holiday {
					dates = append(dates, dateStr)
				}
			} else {
				// 检查是否是周末
				if current.Weekday() != time.Saturday && current.Weekday() != time.Sunday {
					dates = append(dates, dateStr)
				}
			}
		} else {
			// 没有节假日配置的情况，默认按周末逻辑
			if current.Weekday() != time.Saturday && current.Weekday() != time.Sunday {
				dates = append(dates, dateStr)
			}
		}
		// 增加一天
		current = current.AddDate(0, 0, 1)
	}

	return dates, nil
}

// 获取公共节假日，手动维护数据，没必要自动更新，毕竟一年一次
func GetCurrentYearHolidays() map[string]map[string]bool {
	dataMap := make(map[string]map[string]bool, 100)

	dataMap["2024"] = map[string]bool{
		"2024-01-01": true,
		"2024-02-10": true,
		"2024-02-11": true,
		"2024-02-12": true,
		"2024-02-13": true,
		"2024-02-14": true,
		"2024-02-15": true,
		"2024-02-16": true,
		"2024-02-17": true,
		"2024-04-04": true,
		"2024-04-05": true,
		"2024-04-06": true,
		"2024-05-01": true,
		"2024-05-02": true,
		"2024-05-03": true,
		"2024-05-04": true,
		"2024-05-05": true,
		"2024-06-10": true,
		"2024-09-15": true,
		"2024-09-16": true,
		"2024-09-17": true,
		"2024-10-01": true,
		"2024-10-02": true,
		"2024-10-03": true,
		"2024-10-04": true,
		"2024-10-05": true,
		"2024-10-06": true,
		"2024-10-07": true,
		"2024-02-04": false,
		"2024-02-18": false,
		"2024-04-07": false,
		"2024-04-28": false,
		"2024-05-11": false,
		"2024-09-14": false,
		"2024-09-29": false,
		"2024-10-12": false,
	}

	return dataMap
}

/*
		获取指定天数和小时的毫秒时间戳
	    timezoneOffset: 时区偏移量
	    day: 天数偏移量
	    hour: 小时偏移量
	    返回值：毫秒时间戳，错误信息
	    示例：
	    GetTimestamp(8, 1, 8)
	    GetTimestamp(8, -1, 8)
	    GetTimestamp(8, 0, 8)
	    GetTimestamp(8, 0, 16)
	    GetTimestamp(8, 1, 16)
	    GetTimestamp(8, -1, 16)
	    GetTimestamp(8, 0, 0)
	    GetTimestamp(8, 1, 0)
	    GetTimestamp(8, -1, 0)
	    GetTimestamp(8, 0, -1)
*/
func GetTimestamp(timezoneOffset int, day int, hour int) (int64, error) {
	// 获取当前时间
	now := time.Now()

	// 计算时区偏移量
	loc := time.FixedZone("Custom", timezoneOffset*3600)

	// 获取当前时间的自定义时区时间
	nowInLoc := now.In(loc)

	// 获取指定天数偏移后的时间
	specifiedDay := nowInLoc.AddDate(0, 0, day)

	// 创建指定天数偏移后指定小时的时间
	specifiedDayAtHour := time.Date(specifiedDay.Year(), specifiedDay.Month(), specifiedDay.Day(), hour, 0, 0, 0, loc)

	// 获取毫秒时间戳
	millis := specifiedDayAtHour.UnixNano() / int64(time.Millisecond)

	return millis, nil
}

func GetNowTimestamp() (int64, error) {
	// 获取当前时间
	now := time.Now()

	// 设置东八区时区
	location, err := time.LoadLocation("Asia/Shanghai")
	if err != nil {
		return 0, err
	}

	// 将当前时间转换为东八区时间
	eightHourAheadTime := now.In(location)

	// 获取毫秒时间戳
	millis := eightHourAheadTime.UnixNano() / int64(time.Millisecond)

	return millis, nil
}
