package tools

// 求交集
func intersect(slice1, slice2 []string) []string {
	m := make(map[string]int)
	nn := make([]string, 0)
	for _, v := range slice1 {
		m[v]++
	}

	for _, v := range slice2 {
		times, _ := m[v]
		if times == 1 {
			nn = append(nn, v)
		}
	}
	return nn
}

// 求差集 slice1-并集
func Difference(slice1, slice2 []string) []string {
	m := make(map[string]int)
	nn := make([]string, 0)
	inter := intersect(slice1, slice2)
	for _, v := range inter {
		m[v]++
	}

	for _, value := range slice1 {
		times, _ := m[value]
		if times == 0 {
			nn = append(nn, value)
		}
	}
	return nn
}

func TrimStringSlice(slice []string) []string {
	m := []string{}
	for _, v := range slice {
		if v != "" {
			m = append(m, v)
		}
	}
	return m
}

func TrimIntSlice(slice []int) []int {
	m := []int{}
	for _, v := range slice {
		if v != 0 {
			m = append(m, v)
		}
	}
	return m
}
