package tools

import (
	"fmt"
	"testing"

	"github.com/davecgh/go-spew/spew"
)

func Test_GetDaysData(t *testing.T) {
	list := GetDaysData(1713715200, 1714060800)

	spew.Dump(list)

	for day := range list {
		spew.Dump(day)
	}

}

func Test_Date2Timestamp(t *testing.T) {
	t2 := Date2Timestamp("2024-04-24 00:00:00")
	spew.Dump(t2)
}
func TestGetDatesInRange_NilTimestamp(t *testing.T) {
	startTimestamp := int64(1714492800000)
	endTimestamp := int64(1716134400000)

	dates, err := GetDatesInRange(startTimestamp, endTimestamp)

	if err != nil {
		t.Error("Expected error for nil timestamps, but got nil")
	}

	fmt.Println(dates)

	// id: 931,dlist: []string{"2024-05-13", "2024-05-20", "2024-04-29", "2024-05-06", "2024-04-01"},start:1711900800000,end:1712419200000

	dates2, rangeWDt := GetWeeksInRange(1714457331504, 1716912000000)

	fmt.Println(dates2, rangeWDt)

	// dates3, rangeMDt := GetMonthsInRange(1710864000000, endTimestamp)

	// fmt.Println(dates3, rangeMDt)

	start := "2024-04-28"
	end := "2024-05-02"

	isSameWeek, _ := IsSameWeek(start, end)
	fmt.Println("是否属于同一周:", isSameWeek)

	start = "2024-04-28"
	end = "2024-04-28"

	isSameWeek, _ = IsSameWeek(start, end)
	fmt.Println("是否属于同一周:", isSameWeek)

	start = "2024-04-28"
	end = "2024-04-29"

	isSameWeek, _ = IsSameWeek(start, end)
	fmt.Println("是否属于同一周:", isSameWeek)

	start = "2024-04-29"
	end = "2024-05-05"

	isSameWeek, _ = IsSameWeek(start, end)
	fmt.Println("是否属于同一周:", isSameWeek)
}

func TestGetDatesInRangeIgnoreHolidays_OnlyWeekendDays(t *testing.T) {

	startTimestamp := int64(1714492800000) // 2024-05-01 00:00:00.000
	endTimestamp := int64(1716134400000)   // 2024-05-20 00:00:00.000

	dates, err := GetDatesInRangeIgnoreHolidays(startTimestamp, endTimestamp)

	if err != nil {
		t.Errorf("Unexpected error: %v", err)
	}

	fmt.Println(dates)

}

func TestGetTimestampDSTStart(t *testing.T) {
	timestamp, err := GetTimestamp(8, -1, 22) // 东八区，前天，22 点
	expectedTimestamp := int64(1716991200000) // 20240529 22:00:00
	if err != nil {
		t.Errorf("Error: %v", err)
	}
	if timestamp != expectedTimestamp {
		t.Errorf("Expected timestamp: %d, got: %d", expectedTimestamp, timestamp)
	}

	timestamp, err = GetTimestamp(8, 1, 22)  // 东八区，前天，22 点
	expectedTimestamp = int64(1717164000000) // 20240531 22:00:00
	if err != nil {
		t.Errorf("Error: %v", err)
	}
	if timestamp != expectedTimestamp {
		t.Errorf("Expected timestamp: %d, got: %d", expectedTimestamp, timestamp)
	}
}
