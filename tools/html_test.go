package tools

import (
	"testing"
)

func TestParseHtml(t *testing.T) {
	{
		html := `<blockquote><strong><em><u>nihao</u></em></strong></blockquote><p><span style="color: rgb(255, 235, 204); background-color: rgb(102, 61, 0);">asdfasdf</span></p><p><br></p><ol><li data-list="ordered"><span class="ql-ui" contenteditable="false"></span>sdf</li><li data-list="ordered"><span class="ql-ui" contenteditable="false"></span>asd</li><li data-list="ordered"><span class="ql-ui" contenteditable="false"></span>a</li><li data-list="ordered"><span class="ql-ui" contenteditable="false"></span>sdf</li><li data-list="ordered"><span class="ql-ui" contenteditable="false"></span>asd</li><li data-list="ordered"><span class="ql-ui" contenteditable="false"></span><br></li></ol>`
		t.Log(ParseHtml(html))
	}
	{
		html := `<p>你好 </p><p><br></p><p>你好</p>`
		t.Log(ParseHtml(html))
	}

	{
		html := `ceshi`
		t.Log(ParseHtml(html))
	}
	{
		html := ``
		t.Log(ParseHtml(html))
	}
}
