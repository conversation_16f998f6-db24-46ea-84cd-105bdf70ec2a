# API 接口对接指南

## 概述

本文档描述了前端富文本编辑器发送功能与后端 `/api/v1/note/create` 接口的对接实现。

## 后端接口规范

### 创建笔记接口

**接口地址**: `POST /api/v1/note/create`

**请求头**:
```
Content-Type: application/json
Authorization: <token>  // 需要认证
```

**请求参数**:
```json
{
  "topic_id": "string",    // 主题ID，必需
  "note_type": "string",   // 笔记类型，必需 (text, rich-text, image, link, file)
  "content": "string"      // 笔记内容，必需
}
```

**响应格式**:
```json
{
  "message": "success"
}
```

### 获取笔记列表接口

**接口地址**: `GET /api/v1/note/list`

**请求参数**:
```
topic_id: string  // 主题ID，必需
last_id: number   // 分页参数，可选
```

**响应格式**:
```json
{
  "data": [
    {
      "id": 123456789,
      "user_id": "user123",
      "topic_id": "topic123",
      "note_type": "rich-text",
      "content": "笔记内容",
      "created_time": 1640995200,
      "updated_time": 1640995200,
      "delete_time": 0
    }
  ]
}
```

## 前端实现

### 1. 类型定义

在 `src/types/index.ts` 中添加了后端接口对应的类型：

```typescript
// 服务端创建笔记请求类型
export interface CreateNoteRequest {
  topic_id: string
  note_type: string
  content: string
}

// 服务端创建笔记响应类型
export interface CreateNoteResponse {
  message: string
}

// 服务端笔记列表响应类型
export interface ListNoteResponse {
  id: number
  user_id: string
  topic_id: string
  note_type: string
  content: string
  created_time: number
  updated_time: number
  delete_time: number
}
```

### 2. API 客户端实现

在 `src/services/api.ts` 中更新了 `messageApi.create` 方法：

```typescript
// 创建消息 - 对接后端 /api/v1/note/create 接口
create: async (message: Omit<NoteMessage, 'id' | 'createdAt' | 'updatedAt' | 'isEdited'>): Promise<NoteMessage> => {
  // 构造后端接口需要的请求参数
  const request: CreateNoteRequest = {
    topic_id: message.themeId,
    note_type: message.type,
    content: message.content
  }
  
  console.log('🌐 调用 /api/v1/note/create 接口，请求参数:', request)
  
  // 调用后端接口创建笔记
  const response = await apiClient.post<CreateNoteResponse>('/api/v1/note/create', request)
  console.log('🌐 后端响应:', response)
  
  // 由于后端只返回成功消息，我们需要构造一个 NoteMessage 对象返回
  const createdMessage: NoteMessage = {
    id: generateId(), // 生成临时 ID
    themeId: message.themeId,
    content: message.content,
    type: message.type,
    createdAt: new Date(),
    isEdited: false,
    metadata: message.metadata
  }
  
  console.log('✅ 笔记创建成功，构造返回对象:', createdMessage)
  return createdMessage
}
```

### 3. 数据转换

前端 `NoteMessage` 类型与后端接口的字段映射：

| 前端字段 | 后端字段 | 说明 |
|---------|---------|------|
| `themeId` | `topic_id` | 主题ID |
| `type` | `note_type` | 笔记类型 |
| `content` | `content` | 笔记内容 |
| `id` | `id` | 笔记ID（前端临时生成） |
| `createdAt` | `created_time` | 创建时间 |

## 测试方法

### 1. 使用测试页面

访问 `/test` 路由，在 "API 接口测试" 部分：

1. 输入主题ID（默认为 "1"）
2. 输入测试笔记内容
3. 点击 "测试创建笔记" 按钮
4. 观察控制台日志和测试结果

### 2. 使用富文本编辑器

在 `/notes` 页面：

1. 选择一个主题
2. 在富文本编辑器中输入内容
3. 按 `Cmd+Enter` 或 `Ctrl+Enter` 发送
4. 观察控制台日志确认 API 调用

### 3. 调试日志

完整的调试日志链路：

```
🌐 调用 /api/v1/note/create 接口，请求参数: {...}
🌐 API请求开始
🌐 URL: http://localhost:8000/api/v1/note/create
🌐 Method: POST
🌐 需要认证，token存在: true
🌐 发送请求...
🌐 收到响应
🌐 响应状态: 200
✅ 响应成功，准备解析JSON
✅ JSON解析成功: {message: "success"}
🌐 后端响应: {message: "success"}
✅ 笔记创建成功，构造返回对象: {...}
```

## 注意事项

### 1. 认证要求

- `/api/v1/note/create` 接口需要认证
- 确保在请求头中包含有效的 Authorization token
- 如果认证失败，会收到 401 状态码

### 2. 错误处理

- 网络错误：检查后端服务是否启动
- 认证错误：检查 token 是否有效
- 参数错误：检查请求参数格式是否正确

### 3. 数据一致性

- 前端生成临时ID，实际ID由后端数据库自动生成
- 如需获取真实ID，可以在创建后调用列表接口刷新数据

## 后续优化建议

1. **返回完整对象**：建议后端接口返回创建的完整笔记对象，包含真实ID
2. **批量操作**：支持批量创建笔记的接口
3. **实时同步**：使用 WebSocket 实现实时数据同步
4. **离线支持**：在网络不可用时缓存操作，网络恢复后同步

## 相关文件

- 前端类型定义：`src/types/index.ts`
- API 客户端：`src/services/api.ts`
- 测试页面：`src/views/TestView.vue`
- 后端控制器：`pkg/controller/note_conntroller.go`
- 后端服务：`pkg/service/note_service.go`
- 数据库管理：`pkg/db/note_manager.go`
