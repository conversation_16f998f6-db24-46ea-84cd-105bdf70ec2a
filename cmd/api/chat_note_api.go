package main

import (
	"log"

	"chat_note_api/pkg/controller"
	"chat_note_api/pkg/db"
	"chat_note_api/tools"

	"github.com/gin-gonic/gin"
	"github.com/joho/godotenv"
	"github.com/sirupsen/logrus"
)

func main() {
	err := godotenv.Load(".env")
	if err != nil {
		log.Fatal("Error loading .env file:", err)
	}
	funcs := tools.ApplicationInitList{}
	funcs.AddInitFunc(func() error {
		logrus.SetLevel(logrus.TraceLevel)
		logrus.SetReportCaller(true)
		logrus.SetFormatter(&logrus.JSONFormatter{})
		return nil
	})

	// 初始化Snowflake
	funcs.AddInitFunc(tools.InitSnowflake)
	// 数据库连接
	funcs.AddInitFunc(db.InitDBManagerMain)
	// 启动定时任务
	// funcs.AddInitFunc(service.StartCron)
	// 初始化 cache hold
	// funcs.AddInitFunc(service.InitCacheHold)
	// 启动dingding服务
	// funcs.AddInitFunc(service.InitDingdingService)
	// defer service.StopDingdingService()
	// 初始化Agent tools 列表
	// funcs.AddInitFunc(service.NewAgentToolService)
	// 启动 api 服务
	funcs.AddInitFunc(initApi)

	err = funcs.Run(true)
	if err != nil {
		log.Fatal("service fatal error,", err)
	}
}

func initApi() error {
	engin := gin.Default()

	// 设置请求ID
	engin.Use(controller.SetRequestId)
	// 设置跨域
	engin.Use(controller.CorsConfig())

	api := engin.Group("/api/v1")

	// 用户
	user := api.Group("/user")
	{
		user.POST("/login", controller.Login)
		user.POST("/register", controller.Register)
		user.POST("/send-verification-code", controller.SendVerificationCode)
		user.POST("/reset-password", controller.ResetPassword)
	}

	system := api.Group("/system")
	{
		system.GET("/config", controller.GetSysconfig)
		system.POST("/config", controller.SaveSysconfig)
	}

	note_topic := api.Group("/note_topic", controller.MiddlewareToken)
	{
		note_topic.POST("/create", controller.CreatedNoteTopic)
		note_topic.GET("/list", controller.ListNoteTopic)
	}

	note := api.Group("/note", controller.MiddlewareToken)
	{
		note.POST("/create", controller.CreatedNote)
		note.GET("/list", controller.ListNote)
		note.DELETE("/delete", controller.DeleteNote)
	}

	// 文件上传
	upload := api.Group("/upload", controller.MiddlewareToken)
	{
		upload.POST("/image", controller.UploadImage)
		upload.POST("/image-by-url", controller.UploadImageByUrl)
	}

	// 添加静态文件服务
	engin.Static("/uploads", "./uploads")

	return engin.Run(":8000")
}
