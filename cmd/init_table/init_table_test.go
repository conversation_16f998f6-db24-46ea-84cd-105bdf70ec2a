package main

import (
	"chat_note_api/constant"
	"chat_note_api/pkg/db"
	"fmt"
	"testing"

	"github.com/aliyun/aliyun-tablestore-go-sdk/tablestore"
	"github.com/joho/godotenv"
	"github.com/stretchr/testify/assert"
)

func TestCreateUserTable(t *testing.T) {
	err := godotenv.Load("../../.env")
	assert.Nil(t, err)
	client, err := db.InitDBManager()
	assert.Nil(t, err)

	createTableRequest := new(tablestore.CreateTableRequest)

	tableMeta := new(tablestore.TableMeta)
	//设置数据表名称。
	tableMeta.TableName = constant.USER_TABLE_NAME
	//为数据表添加主键列。主键为pk1（String类型）和pk2（Integer类型），其中pk1主键列为分区键，pk2主键列为自增列。
	tableMeta.AddPrimaryKeyColumn("id", tablestore.PrimaryKeyType_STRING)

	tableMeta.AddDefinedColumn("email", tablestore.DefinedColumn_STRING)
	tableMeta.AddDefinedColumn("nick_name", tablestore.DefinedColumn_STRING)
	tableMeta.AddDefinedColumn("avatar", tablestore.DefinedColumn_STRING)
	tableMeta.AddDefinedColumn("password", tablestore.DefinedColumn_STRING)
	tableMeta.AddDefinedColumn("vip_expire_time", tablestore.DefinedColumn_INTEGER)
	tableMeta.AddDefinedColumn("updated_time", tablestore.DefinedColumn_INTEGER)
	tableMeta.AddDefinedColumn("created_time", tablestore.DefinedColumn_INTEGER)
	tableMeta.AddDefinedColumn("delete_time", tablestore.DefinedColumn_INTEGER)

	//设置数据表选项。
	tableOption := new(tablestore.TableOption)
	//数据的过期时间，-1表示永不过期。
	tableOption.TimeToAlive = -1
	//最大版本数，属性列值最多保留1个版本，即保存最新的版本。
	tableOption.MaxVersion = 1
	//有效版本偏差，即写入数据的时间戳与系统当前时间的偏差允许最大值为86400秒（1天）。
	tableOption.DeviationCellVersionInSec = 60

	//设置预留读写吞吐量，容量型实例下的数据表只能设置为0，高性能型实例下的数据表可以设置为非零值。
	reservedThroughput := new(tablestore.ReservedThroughput)
	reservedThroughput.Readcap = 0
	reservedThroughput.Writecap = 0

	createTableRequest.TableMeta = tableMeta
	createTableRequest.TableOption = tableOption

	createTableRequest.ReservedThroughput = reservedThroughput
	_, err = client.CreateTable(createTableRequest)
	if err != nil {
		fmt.Println("Failed to create table with error:", err)
	} else {
		fmt.Println("Create table finished")
	}

	// 创建索引结构
	indexSchema := &tablestore.IndexSchema{}

	// 添加 email 字段到索引
	emailFieldName := "email"
	indexEnabled := true
	sortEnabled := false
	emailField := &tablestore.FieldSchema{
		FieldName:        &emailFieldName,
		FieldType:        tablestore.FieldType_KEYWORD, // 使用 KEYWORD 类型进行精确匹配
		Index:            &indexEnabled,
		EnableSortAndAgg: &sortEnabled,
	}
	indexSchema.FieldSchemas = append(indexSchema.FieldSchemas, emailField)

	// 创建搜索索引请求
	// createSearchIndexRequest := &tablestore.CreateSearchIndexRequest{
	// 	TableName:   constant.USER_TABLE_NAME,
	// 	IndexName:   constant.USER_TABLE_EMAIL_INDEX,
	// 	IndexSchema: indexSchema,
	// }

	// _, err = client.CreateSearchIndex(createSearchIndexRequest)
	// if err != nil {
	// 	fmt.Println("Failed to create search index with error:", err)
	// } else {
	// 	fmt.Println("Create search index finished")
	// }
}

func TestCreateVerificationCode(t *testing.T) {
	err := godotenv.Load("../../.env")
	assert.Nil(t, err)
	client, err := db.InitDBManager()
	assert.Nil(t, err)

	createTableRequest := new(tablestore.CreateTableRequest)

	tableMeta := new(tablestore.TableMeta)
	//设置数据表名称。
	tableMeta.TableName = constant.VERIFICATION_CODE_TABLE_NAME
	//为数据表添加主键列。主键为pk1（String类型）和pk2（Integer类型），其中pk1主键列为分区键，pk2主键列为自增列。
	tableMeta.AddPrimaryKeyColumn("id", tablestore.PrimaryKeyType_STRING)
	tableMeta.AddPrimaryKeyColumn("type", tablestore.PrimaryKeyType_STRING)

	tableMeta.AddDefinedColumn("email", tablestore.DefinedColumn_STRING)
	tableMeta.AddDefinedColumn("phone", tablestore.DefinedColumn_STRING)
	tableMeta.AddDefinedColumn("code", tablestore.DefinedColumn_STRING)
	tableMeta.AddDefinedColumn("updated_time", tablestore.DefinedColumn_INTEGER)
	tableMeta.AddDefinedColumn("created_time", tablestore.DefinedColumn_INTEGER)
	tableMeta.AddDefinedColumn("delete_time", tablestore.DefinedColumn_INTEGER)

	//设置数据表选项。
	tableOption := new(tablestore.TableOption)
	//数据的过期时间，-1表示永不过期。
	tableOption.TimeToAlive = -1
	//最大版本数，属性列值最多保留1个版本，即保存最新的版本。
	tableOption.MaxVersion = 1
	//有效版本偏差，即写入数据的时间戳与系统当前时间的偏差允许最大值为86400秒（1天）。
	tableOption.DeviationCellVersionInSec = 60

	//设置预留读写吞吐量，容量型实例下的数据表只能设置为0，高性能型实例下的数据表可以设置为非零值。
	reservedThroughput := new(tablestore.ReservedThroughput)
	reservedThroughput.Readcap = 0
	reservedThroughput.Writecap = 0

	createTableRequest.TableMeta = tableMeta
	createTableRequest.TableOption = tableOption

	createTableRequest.ReservedThroughput = reservedThroughput
	_, err = client.CreateTable(createTableRequest)
	if err != nil {
		fmt.Println("Failed to create table with error:", err)
	} else {
		fmt.Println("Create table finished")
	}
}

func TestCreateNoteTopicTable(t *testing.T) {
	err := godotenv.Load("../../.env")
	assert.Nil(t, err)
	client, err := db.InitDBManager()
	assert.Nil(t, err)

	createTableRequest := new(tablestore.CreateTableRequest)

	tableMeta := new(tablestore.TableMeta)
	//设置数据表名称。
	tableMeta.TableName = constant.NOTE_TOPIC_TABLE_NAME

	tableMeta.AddPrimaryKeyColumn("user_id", tablestore.PrimaryKeyType_STRING)
	tableMeta.AddPrimaryKeyColumnOption("id", tablestore.PrimaryKeyType_INTEGER, tablestore.AUTO_INCREMENT)
	tableMeta.AddDefinedColumn("topic_name", tablestore.DefinedColumn_STRING)
	tableMeta.AddDefinedColumn("description", tablestore.DefinedColumn_STRING)
	tableMeta.AddDefinedColumn("avatar", tablestore.DefinedColumn_STRING)
	tableMeta.AddDefinedColumn("updated_time", tablestore.DefinedColumn_INTEGER)
	tableMeta.AddDefinedColumn("created_time", tablestore.DefinedColumn_INTEGER)
	tableMeta.AddDefinedColumn("delete_time", tablestore.DefinedColumn_INTEGER)

	//设置数据表选项。
	tableOption := new(tablestore.TableOption)
	//数据的过期时间，-1表示永不过期。
	tableOption.TimeToAlive = -1
	//最大版本数，属性列值最多保留1个版本，即保存最新的版本。
	tableOption.MaxVersion = 1
	//有效版本偏差，即写入数据的时间戳与系统当前时间的偏差允许最大值为86400秒（1天）。
	tableOption.DeviationCellVersionInSec = 60

	//设置预留读写吞吐量，容量型实例下的数据表只能设置为0，高性能型实例下的数据表可以设置为非零值。
	reservedThroughput := new(tablestore.ReservedThroughput)
	reservedThroughput.Readcap = 0
	reservedThroughput.Writecap = 0

	createTableRequest.TableMeta = tableMeta
	createTableRequest.TableOption = tableOption

	createTableRequest.ReservedThroughput = reservedThroughput
	_, err = client.CreateTable(createTableRequest)
	if err != nil {
		fmt.Println("Failed to create table with error:", err)
	} else {
		fmt.Println("Create table finished")
	}

	// // 创建索引结构
	// indexSchema := &tablestore.IndexSchema{}

	// // 添加 email 字段到索引
	// userIdFieldName := "user_id"
	// indexEnabled := true
	// sortEnabled := false
	// userIdField := &tablestore.FieldSchema{
	// 	FieldName:        &userIdFieldName,
	// 	FieldType:        tablestore.FieldType_KEYWORD, // 使用 KEYWORD 类型进行精确匹配
	// 	Index:            &indexEnabled,
	// 	EnableSortAndAgg: &sortEnabled,
	// }
	// indexSchema.FieldSchemas = append(indexSchema.FieldSchemas, userIdField)

	// // 创建搜索索引请求
	// createSearchIndexRequest := &tablestore.CreateSearchIndexRequest{
	// 	TableName:   constant.NOTE_TOPIC_TABLE_NAME,
	// 	IndexName:   constant.NOTE_TOPIC_TABLE_USERID_INDEX,
	// 	IndexSchema: indexSchema,
	// }

	// _, err = client.CreateSearchIndex(createSearchIndexRequest)
	// if err != nil {
	// 	fmt.Println("Failed to create search index with error:", err)
	// } else {
	// 	fmt.Println("Create search index finished")
	// }
}

func TestCreateNotesTables(t *testing.T) {
	err := godotenv.Load("../../.env")
	assert.Nil(t, err)
	client, err := db.InitDBManager()
	assert.Nil(t, err)

	createTableRequest := new(tablestore.CreateTableRequest)

	tableMeta := new(tablestore.TableMeta)
	//设置数据表名称。
	tableMeta.TableName = constant.NOTE_TABLE_NAME

	tableMeta.AddPrimaryKeyColumn("user_id", tablestore.PrimaryKeyType_STRING)
	tableMeta.AddPrimaryKeyColumn("topic_id", tablestore.PrimaryKeyType_STRING)
	tableMeta.AddPrimaryKeyColumnOption("id", tablestore.PrimaryKeyType_INTEGER, tablestore.AUTO_INCREMENT)
	tableMeta.AddDefinedColumn("note_type", tablestore.DefinedColumn_STRING)
	tableMeta.AddDefinedColumn("content", tablestore.DefinedColumn_STRING)
	tableMeta.AddDefinedColumn("updated_time", tablestore.DefinedColumn_INTEGER)
	tableMeta.AddDefinedColumn("created_time", tablestore.DefinedColumn_INTEGER)
	tableMeta.AddDefinedColumn("delete_time", tablestore.DefinedColumn_INTEGER)

	//设置数据表选项。
	tableOption := new(tablestore.TableOption)
	//数据的过期时间，-1表示永不过期。
	tableOption.TimeToAlive = -1
	//最大版本数，属性列值最多保留1个版本，即保存最新的版本。
	tableOption.MaxVersion = 1
	//有效版本偏差，即写入数据的时间戳与系统当前时间的偏差允许最大值为86400秒（1天）。
	tableOption.DeviationCellVersionInSec = 60

	//设置预留读写吞吐量，容量型实例下的数据表只能设置为0，高性能型实例下的数据表可以设置为非零值。
	reservedThroughput := new(tablestore.ReservedThroughput)
	reservedThroughput.Readcap = 0
	reservedThroughput.Writecap = 0

	createTableRequest.TableMeta = tableMeta
	createTableRequest.TableOption = tableOption

	createTableRequest.ReservedThroughput = reservedThroughput
	_, err = client.CreateTable(createTableRequest)
	if err != nil {
		fmt.Println("Failed to create table with error:", err)
	} else {
		fmt.Println("Create table finished")
	}

}
