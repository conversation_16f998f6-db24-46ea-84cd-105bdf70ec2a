package main

// import (
// 	"chat_note_api/constant"
// 	"chat_note_api/pkg/db"
// 	"fmt"
// 	"log"
// 	"testing"

// 	"github.com/aliyun/aliyun-tablestore-go-sdk/tablestore"
// 	"github.com/joho/godotenv"
// )

// func TestSearchIndex(t *testing.T) {
// 	// 加载环境变量
// 	err := godotenv.Load("../../.env")
// 	if err != nil {
// 		log.Fatal("Error loading .env file:", err)
// 	}

// 	// 初始化数据库管理器
// 	client, err := db.InitDBManager()
// 	if err != nil {
// 		log.Fatal("Failed to initialize DB manager:", err)
// 	}

// 	fmt.Println("=== 创建 TableStore 搜索索引 ===")
// 	fmt.Printf("表名: %s\n", constant.USER_TABLE_NAME)
// 	fmt.Printf("索引名: %s\n", constant.USER_TABLE_EMAIL_INDEX)
// 	fmt.Println()

// 	// 检查索引是否已经存在
// 	fmt.Println("1. 检查索引是否已存在...")
// 	describeSearchIndexRequest := &tablestore.DescribeSearchIndexRequest{
// 		TableName: constant.USER_TABLE_NAME,
// 		IndexName: constant.USER_TABLE_EMAIL_INDEX,
// 	}

// 	_, err = client.DescribeSearchIndex(describeSearchIndexRequest)
// 	if err == nil {
// 		fmt.Printf("✅ 索引已存在: %s\n", constant.USER_TABLE_EMAIL_INDEX)
// 		return
// 	}
// 	fmt.Printf("ℹ️  索引不存在，开始创建...\n")

// 	// 创建搜索索引
// 	fmt.Println("2. 创建搜索索引...")

// 	// 创建索引结构
// 	indexSchema := &tablestore.IndexSchema{}

// 	// 添加 email 字段到索引
// 	emailFieldName := "email"
// 	indexEnabled := true
// 	sortEnabled := false
// 	emailField := &tablestore.FieldSchema{
// 		FieldName:        &emailFieldName,
// 		FieldType:        tablestore.FieldType_KEYWORD, // 使用 KEYWORD 类型进行精确匹配
// 		Index:            &indexEnabled,
// 		EnableSortAndAgg: &sortEnabled,
// 	}
// 	indexSchema.FieldSchemas = append(indexSchema.FieldSchemas, emailField)

// 	// 创建搜索索引请求
// 	createSearchIndexRequest := &tablestore.CreateSearchIndexRequest{
// 		TableName:   constant.USER_TABLE_NAME,
// 		IndexName:   constant.USER_TABLE_EMAIL_INDEX,
// 		IndexSchema: indexSchema,
// 	}

// 	// 执行创建
// 	_, err = client.CreateSearchIndex(createSearchIndexRequest)
// 	if err != nil {
// 		fmt.Printf("❌ 创建索引失败: %v\n", err)
// 		return
// 	}

// 	fmt.Printf("✅ 搜索索引创建成功: %s\n", constant.USER_TABLE_EMAIL_INDEX)
// 	fmt.Println()
// 	fmt.Println("注意: 索引创建后需要一些时间进行数据同步，请等待几分钟后再使用。")
// }
