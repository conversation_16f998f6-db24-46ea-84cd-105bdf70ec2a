package main

import (
	"chat_note_api/pkg/db"
	"testing"

	"github.com/aliyun/aliyun-tablestore-go-sdk/tablestore"
	"github.com/joho/godotenv"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestCheckTableData(t *testing.T) {
	// 加载环境变量
	err := godotenv.Load("../../.env")
	require.NoError(t, err, "应该能够加载环境变量")

	// 初始化数据库管理器
	client, err := db.InitDBManager()
	require.NoError(t, err, "应该能够初始化数据库管理器")

	t.Logf("=== 检查表中的原始数据 ===")

	t.Run("扫描表中的所有数据", func(t *testing.T) {
		// 创建范围查询请求
		rangeRowQueryCriteria := &tablestore.RangeRowQueryCriteria{
			TableName: "user",
		}

		// 设置主键范围 (从最小值到最大值)
		startPK := new(tablestore.PrimaryKey)
		startPK.AddPrimaryKeyColumnWithMinValue("id")
		endPK := new(tablestore.PrimaryKey)
		endPK.AddPrimaryKeyColumnWithMaxValue("id")

		rangeRowQueryCriteria.StartPrimaryKey = startPK
		rangeRowQueryCriteria.EndPrimaryKey = endPK
		rangeRowQueryCriteria.Direction = tablestore.FORWARD
		rangeRowQueryCriteria.MaxVersion = 1
		rangeRowQueryCriteria.Limit = 10 // 限制返回10行

		getRangeRequest := &tablestore.GetRangeRequest{
			RangeRowQueryCriteria: rangeRowQueryCriteria,
		}

		getRangeResp, err := client.GetRange(getRangeRequest)
		require.NoError(t, err, "应该能够扫描表数据")

		t.Logf("表中数据统计: 返回行数: %d", len(getRangeResp.Rows))
		assert.Greater(t, len(getRangeResp.Rows), 0, "表中应该有数据")

		if len(getRangeResp.Rows) > 0 {
			t.Logf("✅ 表中有数据！详细信息:")
			for i, row := range getRangeResp.Rows {
				t.Logf("  行 %d:", i+1)
				t.Logf("    主键: %+v", row.PrimaryKey.PrimaryKeys)
				t.Logf("    属性列:")
				for _, col := range row.Columns {
					t.Logf("      %s: %v", col.ColumnName, col.Value)
				}
			}
		}
	})

	t.Run("查找特定邮箱数据", func(t *testing.T) {
		// 重新获取数据进行邮箱检查
		rangeRowQueryCriteria := &tablestore.RangeRowQueryCriteria{
			TableName: "user",
		}

		startPK := new(tablestore.PrimaryKey)
		startPK.AddPrimaryKeyColumnWithMinValue("id")
		endPK := new(tablestore.PrimaryKey)
		endPK.AddPrimaryKeyColumnWithMaxValue("id")

		rangeRowQueryCriteria.StartPrimaryKey = startPK
		rangeRowQueryCriteria.EndPrimaryKey = endPK
		rangeRowQueryCriteria.Direction = tablestore.FORWARD
		rangeRowQueryCriteria.MaxVersion = 1
		rangeRowQueryCriteria.Limit = 10

		getRangeRequest := &tablestore.GetRangeRequest{
			RangeRowQueryCriteria: rangeRowQueryCriteria,
		}

		getRangeResp, err := client.GetRange(getRangeRequest)
		require.NoError(t, err, "应该能够扫描表数据")

		// 检查是否有 <EMAIL> 的数据
		found := false
		var foundRow *tablestore.Row
		for _, row := range getRangeResp.Rows {
			for _, col := range row.Columns {
				if col.ColumnName == "email" && col.Value == "<EMAIL>" {
					found = true
					foundRow = row
					break
				}
			}
			if found {
				break
			}
		}

		assert.True(t, found, "应该能找到 <EMAIL> 的数据")
		if found {
			t.Logf("✅ 找到 <EMAIL> 的数据:")
			t.Logf("    主键: %+v", foundRow.PrimaryKey.PrimaryKeys)
		} else {
			t.Logf("❌ 未找到 <EMAIL> 的数据")
			t.Logf("   表中的邮箱数据:")
			for _, row := range getRangeResp.Rows {
				for _, col := range row.Columns {
					if col.ColumnName == "email" {
						t.Logf("     - %v", col.Value)
					}
				}
			}
		}
	})

	t.Logf("=== 检查完成 ===")
}
