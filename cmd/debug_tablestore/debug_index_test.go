package main

// import (
// 	"chat_note_api/constant"
// 	"chat_note_api/pkg/db"
// 	"context"
// 	"testing"

// 	"github.com/aliyun/aliyun-tablestore-go-sdk/tablestore"
// 	"github.com/joho/godotenv"
// 	"github.com/stretchr/testify/assert"
// 	"github.com/stretchr/testify/require"
// )

// func TestDebugIndex(t *testing.T) {
// 	// 加载环境变量
// 	err := godotenv.Load("../../.env")
// 	require.NoError(t, err, "应该能够加载环境变量")

// 	// 初始化数据库管理器
// 	client, err := db.InitDBManager()
// 	require.NoError(t, err, "应该能够初始化数据库管理器")

// 	ctx := context.Background()

// 	t.Logf("=== TableStore 索引诊断工具 ===")
// 	t.Logf("表名: %s", constant.USER_TABLE_NAME)
// 	t.Logf("索引名: %s", constant.USER_TABLE_EMAIL_INDEX)

// 	t.Run("检查表是否存在", func(t *testing.T) {
// 		describeTableRequest := &tablestore.DescribeTableRequest{
// 			TableName: constant.USER_TABLE_NAME,
// 		}

// 		_, err := client.DescribeTable(describeTableRequest)
// 		assert.NoError(t, err, "表应该存在且可访问")
// 		t.Logf("✅ 表存在: %s", constant.USER_TABLE_NAME)
// 	})

// 	t.Run("检查索引是否存在", func(t *testing.T) {
// 		describeSearchIndexRequest := &tablestore.DescribeSearchIndexRequest{
// 			TableName: constant.USER_TABLE_NAME,
// 			IndexName: constant.USER_TABLE_EMAIL_INDEX,
// 		}

// 		_, err := client.DescribeSearchIndex(describeSearchIndexRequest)
// 		assert.NoError(t, err, "索引应该存在且可访问")
// 		t.Logf("✅ 索引存在: %s", constant.USER_TABLE_EMAIL_INDEX)
// 	})

// 	t.Run("测试搜索功能", func(t *testing.T) {
// 		err := db.UserManager.CheckIndexExists(ctx, constant.USER_TABLE_NAME, constant.USER_TABLE_EMAIL_INDEX)
// 		assert.NoError(t, err, "索引检查应该通过")
// 		t.Logf("✅ 索引检查通过")
// 	})

// 	t.Logf("=== 诊断完成 ===")
// }
