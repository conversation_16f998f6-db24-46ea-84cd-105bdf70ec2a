package main

import (
	"chat_note_api/pkg/db"
	"testing"
	"time"

	"github.com/aliyun/aliyun-tablestore-go-sdk/tablestore"
	"github.com/aliyun/aliyun-tablestore-go-sdk/tablestore/search"
	"github.com/joho/godotenv"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestReindexData(t *testing.T) {
	// 加载环境变量
	err := godotenv.Load("../../.env")
	require.NoError(t, err, "应该能够加载环境变量")

	// 初始化数据库管理器
	client, err := db.InitDBManager()
	require.NoError(t, err, "应该能够初始化数据库管理器")

	t.Logf("=== 重新索引现有数据 ===")

	t.Run("读取现有数据", func(t *testing.T) {
		t.Logf("读取现有的 <EMAIL> 数据...")

		getRowPK := new(tablestore.PrimaryKey)
		getRowPK.AddPrimaryKeyColumn("id", "1")

		criteria := new(tablestore.SingleRowQueryCriteria)
		criteria.PrimaryKey = getRowPK
		criteria.TableName = "user"
		criteria.MaxVersion = 1

		getReq := new(tablestore.GetRowRequest)
		getReq.SingleRowQueryCriteria = criteria

		getResp, err := client.GetRow(getReq)
		require.NoError(t, err, "应该能够读取数据")
		assert.Greater(t, len(getResp.Columns), 0, "应该有属性列数据")

		t.Logf("✅ 读取到数据，属性列数: %d", len(getResp.Columns))
	})

	t.Run("更新数据以触发索引", func(t *testing.T) {
		t.Logf("更新数据以触发索引...")

		// 先读取现有数据
		getRowPK := new(tablestore.PrimaryKey)
		getRowPK.AddPrimaryKeyColumn("id", "1")

		criteria := new(tablestore.SingleRowQueryCriteria)
		criteria.PrimaryKey = getRowPK
		criteria.TableName = "user"
		criteria.MaxVersion = 1

		getReq := new(tablestore.GetRowRequest)
		getReq.SingleRowQueryCriteria = criteria

		getResp, err := client.GetRow(getReq)
		require.NoError(t, err, "应该能够读取数据")

		// 更新数据
		updateRowPK := new(tablestore.PrimaryKey)
		updateRowPK.AddPrimaryKeyColumn("id", "1")

		updateRowChange := new(tablestore.UpdateRowChange)
		updateRowChange.TableName = "user"
		updateRowChange.PrimaryKey = updateRowPK

		// 复制所有现有的列数据
		for _, col := range getResp.Columns {
			updateRowChange.PutColumn(col.ColumnName, col.Value)
		}

		// 更新 updated_time 以确保有变化
		updateRowChange.PutColumn("updated_time", time.Now().Unix())

		updateRowChange.SetCondition(tablestore.RowExistenceExpectation_EXPECT_EXIST)

		updateRequest := new(tablestore.UpdateRowRequest)
		updateRequest.UpdateRowChange = updateRowChange

		_, err = client.UpdateRow(updateRequest)
		assert.NoError(t, err, "应该能够更新数据")

		t.Logf("✅ 数据更新成功")
	})

	t.Run("等待索引同步", func(t *testing.T) {
		t.Logf("等待索引同步...")
		time.Sleep(3 * time.Second) // 减少等待时间用于测试
		t.Logf("✅ 等待完成")
	})

	t.Run("测试搜索功能", func(t *testing.T) {
		t.Logf("测试搜索功能...")

		searchRequest := &tablestore.SearchRequest{
			TableName: "user",
			IndexName: "user_email_index",
			SearchQuery: search.NewSearchQuery().
				SetQuery(&search.TermQuery{
					FieldName: "email",
					Term:      "<EMAIL>",
				}).
				SetLimit(10),
		}

		res, err := client.Search(searchRequest)
		require.NoError(t, err, "搜索应该成功")

		t.Logf("搜索结果:")
		t.Logf("  - 总数: %d", res.TotalCount)
		t.Logf("  - 返回行数: %d", len(res.Rows))

		// 检查是否有返回的行数据
		if len(res.Rows) > 0 {
			t.Logf("✅ 搜索成功！找到数据")
			for i, row := range res.Rows {
				t.Logf("  行 %d:", i+1)
				t.Logf("    主键: %+v", row.PrimaryKey.PrimaryKeys)
				t.Logf("    属性列:")
				for _, col := range row.Columns {
					t.Logf("      %s: %v", col.ColumnName, col.Value)
				}
			}
			assert.Greater(t, len(res.Rows), 0, "应该找到数据")
		} else if res.TotalCount > 0 {
			t.Logf("⚠️  有匹配数据但未返回行，TotalCount: %d", res.TotalCount)
		} else {
			t.Logf("❌ 搜索仍然失败，可能需要更多时间同步")
			t.Logf("   建议等待几分钟后再次测试")
		}
	})

	t.Logf("=== 重新索引完成 ===")
}
