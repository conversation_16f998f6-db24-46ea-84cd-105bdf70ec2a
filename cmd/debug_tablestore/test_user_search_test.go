package main

import (
	"chat_note_api/pkg/db"
	"context"
	"fmt"
	"log"
	"testing"

	"github.com/joho/godotenv"
)

func TestUserSearch(t *testing.T) {
	// 加载环境变量
	err := godotenv.Load("../../.env")
	if err != nil {
		log.Fatal("Error loading .env file:", err)
	}

	// 初始化数据库管理器
	_, err = db.InitDBManager()
	if err != nil {
		log.Fatal("Failed to initialize DB manager:", err)
	}

	ctx := context.Background()

	fmt.Println("=== 测试用户邮箱搜索功能 ===")

	// 测试搜索一个不存在的邮箱
	testEmail := "<EMAIL>"
	fmt.Printf("搜索邮箱: %s\n", testEmail)

	user, err := db.UserManager.GetUserByEmail(ctx, testEmail)
	if err != nil {
		fmt.Printf("搜索结果: 出现错误 - %v\n", err)
	} else if user == nil {
		fmt.Printf("搜索结果: 未找到用户\n")
	} else {
		fmt.Printf("搜索结果: 找到用户 - %+v\n", user)
	}

	fmt.Println("\n✅ 搜索功能测试完成！")
	fmt.Println("如果没有错误信息，说明索引已经可以正常使用了。")
}
