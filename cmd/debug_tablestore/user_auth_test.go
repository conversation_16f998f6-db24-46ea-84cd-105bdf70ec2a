package main

import (
	"chat_note_api/pkg/db"
	"chat_note_api/pkg/service"
	"context"
	"testing"

	"github.com/joho/godotenv"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestUserAuthFlow(t *testing.T) {
	// 加载环境变量
	err := godotenv.Load("../../.env")
	require.NoError(t, err, "应该能够加载环境变量")

	// 初始化数据库管理器
	_, err = db.InitDBManager()
	require.NoError(t, err, "应该能够初始化数据库管理器")

	ctx := context.Background()

	t.Logf("=== 测试用户认证流程 ===")

	testEmail := "<EMAIL>"

	t.Run("通过邮箱获取用户", func(t *testing.T) {
		t.Logf("通过邮箱获取用户: %s", testEmail)

		user, err := service.UserService.GetUserByEmail(ctx, testEmail)
		assert.NoError(t, err, "获取用户应该成功")
		assert.NotNil(t, user, "用户应该存在")

		if user != nil {
			t.Logf("✅ 成功获取用户信息:")
			t.Logf("   ID: %d", user.Id)
			t.Logf("   邮箱: %s", user.Email)
			t.Logf("   昵称: %s", user.NickName)
			t.Logf("   头像: %s", user.Avatar)
			t.Logf("   VIP到期时间: %d", user.VipExpireTime)
			t.Logf("   创建时间: %d", user.CreatedTime)
			t.Logf("   更新时间: %d", user.UpdatedTime)

			// 验证关键字段
			assert.Equal(t, testEmail, user.Email, "邮箱应该匹配")
			assert.Greater(t, user.Id, int64(0), "用户ID应该大于0")
		}
	})

	t.Run("测试不存在的用户", func(t *testing.T) {
		nonExistentEmail := "<EMAIL>"
		t.Logf("测试不存在的邮箱: %s", nonExistentEmail)

		user, err := service.UserService.GetUserByEmail(ctx, nonExistentEmail)
		assert.Error(t, err, "不存在的用户应该返回错误")
		assert.Nil(t, user, "不存在的用户应该返回nil")

		t.Logf("✅ 正确处理了不存在的用户")
	})

	t.Run("测试空邮箱", func(t *testing.T) {
		emptyEmail := ""
		t.Logf("测试空邮箱: '%s'", emptyEmail)

		user, err := service.UserService.GetUserByEmail(ctx, emptyEmail)
		assert.Error(t, err, "空邮箱应该返回错误")
		assert.Nil(t, user, "空邮箱应该返回nil")

		t.Logf("✅ 正确处理了空邮箱")
	})

	t.Logf("✅ 用户认证流程测试完成！")
	t.Logf("现在可以正常使用登录功能了。")
}
