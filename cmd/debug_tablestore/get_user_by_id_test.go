package main

import (
	"chat_note_api/pkg/db"
	"context"
	"testing"

	"github.com/joho/godotenv"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestGetUserById(t *testing.T) {
	// 加载环境变量
	err := godotenv.Load("../../.env")
	require.NoError(t, err, "应该能够加载环境变量")

	// 初始化数据库管理器
	_, err = db.InitDBManager()
	require.NoError(t, err, "应该能够初始化数据库管理器")

	ctx := context.Background()

	t.Logf("=== 测试 GetUserById 方法 ===")

	t.Run("通过字符串ID获取用户", func(t *testing.T) {
		userId := "1"
		t.Logf("通过字符串ID获取用户: %s", userId)

		user, err := db.UserManager.GetUserById(ctx, userId)
		assert.NoError(t, err, "应该能够通过字符串ID获取用户")
		assert.NotNil(t, user, "用户应该存在")

		if user != nil {
			t.Logf("✅ 成功获取用户信息:")
			t.Logf("   ID: %d", user.Id)
			t.Logf("   邮箱: %s", user.Email)
			t.Logf("   昵称: %s", user.NickName)
			t.Logf("   头像: %s", user.Avatar)
			t.Logf("   VIP到期时间: %d", user.VipExpireTime)
			t.Logf("   创建时间: %d", user.CreatedTime)
			t.Logf("   更新时间: %d", user.UpdatedTime)

			// 验证关键字段
			assert.Equal(t, "<EMAIL>", user.Email, "邮箱应该匹配")
			assert.Equal(t, int64(1), user.Id, "用户ID应该为1")
		}
	})

	t.Run("通过整数ID获取用户", func(t *testing.T) {
		userId := "1"
		t.Logf("通过整数ID获取用户: %d", userId)

		user, err := db.UserManager.GetUserById(ctx, userId)
		assert.NoError(t, err, "应该能够通过整数ID获取用户")
		assert.NotNil(t, user, "用户应该存在")

		if user != nil {
			t.Logf("✅ 成功获取用户信息:")
			t.Logf("   ID: %d", user.Id)
			t.Logf("   邮箱: %s", user.Email)

			// 验证关键字段
			assert.Equal(t, "<EMAIL>", user.Email, "邮箱应该匹配")
			assert.Equal(t, int64(1), user.Id, "用户ID应该为1")
		}
	})

	t.Run("测试不存在的用户ID", func(t *testing.T) {
		nonExistentId := "999"
		t.Logf("测试不存在的用户ID: %s", nonExistentId)

		user, err := db.UserManager.GetUserById(ctx, nonExistentId)
		assert.Error(t, err, "不存在的用户ID应该返回错误")
		assert.Nil(t, user, "不存在的用户应该返回nil")

		t.Logf("✅ 正确处理了不存在的用户ID")
	})

	t.Run("对比GetUserById和GetUserByEmail结果", func(t *testing.T) {
		userId := "1"
		userEmail := "<EMAIL>"

		// 通过ID获取用户
		userById, err1 := db.UserManager.GetUserById(ctx, userId)
		require.NoError(t, err1, "通过ID获取用户应该成功")

		// 通过邮箱获取用户
		userByEmail, err2 := db.UserManager.GetUserByEmail(ctx, userEmail)
		require.NoError(t, err2, "通过邮箱获取用户应该成功")

		// 比较两个结果
		assert.Equal(t, userById.Id, userByEmail.Id, "ID应该相同")
		assert.Equal(t, userById.Email, userByEmail.Email, "邮箱应该相同")
		assert.Equal(t, userById.NickName, userByEmail.NickName, "昵称应该相同")
		assert.Equal(t, userById.Avatar, userByEmail.Avatar, "头像应该相同")
		assert.Equal(t, userById.VipExpireTime, userByEmail.VipExpireTime, "VIP到期时间应该相同")

		t.Logf("✅ GetUserById 和 GetUserByEmail 返回相同的用户数据")
	})

	t.Logf("=== GetUserById 测试完成 ===")
}
