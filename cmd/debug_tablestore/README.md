# TableStore 调试测试套件

这个目录包含了用于调试和测试 TableStore 功能的单元测试。

## 测试文件说明

### 核心测试文件

1. **`debug_index_test.go`** - 索引诊断测试
   - 检查表是否存在
   - 检查搜索索引是否存在
   - 测试索引功能

2. **`check_table_data_test.go`** - 表数据检查测试
   - 扫描表中的所有数据
   - 查找特定邮箱数据
   - 验证数据完整性

3. **`create_search_index_unit_test.go`** - 搜索索引创建测试
   - 检查索引是否已存在
   - 创建新的搜索索引
   - 验证索引创建成功

4. **`search_functionality_test.go`** - 搜索功能测试
   - 直接搜索测试
   - 检查索引中的数据
   - 使用 UserManager 测试

5. **`user_auth_test.go`** - 用户认证流程测试
   - 通过邮箱获取用户
   - 测试不存在的用户
   - 测试空邮箱处理

6. **`reindex_data_test.go`** - 重新索引数据测试
   - 读取现有数据
   - 更新数据以触发索引
   - 测试搜索功能

7. **`tablestore_test_suite.go`** - 测试套件
   - 完整的测试流程
   - 集成所有测试

### 旧版本测试文件（保留）

- `create_search_index_test.go` - 旧版本的索引创建测试
- `test_user_search_test.go` - 旧版本的用户搜索测试

## 运行测试

### 运行所有测试

```bash
# 在项目根目录下运行
go test ./cmd/debug_tablestore/... -v
```

### 运行特定测试

```bash
# 运行索引诊断测试
go test ./cmd/debug_tablestore/ -run TestDebugIndex -v

# 运行表数据检查测试
go test ./cmd/debug_tablestore/ -run TestCheckTableData -v

# 运行搜索索引创建测试
go test ./cmd/debug_tablestore/ -run TestCreateSearchIndex -v

# 运行搜索功能测试
go test ./cmd/debug_tablestore/ -run TestSearchFunctionality -v

# 运行用户认证测试
go test ./cmd/debug_tablestore/ -run TestUserAuthFlow -v

# 运行重新索引测试
go test ./cmd/debug_tablestore/ -run TestReindexData -v

# 运行完整测试套件
go test ./cmd/debug_tablestore/ -run TestTableStoreTestSuite -v
```

### 运行测试并生成详细报告

```bash
# 生成测试覆盖率报告
go test ./cmd/debug_tablestore/... -v -coverprofile=coverage.out

# 查看覆盖率报告
go tool cover -html=coverage.out
```

## 测试顺序建议

建议按以下顺序运行测试：

1. **`TestDebugIndex`** - 首先检查基础设施是否正常
2. **`TestCheckTableData`** - 验证表中是否有数据
3. **`TestCreateSearchIndex`** - 确保搜索索引存在
4. **`TestReindexData`** - 重新索引现有数据
5. **`TestSearchFunctionality`** - 测试搜索功能
6. **`TestUserAuthFlow`** - 测试完整的用户认证流程

## 环境要求

- 确保 `.env` 文件存在且配置正确
- TableStore 实例正常运行
- 网络连接正常
- 必要的权限配置

## 故障排除

### 常见问题

1. **索引不存在错误**
   - 运行 `TestCreateSearchIndex` 创建索引

2. **搜索结果为空**
   - 运行 `TestReindexData` 重新索引数据
   - 等待几分钟让索引同步

3. **环境变量加载失败**
   - 检查 `.env` 文件路径
   - 确保环境变量配置正确

4. **权限错误**
   - 检查 TableStore 访问权限
   - 验证 AccessKey 配置

### 调试技巧

- 使用 `-v` 参数查看详细日志
- 检查测试输出中的错误信息
- 逐个运行测试以定位问题
- 查看 TableStore 控制台确认资源状态

## 测试数据

测试使用的示例数据：
- 邮箱: `<EMAIL>`
- 用户ID: `1`
- 表名: `user`
- 索引名: `user_email_index`

## 注意事项

- 测试会修改数据库中的数据（更新时间戳）
- 某些测试需要等待索引同步，可能需要几分钟时间
- 建议在开发环境中运行这些测试
- 生产环境请谨慎使用
