package main

// import (
// 	"chat_note_api/constant"
// 	"chat_note_api/pkg/db"
// 	"testing"

// 	"github.com/aliyun/aliyun-tablestore-go-sdk/tablestore"
// 	"github.com/joho/godotenv"
// 	"github.com/stretchr/testify/assert"
// 	"github.com/stretchr/testify/require"
// )

// func TestCreateSearchIndex(t *testing.T) {
// 	// 加载环境变量
// 	err := godotenv.Load("../../.env")
// 	require.NoError(t, err, "应该能够加载环境变量")

// 	// 初始化数据库管理器
// 	client, err := db.InitDBManager()
// 	require.NoError(t, err, "应该能够初始化数据库管理器")

// 	t.Logf("=== 创建 TableStore 搜索索引 ===")
// 	t.Logf("表名: %s", constant.USER_TABLE_NAME)
// 	t.Logf("索引名: %s", constant.USER_TABLE_EMAIL_INDEX)

// 	t.Run("检查索引是否已存在", func(t *testing.T) {
// 		describeSearchIndexRequest := &tablestore.DescribeSearchIndexRequest{
// 			TableName: constant.USER_TABLE_NAME,
// 			IndexName: constant.USER_TABLE_EMAIL_INDEX,
// 		}

// 		_, err := client.DescribeSearchIndex(describeSearchIndexRequest)
// 		if err == nil {
// 			t.Logf("✅ 索引已存在: %s", constant.USER_TABLE_EMAIL_INDEX)
// 			t.Skip("索引已存在，跳过创建")
// 		}
// 		t.Logf("ℹ️  索引不存在，需要创建")
// 	})

// 	t.Run("创建搜索索引", func(t *testing.T) {
// 		// 先检查是否已存在
// 		describeSearchIndexRequest := &tablestore.DescribeSearchIndexRequest{
// 			TableName: constant.USER_TABLE_NAME,
// 			IndexName: constant.USER_TABLE_EMAIL_INDEX,
// 		}

// 		_, err := client.DescribeSearchIndex(describeSearchIndexRequest)
// 		if err == nil {
// 			t.Skip("索引已存在，跳过创建")
// 		}

// 		// 创建索引结构
// 		indexSchema := &tablestore.IndexSchema{}

// 		// 添加 email 字段到索引
// 		emailFieldName := "email"
// 		indexEnabled := true
// 		sortEnabled := false
// 		emailField := &tablestore.FieldSchema{
// 			FieldName:        &emailFieldName,
// 			FieldType:        tablestore.FieldType_KEYWORD, // 使用 KEYWORD 类型进行精确匹配
// 			Index:            &indexEnabled,
// 			EnableSortAndAgg: &sortEnabled,
// 		}
// 		indexSchema.FieldSchemas = append(indexSchema.FieldSchemas, emailField)

// 		// 创建搜索索引请求
// 		createSearchIndexRequest := &tablestore.CreateSearchIndexRequest{
// 			TableName:   constant.USER_TABLE_NAME,
// 			IndexName:   constant.USER_TABLE_EMAIL_INDEX,
// 			IndexSchema: indexSchema,
// 		}

// 		// 执行创建
// 		_, err = client.CreateSearchIndex(createSearchIndexRequest)
// 		assert.NoError(t, err, "应该能够成功创建搜索索引")

// 		if err == nil {
// 			t.Logf("✅ 搜索索引创建成功: %s", constant.USER_TABLE_EMAIL_INDEX)
// 			t.Logf("注意: 索引创建后需要一些时间进行数据同步，请等待几分钟后再使用。")
// 		}
// 	})

// 	t.Run("验证索引创建成功", func(t *testing.T) {
// 		describeSearchIndexRequest := &tablestore.DescribeSearchIndexRequest{
// 			TableName: constant.USER_TABLE_NAME,
// 			IndexName: constant.USER_TABLE_EMAIL_INDEX,
// 		}

// 		_, err := client.DescribeSearchIndex(describeSearchIndexRequest)
// 		assert.NoError(t, err, "创建后应该能够描述索引")
// 		t.Logf("✅ 索引验证成功")
// 	})
// }
