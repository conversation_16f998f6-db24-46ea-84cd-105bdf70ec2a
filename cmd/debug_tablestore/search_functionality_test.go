package main

import (
	"chat_note_api/pkg/db"
	"context"
	"testing"

	"github.com/aliyun/aliyun-tablestore-go-sdk/tablestore"
	"github.com/aliyun/aliyun-tablestore-go-sdk/tablestore/search"
	"github.com/joho/godotenv"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestSearchFunctionality(t *testing.T) {
	// 加载环境变量
	err := godotenv.Load("../../.env")
	require.NoError(t, err, "应该能够加载环境变量")

	// 初始化数据库管理器
	client, err := db.InitDBManager()
	require.NoError(t, err, "应该能够初始化数据库管理器")

	ctx := context.Background()

	t.Logf("=== 测试用户邮箱搜索功能 ===")

	testEmail := "<EMAIL>"
	t.Logf("搜索邮箱: %s", testEmail)

	t.Run("直接搜索测试", func(t *testing.T) {
		searchRequest := &tablestore.SearchRequest{
			TableName: "user",
			IndexName: "user_email_index",
			SearchQuery: search.NewSearchQuery().
				SetQuery(&search.TermQuery{
					FieldName: "email",
					Term:      testEmail,
				}).
				SetLimit(10),
		}

		res, err := client.Search(searchRequest)
		require.NoError(t, err, "搜索请求应该成功")

		t.Logf("搜索结果统计:")
		t.Logf("  - 总数: %d", res.TotalCount)
		t.Logf("  - 返回行数: %d", len(res.Rows))

		assert.Greater(t, len(res.Rows), 0, "应该找到至少一条数据")

		if len(res.Rows) > 0 {
			t.Logf("✅ 找到数据！详细信息:")
			for i, row := range res.Rows {
				t.Logf("  行 %d:", i+1)
				t.Logf("    主键: %+v", row.PrimaryKey.PrimaryKeys)
				t.Logf("    属性列:")
				for _, col := range row.Columns {
					t.Logf("      %s: %v", col.ColumnName, col.Value)
				}
			}
		}
	})

	t.Run("检查索引中的数据", func(t *testing.T) {
		// 使用 MatchAllQuery 来查看索引中是否有任何数据
		allDataRequest := &tablestore.SearchRequest{
			TableName: "user",
			IndexName: "user_email_index",
			SearchQuery: search.NewSearchQuery().
				SetQuery(&search.MatchAllQuery{}).
				SetLimit(5),
		}

		allRes, err := client.Search(allDataRequest)
		require.NoError(t, err, "查询所有数据应该成功")

		t.Logf("索引中总数据量: %d", allRes.TotalCount)
		assert.Greater(t, len(allRes.Rows), 0, "索引中应该有数据")

		if len(allRes.Rows) > 0 {
			t.Logf("索引中的数据示例:")
			for i, row := range allRes.Rows {
				t.Logf("  行 %d: 主键=%+v", i+1, row.PrimaryKey.PrimaryKeys)
				for _, col := range row.Columns {
					t.Logf("    %s: %v", col.ColumnName, col.Value)
				}
			}
		}
	})

	t.Run("使用UserManager测试", func(t *testing.T) {
		user, err := db.UserManager.GetUserByEmail(ctx, testEmail)
		assert.NoError(t, err, "UserManager搜索应该成功")
		assert.NotNil(t, user, "应该返回用户对象")

		if user != nil {
			t.Logf("✅ UserManager 找到用户:")
			t.Logf("  ID: %d", user.Id)
			t.Logf("  邮箱: %s", user.Email)
			t.Logf("  昵称: %s", user.NickName)
			t.Logf("  头像: %s", user.Avatar)
			t.Logf("  VIP到期时间: %d", user.VipExpireTime)
			t.Logf("  创建时间: %d", user.CreatedTime)
			t.Logf("  更新时间: %d", user.UpdatedTime)

			assert.Equal(t, testEmail, user.Email, "返回的邮箱应该匹配")
		}
	})

	t.Logf("=== 测试完成 ===")
}
