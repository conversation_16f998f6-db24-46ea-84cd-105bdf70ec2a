#!/bin/bash

# Electron-Vite 多平台编译脚本
# 使用方法: ./build.sh [platform]
# 平台选项: mac, win, linux, all, mac-x64, mac-arm64, win-x64, linux-x64

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查依赖
check_dependencies() {
    print_info "检查依赖..."
    
    if ! command -v node &> /dev/null; then
        print_error "Node.js 未安装"
        exit 1
    fi
    
    if ! command -v npm &> /dev/null; then
        print_error "npm 未安装"
        exit 1
    fi
    
    if [ ! -d "node_modules" ]; then
        print_warning "node_modules 不存在，正在安装依赖..."
        npm install
    fi
    
    print_success "依赖检查完成"
}

# 清理构建目录
clean_build() {
    print_info "清理构建目录..."
    rm -rf out/
    rm -rf release/
    print_success "构建目录已清理"
}

# 构建应用
build_app() {
    print_info "构建应用..."
    npm run build
    print_success "应用构建完成"
}

# 编译特定平台
build_platform() {
    local platform=$1
    print_info "开始编译 $platform 平台..."
    
    case $platform in
        "mac")
            npm run electron:build:mac
            ;;
        "win")
            npm run electron:build:win
            ;;
        "linux")
            npm run electron:build:linux
            ;;
        "all")
            npm run electron:build:all
            ;;
        "mac-x64")
            npm run electron:build:mac-x64
            ;;
        "mac-arm64")
            npm run electron:build:mac-arm64
            ;;
        "win-x64")
            npm run electron:build:win-x64
            ;;
        "linux-x64")
            npm run electron:build:linux-x64
            ;;
        *)
            print_error "不支持的平台: $platform"
            print_info "支持的平台: mac, win, linux, all, mac-x64, mac-arm64, win-x64, linux-x64"
            exit 1
            ;;
    esac
    
    print_success "$platform 平台编译完成"
}

# 显示构建结果
show_results() {
    print_info "构建结果:"
    if [ -d "release" ]; then
        ls -lh release/ | grep -E '\.(dmg|exe|AppImage)$' || print_warning "未找到安装包文件"
    else
        print_warning "release 目录不存在"
    fi
}

# 显示帮助信息
show_help() {
    echo "Electron-Vite 多平台编译脚本"
    echo ""
    echo "使用方法:"
    echo "  ./build.sh [platform] [options]"
    echo ""
    echo "平台选项:"
    echo "  mac       - macOS (x64 + arm64)"
    echo "  win       - Windows (x64)"
    echo "  linux     - Linux (x64)"
    echo "  all       - 所有平台"
    echo "  mac-x64   - macOS Intel"
    echo "  mac-arm64 - macOS Apple Silicon"
    echo "  win-x64   - Windows x64"
    echo "  linux-x64 - Linux x64"
    echo ""
    echo "选项:"
    echo "  --clean   - 构建前清理目录"
    echo "  --help    - 显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  ./build.sh mac --clean"
    echo "  ./build.sh all"
    echo "  ./build.sh mac-arm64"
}

# 主函数
main() {
    local platform=""
    local clean_flag=false
    
    # 解析参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            --clean)
                clean_flag=true
                shift
                ;;
            --help)
                show_help
                exit 0
                ;;
            -*)
                print_error "未知选项: $1"
                show_help
                exit 1
                ;;
            *)
                if [ -z "$platform" ]; then
                    platform=$1
                else
                    print_error "只能指定一个平台"
                    exit 1
                fi
                shift
                ;;
        esac
    done
    
    # 如果没有指定平台，显示帮助
    if [ -z "$platform" ]; then
        show_help
        exit 0
    fi
    
    print_info "开始构建 $platform 平台..."
    
    # 检查依赖
    check_dependencies
    
    # 清理构建目录（如果指定）
    if [ "$clean_flag" = true ]; then
        clean_build
    fi
    
    # 构建应用
    build_app
    
    # 编译平台
    build_platform "$platform"
    
    # 显示结果
    show_results
    
    print_success "构建完成！"
}

# 运行主函数
main "$@"
