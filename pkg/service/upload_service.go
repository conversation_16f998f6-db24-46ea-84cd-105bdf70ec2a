package service

import (
	"chat_note_api/pkg/vo"
	"chat_note_api/tools"
	"context"
	"fmt"
	"io"
	"mime/multipart"
	"net/http"
	"os"
	"path/filepath"
	"strings"
	"time"
)

var UploadService = &uploadService{}

type uploadService struct{}

func (s *uploadService) UploadImage(ctx context.Context, req *vo.UploadImageReq) (*vo.UploadImageRes, error) {
	_, log := tools.GenContext(ctx)
	log.Info("UploadImage")

	// 验证文件类型
	if !s.isValidImageType(req.File.Header.Get("Content-Type")) {
		return nil, fmt.Errorf("不支持的图片格式")
	}

	// 验证文件大小（限制10MB）
	if req.File.Size > 10*1024*1024 {
		return nil, fmt.Errorf("图片文件过大，最大支持10MB")
	}

	// 生成文件名和路径
	fileName := s.generateFileName(req.File.Filename)
	filePath := s.getUploadPath(fileName)

	// 保存文件
	err := s.saveFile(req.File, filePath)
	if err != nil {
		log.Error(err)
		return nil, err
	}

	// 生成访问URL
	url := s.generateFileUrl(fileName)

	return &vo.UploadImageRes{
		Url:      url,
		FileName: fileName,
		Size:     req.File.Size,
	}, nil
}

func (s *uploadService) UploadImageByUrl(ctx context.Context, req *vo.UploadImageByUrlReq) (*vo.UploadImageRes, error) {
	_, log := tools.GenContext(ctx)
	log.Info("UploadImageByUrl")

	// 下载图片
	resp, err := http.Get(req.Url)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	// 验证内容类型
	if !s.isValidImageType(resp.Header.Get("Content-Type")) {
		return nil, fmt.Errorf("不支持的图片格式")
	}

	// 生成文件名
	fileName := s.generateFileNameFromUrl(req.Url)
	filePath := s.getUploadPath(fileName)

	// 保存文件
	err = s.saveFileFromReader(resp.Body, filePath)
	if err != nil {
		log.Error(err)
		return nil, err
	}

	// 获取文件信息
	fileInfo, err := os.Stat(filePath)
	if err != nil {
		return nil, err
	}

	url := s.generateFileUrl(fileName)

	return &vo.UploadImageRes{
		Url:      url,
		FileName: fileName,
		Size:     fileInfo.Size(),
	}, nil
}

// 验证图片类型
func (s *uploadService) isValidImageType(contentType string) bool {
	validTypes := []string{
		"image/jpeg", "image/jpg", "image/png",
		"image/gif", "image/webp", "image/bmp",
	}

	for _, validType := range validTypes {
		if strings.Contains(contentType, validType) {
			return true
		}
	}
	return false
}

// 生成文件名
func (s *uploadService) generateFileName(originalName string) string {
	ext := filepath.Ext(originalName)
	timestamp := time.Now().UnixNano()
	return fmt.Sprintf("%d%s", timestamp, ext)
}

// 从URL生成文件名
func (s *uploadService) generateFileNameFromUrl(url string) string {
	// 尝试从URL中提取文件扩展名
	ext := filepath.Ext(url)
	if ext == "" {
		ext = ".jpg" // 默认扩展名
	}
	timestamp := time.Now().UnixNano()
	return fmt.Sprintf("%d%s", timestamp, ext)
}

// 获取上传路径
func (s *uploadService) getUploadPath(fileName string) string {
	uploadDir := "./uploads/images"
	os.MkdirAll(uploadDir, 0755)
	return filepath.Join(uploadDir, fileName)
}

// 生成文件访问URL
func (s *uploadService) generateFileUrl(fileName string) string {
	return fmt.Sprintf("/uploads/images/%s", fileName)
}

// 保存上传的文件
func (s *uploadService) saveFile(file *multipart.FileHeader, dst string) error {
	src, err := file.Open()
	if err != nil {
		return err
	}
	defer src.Close()

	out, err := os.Create(dst)
	if err != nil {
		return err
	}
	defer out.Close()

	_, err = io.Copy(out, src)
	return err
}

// 从Reader保存文件
func (s *uploadService) saveFileFromReader(reader io.Reader, dst string) error {
	out, err := os.Create(dst)
	if err != nil {
		return err
	}
	defer out.Close()

	_, err = io.Copy(out, reader)
	return err
}
