package service

import (
	"chat_note_api/constant"
	"chat_note_api/pkg/db"
	"chat_note_api/pkg/model"
	"chat_note_api/pkg/vo"
	"chat_note_api/tools"
	"context"
)

var VerificationCodeService = &verificationCodeService{}

type verificationCodeService struct {
}

func (s *verificationCodeService) SendVerificationCode(ctx context.Context, sendVerificationCodeReq *vo.SendVerificationCodeReq) error {
	_, log := tools.GenContext(ctx)
	log.WithField("email", sendVerificationCodeReq.Email).Info("SendVerificationCode")

	// 生成6位数字验证码
	code := tools.GenerateRandomCode(6)
	log.Debug("code:", code)

	// todo 发送邮件

	err := db.VerificationCodeManager.SendVerificationCode(ctx, sendVerificationCodeReq, code)
	if err != nil {
		return err
	}
	return nil
}

func (s *verificationCodeService) GetVerificationCode(ctx context.Context, vcType constant.VerificationCodeType, email string) (*model.VerificationCode, error) {
	_, log := tools.GenContext(ctx)
	log.WithField("email", email).WithField("vc_type", vcType).Info("GetVerificationCode")

	verificationCode, err := db.VerificationCodeManager.GetVerificationCode(ctx, vcType, email)
	if err != nil {
		return nil, err
	}
	return verificationCode, nil
}
