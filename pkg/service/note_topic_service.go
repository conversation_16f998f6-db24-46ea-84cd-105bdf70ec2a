package service

import (
	"chat_note_api/pkg/db"
	"chat_note_api/pkg/model"
	"chat_note_api/pkg/vo"
	"chat_note_api/tools"
	"context"
	"time"

	"github.com/jinzhu/copier"
)

var NoteTopicService = &noteTopicService{}

type noteTopicService struct {
}

func (s *noteTopicService) ListNoteTopic(ctx context.Context) ([]*vo.ListNoteTopicRes, error) {
	_, log := tools.GenContext(ctx)
	log.Info("ListNoteTopic")

	userId := tools.GetUserId(ctx)
	noteTopics, err := db.NoteTopicManager.ListNoteTopic(ctx, userId)
	if err != nil {
		log.Error(err)
		return nil, err
	}

	noteTopicRes := make([]*vo.ListNoteTopicRes, 0)
	err = copier.Copy(&noteTopicRes, noteTopics)
	if err != nil {
		log.Error(err)
		return nil, err
	}
	return noteTopicRes, nil
}

func (s *noteTopicService) CreatedNoteTopic(ctx context.Context, request *vo.CreatedNoteTopicReq) (*vo.CreatedNoteTopicRes, error) {
	_, log := tools.GenContext(ctx)
	log.Info("CreatedNoteTopic")

	userId := tools.GetUserId(ctx)
	noteTopicModel := &model.NoteTopicModel{
		UserId:      userId,
		Title:       request.Title,
		Description: request.Description,
		Avatar:      request.Avatar,
		CreatedTime: time.Now().Unix(),
		UpdatedTime: time.Now().Unix(),
		DeleteTime:  int64(0),
	}
	noteTopicModel, err := db.NoteTopicManager.CreatedNoteTopic(ctx, noteTopicModel)
	if err != nil {
		log.Error(err)
		return nil, err
	}

	createdNoteTopicRes := &vo.CreatedNoteTopicRes{}
	err = copier.Copy(createdNoteTopicRes, noteTopicModel)
	if err != nil {
		log.Error(err)
		return nil, err
	}
	return createdNoteTopicRes, nil
}
