package service

import (
	"chat_note_api/pkg/db"
	"chat_note_api/pkg/model"
	"chat_note_api/pkg/vo"
	"chat_note_api/tools"
	"context"
	"time"

	"github.com/jinzhu/copier"
)

var NoteService = &noteService{}

type noteService struct {
}

func (s *noteService) CreatedNote(ctx context.Context, createNoteReq *vo.CreatedNoteReq) (*vo.CreatedNoteRes, error) {
	_, log := tools.GenContext(ctx)
	log.Info("CreatedNote")

	userId := tools.GetUserId(ctx)
	note := &model.Note{
		UserId:      userId,
		TopicId:     createNoteReq.TopicId,
		NoteType:    createNoteReq.NoteType,
		Content:     createNoteReq.Content,
		CreatedTime: time.Now().Unix(),
		UpdatedTime: time.Now().Unix(),
		DeleteTime:  int64(0),
	}
	note, err := db.NoteManager.CreatedNote(ctx, note)
	if err != nil {
		log.Error(err)
		return nil, err
	}

	createdNoteRes := &vo.CreatedNoteRes{}
	err = copier.Copy(createdNoteRes, note)
	if err != nil {
		log.Error(err)
		return nil, err
	}
	return createdNoteRes, nil
}

// https://help.aliyun.com/zh/tablestore/use-cases/optimize-the-high-concurrency-im-system-architecture?spm=a2c4g.11186623.help-menu-27278.d_5_4_1.7ccd251cXt2p5a

func (s *noteService) ListNote(ctx context.Context, listNoteReq *vo.ListNoteReq) ([]vo.ListNoteRes, int64, error) {
	_, log := tools.GenContext(ctx)
	log.Info("ListNote")

	userId := tools.GetUserId(ctx)
	notes, lasterId, err := db.NoteManager.ListNote(ctx, userId, listNoteReq)
	if err != nil {
		log.Error(err)
		return nil, 0, err
	}

	listNoteRes := make([]vo.ListNoteRes, 0)
	for _, note := range notes {
		listNoteRes = append(listNoteRes, vo.ListNoteRes{
			Id:          note.Id,
			UserId:      note.UserId,
			TopicId:     note.TopicId,
			NoteType:    note.NoteType,
			Content:     note.Content,
			CreatedTime: note.CreatedTime,
			UpdatedTime: note.UpdatedTime,
			DeleteTime:  note.DeleteTime,
		})
	}
	return listNoteRes, lasterId, nil
}

func (s *noteService) DeleteNote(ctx context.Context, deleteNoteReq *vo.DeleteNoteReq) error {
	_, log := tools.GenContext(ctx)
	log.Info("DeleteNote")

	userId := tools.GetUserId(ctx)
	err := db.NoteManager.DeleteNote(ctx, userId, deleteNoteReq)
	if err != nil {
		log.Error(err)
		return err
	}
	return nil
}
