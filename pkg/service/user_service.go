package service

import (
	"chat_note_api/pkg/db"
	"chat_note_api/pkg/model"
	"chat_note_api/pkg/vo"
	"chat_note_api/tools"
	"context"
	"strconv"
	"time"

	"github.com/jinzhu/copier"
)

var UserService = &userService{}

type userService struct {
}

func (s *userService) GetUserById(ctx context.Context, id string) (*vo.UserRes, error) {
	_, log := tools.GenContext(ctx)
	log.WithField("id", id).Info("GetUserById")

	user, err := db.UserManager.GetUserById(ctx, id)
	if err != nil {
		log.Error(err)
		return nil, err
	}
	userRes := &vo.UserRes{}
	err = copier.Copy(userRes, user)
	if err != nil {
		log.Error(err)
		return nil, err
	}
	return userRes, nil
}

func (s *userService) GetUserByEmail(ctx context.Context, email string) (*vo.UserRes, error) {
	_, log := tools.GenContext(ctx)
	log.WithField("email", email).Info("GetUserByEmail")

	user, err := db.UserManager.GetUserByEmail(ctx, email)
	if err != nil {
		log.Error(err)
		return nil, err
	}
	userRes := &vo.UserRes{}
	err = copier.Copy(userRes, user)
	if err != nil {
		log.Error(err)
		return nil, err
	}
	return userRes, nil
}

func (s *userService) CreateUser(ctx context.Context, input *vo.RegisterReq, hashedPassword string) (*vo.UserRes, error) {
	user := &model.UserModel{}
	err := copier.Copy(user, input)
	if err != nil {
		return nil, err
	}
	user.Id = strconv.FormatInt(tools.GenId(), 10)
	user.Password = string(hashedPassword)
	user.VipExpireTime = time.Now().Add(time.Hour * 24 * 30).Unix()
	user.CreatedTime = time.Now().Unix()
	user.UpdatedTime = time.Now().Unix()
	user.DeleteTime = 0

	err = db.UserManager.CreateUser(ctx, user)
	if err != nil {
		return nil, err
	}

	userRes := &vo.UserRes{}
	err = copier.Copy(userRes, user)
	if err != nil {
		return nil, err
	}
	return userRes, nil

}

func (s *userService) ResetPassword(ctx context.Context, email, hashedPassword string) error {
	_, log := tools.GenContext(ctx)
	log.WithField("email", email).Info("ResetPassword")

	return db.UserManager.UpdateUser(ctx, email, map[string]interface{}{
		"password": hashedPassword,
	})
}
