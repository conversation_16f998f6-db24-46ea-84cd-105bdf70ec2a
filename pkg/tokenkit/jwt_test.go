package tokenkit

import (
	"github.com/golang-jwt/jwt"
	log "github.com/sirupsen/logrus"
	. "github.com/smartystreets/goconvey/convey"
	"testing"
	"time"
)

func TestEncrypt(t *testing.T) {
	Convey("test create tokenKit", t, func() {
		currentTime := time.Now()
		t := JWTToken{
			Magic: "xuanjinliang@gmail.come10adc3949ba59abbe56e057f20f883e",
			Claims: jwt.MapClaims{
				"exp": currentTime.Add(time.Hour * 2).Unix(),
				"sub": "<EMAIL>",
				"iat": currentTime.Unix(),
			},
		}

		log.Infof("%v", t.CreateToken())
	})
}

func TestToken_ParseToken(t *testing.T) {
	Convey("test create Valid", t, func() {
		t := JWTToken{
			Magic: "xuanjinliang@gmail.come10adc3949ba59abbe56e057f20f883e",
			Token: "ZXlKaGJHY2lPaUpJVXpJMU5pSXNJblI1Y0NJNklrcFhWQ0o5LmV5SmxlSEFpT2pFMk1qWXpOREF6TmpVc0ltbGhkQ0k2TVRZeU5qTXpNekUyTlN3aWFYTnpJam9pVTNCdmRFMWhlQ0lzSW01aVppSTZNVFl5TmpNek16RTJOU3dpYzNWaUlqb2lZV0pqUUdWNFlXMXdiR1V1WTI5dEluMC41WFZuOHdsS241aFcyNWdZWHlCTXJTVVRLM1NlS1JzOFhVWW4tZXAtaE1z",
		}

		data, err := t.ParseToken()
		So(err, ShouldBeNil)
		log.Infof("%v", data)
	})
}
