package tokenkit

import (
	"fmt"
	"github.com/golang-jwt/jwt"
	"github.com/sirupsen/logrus"
)

type JWTToken struct {
	Magic  string
	Token  string
	Claims jwt.MapClaims
}

func (t JWTToken) CreateToken() string {
	SecretKey := []byte(t.Magic)
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, &t.Clai<PERSON>)
	tokenStr, err := token.SignedString(SecretKey)
	t.Token = jwt.EncodeSegment([]byte(tokenStr))

	if err != nil {
		logrus.Panicf("%v", err)
	}

	return t.Token
}

func (t JWTToken) ParseToken() (jwt.MapClaims, error) {
	base64Decoder, _ := jwt.DecodeSegment(t.Token)
	token, err := jwt.ParseWithClaims(string(base64Decoder), jwt.MapClaims{}, func(token *jwt.Token) (interface{}, error) {
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
		}
		return []byte(t.Magic), nil
	})
	if err != nil {
		return nil, err
	}

	claims, ok := token.Claims.(jwt.MapClaims)
	if ok && token.Valid {
		return claims, nil
	}

	return nil, fmt.Errorf("unexpected token error")
}
