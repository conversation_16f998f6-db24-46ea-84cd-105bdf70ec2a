package tokenkit

import (
	"time"

	"github.com/golang-jwt/jwt"
)

const key = "DHJ4d34g@34dfASD"

const tokenDuration = time.Hour * 24 * 7

type Token struct {
	Str    string `json:"str"`
	UserId string `json:"user_id"`
}

func NewToken(userId string) *Token {
	currentTime := time.Now()
	jwtToken := JWTToken{
		Magic: key,
		Claims: jwt.MapClaims{
			"exp": currentTime.Add(tokenDuration).Unix(),
			"sub": userId,
			"iat": currentTime.Unix(),
		},
	}

	token := Token{
		Str: jwtToken.CreateToken(),
	}
	return &token
}

func NewTokenFromStr(token string) (*Token, error) {
	jwtToken := JWTToken{
		Magic: key,
		Token: token,
	}
	claims, err := jwtToken.ParseToken()
	if err != nil {
		return nil, err
	}

	t := Token{
		Str:    token,
		UserId: claims["sub"].(string),
	}

	return &t, nil
}
