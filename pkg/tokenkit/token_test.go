package tokenkit

import (
	"fmt"
	"testing"
	"time"

	"github.com/davecgh/go-spew/spew"
	. "github.com/smartystreets/goconvey/convey"
)

func TestToken_NewToken(t *testing.T) {
	Convey("test", t, func() {

		token1 := NewToken("1")
		token2, err := NewTokenFromStr(token1.Str)
		So(err, ShouldBeNil)
		fmt.Println(token2)
	})
}

func TestToken_Expired(t *testing.T) {
	Convey("test", t, func() {
		// email := "<EMAIL>"
		token1 := NewToken("1")
		time.Sleep(time.Second * 2)
		token2, err := NewTokenFromStr(token1.Str)
		So(err, ShouldNotBeNil)
		fmt.Println(err)
		spew.Dump(token2)
	})
}

func TestToken_EncryptDecrypt(t *testing.T) {

	token1 := NewToken("345")
	fmt.Println(token1)
	// token3 := "5f3kcxPhu0UOdkwMKBGqr7XE86ecjT/SjfLh9oz6v/DUhyl5gvlIUphouGVa2nAsb36HTGwC+vikKMyNWylRdw=="
	token2, err := NewTokenFromStr(token1.Str)
	// token2, err := NewTokenFromStr(token3)
	if err != nil {
		t.Log(err)
		return
	}
	fmt.Println(token2)
}
