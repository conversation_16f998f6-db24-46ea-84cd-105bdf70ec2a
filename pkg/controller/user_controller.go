package controller

import (
	"chat_note_api/constant"
	"chat_note_api/pkg/errs"
	"chat_note_api/pkg/service"
	"chat_note_api/pkg/tokenkit"
	"chat_note_api/pkg/vo"
	"chat_note_api/tools"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"golang.org/x/crypto/bcrypt"
	"gorm.io/gorm"
)

func Login(c *gin.Context) {
	ctx, log := tools.GenContext(c)

	input := &vo.LoginReq{}
	err := c.ShouldBind(&input)
	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusBadRequest, err)
		return
	}

	user, err := service.UserService.GetUserByEmail(ctx, input.Email)
	if err != nil && err != gorm.ErrRecordNotFound {
		log.Error(err)
		ResponseWithError(c, http.StatusInternalServerError, err)
		return
	}

	// check password
	err = bcrypt.CompareHashAndPassword([]byte(user.Password), []byte(input.Password))
	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusUnauthorized, errs.ErrorAccount)
		return
	}

	token := tokenkit.NewToken(user.Id)

	DefaultResponse(c, gin.H{"token": token.Str, "user": user})
}

func Register(c *gin.Context) {
	ctx, log := tools.GenContext(c)

	input := &vo.RegisterReq{}
	err := c.ShouldBind(&input)
	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusBadRequest, err)
		return
	}

	verificationCode, err := service.VerificationCodeService.GetVerificationCode(ctx, constant.VerificationCodeTypeRegister, input.Email)
	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusInternalServerError, errs.ErrorVerificationCode)
		return
	}

	// 验证码有效期10分钟
	if verificationCode.CreatedTime+(60*10) < time.Now().Unix() {
		ResponseWithError(c, http.StatusBadRequest, errs.ErrorVerificationCodeExpire)
		return
	}
	if verificationCode.Code != input.VerificationCode {
		ResponseWithError(c, http.StatusBadRequest, errs.ErrorVerificationCode)
		return
	}

	// check email
	user, err := service.UserService.GetUserByEmail(ctx, input.Email)
	if err != nil {
		if err != errs.ErrorNoData {
			ResponseWithError(c, http.StatusInternalServerError, err)
			return
		}
	}
	if user != nil && user.Id != "" {
		ResponseWithError(c, http.StatusBadRequest, errs.ErrorAccountExist)
		return
	}

	// hash password
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(input.Password), bcrypt.DefaultCost)
	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusInternalServerError, err)
		return
	}

	user, err = service.UserService.CreateUser(ctx, input, string(hashedPassword))
	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusInternalServerError, err)
		return
	}

	token := tokenkit.NewToken(user.Id)

	DefaultResponse(c, gin.H{"token": token.Str, "user": user})
}

func SendVerificationCode(c *gin.Context) {
	_, log := tools.GenContext(c)
	input := &vo.SendVerificationCodeReq{}
	err := c.ShouldBind(&input)
	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusBadRequest, err)
		return
	}

	err = service.VerificationCodeService.SendVerificationCode(c, input)
	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusInternalServerError, err)
		return
	}

	DefaultResponse(c, "success")
}

func ResetPassword(c *gin.Context) {
	ctx, log := tools.GenContext(c)

	input := &vo.ResetPasswordReq{}
	err := c.ShouldBind(&input)
	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusBadRequest, err)
		return
	}

	verificationCode, err := service.VerificationCodeService.GetVerificationCode(ctx, constant.VerificationCodeTypeResetPassword, input.Email)
	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusInternalServerError, errs.ErrorVerificationCode)
		return
	}

	// 验证码有效期10分钟
	if verificationCode.CreatedTime+(60*10) < time.Now().Unix() {
		ResponseWithError(c, http.StatusBadRequest, errs.ErrorVerificationCodeExpire)
		return
	}
	if verificationCode.Code != input.VerificationCode {
		ResponseWithError(c, http.StatusBadRequest, errs.ErrorVerificationCode)
		return
	}

	// hash password
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(input.NewPassword), bcrypt.DefaultCost)
	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusInternalServerError, err)
		return
	}

	err = service.UserService.ResetPassword(ctx, input.Email, string(hashedPassword))
	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusInternalServerError, err)
		return
	}

	DefaultResponse(c, "success")
}
