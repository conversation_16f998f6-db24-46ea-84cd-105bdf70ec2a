package controller

import (
	"chat_note_api/pkg/service"
	"chat_note_api/pkg/vo"
	"chat_note_api/tools"
	"net/http"

	"github.com/gin-gonic/gin"
)

func CreatedNoteTopic(c *gin.Context) {
	ctx, log := tools.GenContext(c)
	log.Info("CreatedNoteTopic")

	createdNoteTopicReq := &vo.CreatedNoteTopicReq{}
	err := c.ShouldBind(&createdNoteTopicReq)
	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusBadRequest, err)
		return
	}

	noteTopic, err := service.NoteTopicService.CreatedNoteTopic(ctx, createdNoteTopicReq)
	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusInternalServerError, err)
		return
	}

	DefaultResponse(c, noteTopic)
}

func ListNoteTopic(c *gin.Context) {
	ctx, log := tools.GenContext(c)
	log.Info("ListNoteTopic")

	noteTopics, err := service.NoteTopicService.ListNoteTopic(ctx)
	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusInternalServerError, err)
		return
	}

	DefaultResponse(c, noteTopics)
}
