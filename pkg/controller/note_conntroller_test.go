package controller

import (
	"chat_note_api/pkg/vo"
	"fmt"
	"testing"

	"github.com/davecgh/go-spew/spew"
)

func TestCreatedNote(t *testing.T) {
	response := Post(t, CreatedNote, &vo.CreatedNoteReq{
		TopicId:  "1",
		NoteType: "text",
		Content:  fmt.Sprintf("hello %d", 1),
	})
	spew.Dump(response.Body)
}

func TestListNote(t *testing.T) {
	response := Post(t, ListNote, &vo.ListNoteReq{
		TopicId:  "1",
		LasterId: 1750002741970000,
	})
	spew.Dump(response.Body)
}

func TestDeleteNote(t *testing.T) {
	deleteNoteReq := &vo.DeleteNoteReq{
		TopicId: "1750341723488000",
		NoteId:  1750342015881000,
	}
	response := DeleteBody(t, DeleteNote, deleteNoteReq)
	spew.Dump(response.Body)

}
