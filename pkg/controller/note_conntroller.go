package controller

import (
	"chat_note_api/pkg/service"
	"chat_note_api/pkg/vo"
	"chat_note_api/tools"
	"fmt"
	"net/http"

	"github.com/gin-gonic/gin"
)

func CreatedNote(c *gin.Context) {
	_, log := tools.GenContext(c)
	log.Info("CreatedNote")

	createNoteReq := vo.CreatedNoteReq{}
	err := c.ShouldBind(&createNoteReq)
	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusBadRequest, err)
		return
	}

	res, err := service.NoteService.CreatedNote(c, &createNoteReq)
	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusInternalServerError, err)
		return
	}
	DefaultResponse(c, res)
}

func ListNote(c *gin.Context) {
	_, log := tools.GenContext(c)
	log.Info("ListNote")
	listNoteReq := &vo.ListNoteReq{}
	err := c.ShouldBind<PERSON>uery(listNoteReq)
	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusBadRequest, err)
		return
	}

	notes, lasterId, err := service.NoteService.ListNote(c, listNoteReq)
	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusInternalServerError, err)
		return
	}

	DefaultResponse(c, gin.H{"notes": notes, "laster_id": fmt.Sprintf("%d", lasterId)})
}

func DeleteNote(c *gin.Context) {
	_, log := tools.GenContext(c)
	log.Info("DeleteNote")

	input := &vo.DeleteNoteReq{}
	err := c.ShouldBind(&input)
	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusBadRequest, err)
		return
	}
	err = service.NoteService.DeleteNote(c, input)
	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusInternalServerError, err)
		return
	}

	// todo
	DefaultResponse(c, "success")
}
