package controller

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"

	"chat_note_api/pkg/db"
	"chat_note_api/pkg/tokenkit"
	"chat_note_api/tools"
	"strings"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/joho/godotenv"
	"github.com/sirupsen/logrus"
	log "github.com/sirupsen/logrus"
	"github.com/stretchr/testify/assert"
)

var token = tokenkit.NewToken("1933201597645819904").Str

func testInitGin(t *testing.T) *gin.Engine {
	logrus.SetLevel(logrus.TraceLevel)
	err := godotenv.Load("../../.env")
	assert.Nil(t, err)
	_, err = db.InitDBManager()
	assert.Nil(t, err)
	assert.Nil(t, tools.InitSnowflake())
	engin := gin.Default()
	engin.Use(SetRequestId)
	engin.Use(MiddlewareToken)

	return engin
}

func Put(t *testing.T, ginFunc func(c *gin.Context), query map[string]string, body interface{}) *httptest.ResponseRecorder {
	engin := testInitGin(t)

	bodyByte, _ := json.Marshal(body)

	t.Log("createAuthorizationReqByte", string(bodyByte))

	uri := "/api/put"
	sb := []string{}
	for key, value := range query {
		sb = append(sb, fmt.Sprintf("%v=%v", key, value))
	}

	urlWithArgs := uri
	if len(sb) > 0 {
		urlWithArgs = fmt.Sprintf("%v?%v", urlWithArgs, strings.Join(sb, "&"))
	}
	request := httptest.NewRequest(http.MethodPut, urlWithArgs, bytes.NewBuffer(bodyByte))
	request.Header.Set("Content-Type", "application/json; charset=UTF-8")

	response := httptest.NewRecorder()

	engin.PUT(uri, ginFunc)
	// todo del
	request.Header.Add("Authorization", token)
	engin.ServeHTTP(response, request)

	return response
}

func Post(t *testing.T, ginFunc func(c *gin.Context), req interface{}) *httptest.ResponseRecorder {
	engin := testInitGin(t)

	reqByte, _ := json.Marshal(req)

	t.Log("createAuthorizationReqByte", string(reqByte))

	uri := "/api/post"

	request := httptest.NewRequest(http.MethodPost, uri, bytes.NewBuffer(reqByte))
	request.Header.Set("Content-Type", "application/json; charset=UTF-8")

	response := httptest.NewRecorder()

	engin.POST(uri, ginFunc)
	// todo del
	request.Header.Add("Authorization", token)
	engin.ServeHTTP(response, request)

	return response
}

func Get(t *testing.T, ginFunc func(c *gin.Context), req map[string]string) *httptest.ResponseRecorder {
	engin := testInitGin(t)

	reqByte, err := json.Marshal(req)
	if err != nil {
		log.Error(err)
		return nil
	}

	t.Log("createAuthorizationReqByte", string(reqByte))

	uri := "/api/get"

	sb := []string{}
	for key, value := range req {
		sb = append(sb, fmt.Sprintf("%v=%v", key, value))
	}

	urlWithArgs := uri
	if len(sb) > 0 {
		urlWithArgs = fmt.Sprintf("%v?%v", urlWithArgs, strings.Join(sb, "&"))
	}

	request := httptest.NewRequest(http.MethodGet, urlWithArgs, bytes.NewBuffer(reqByte))
	// todo del
	request.Header.Add("Authorization", token)
	response := httptest.NewRecorder()

	engin.GET(uri, ginFunc)
	engin.ServeHTTP(response, request)

	return response
}

// response := GetParams(t, DetailBundleGroup, "/api/:bundle_group_id", "/api/1")
func GetParams(t *testing.T, ginFunc func(c *gin.Context), url, fulUrl string) *httptest.ResponseRecorder {
	engin := testInitGin(t)

	request := httptest.NewRequest(http.MethodGet, fulUrl, nil)
	// todo del
	request.Header.Add("Authorization", token)
	response := httptest.NewRecorder()

	engin.GET(url, ginFunc)
	engin.ServeHTTP(response, request)

	return response
}

func PutParams(t *testing.T, ginFunc func(c *gin.Context), url, fulUrl string, body interface{}) *httptest.ResponseRecorder {
	engin := testInitGin(t)

	bodyByte, _ := json.Marshal(body)

	t.Log("createAuthorizationReqByte", string(bodyByte))

	request := httptest.NewRequest(http.MethodPut, fulUrl, bytes.NewBuffer(bodyByte))
	request.Header.Set("Content-Type", "application/json; charset=UTF-8")

	response := httptest.NewRecorder()

	engin.PUT(url, ginFunc)
	// todo del
	request.Header.Add("Authorization", token)
	engin.ServeHTTP(response, request)

	return response
}

func Patch(t *testing.T, ginFunc func(c *gin.Context), req interface{}) *httptest.ResponseRecorder {
	engin := testInitGin(t)

	reqByte, _ := json.Marshal(req)

	t.Log("createAuthorizationReqByte", string(reqByte))

	uri := "/api/post"

	request := httptest.NewRequest(http.MethodPost, uri, bytes.NewBuffer(reqByte))
	request.Header.Set("Content-Type", "application/json; charset=UTF-8")

	response := httptest.NewRecorder()

	engin.PATCH(uri, ginFunc)
	// todo del
	request.Header.Add("Authorization", token)
	engin.ServeHTTP(response, request)

	return response
}

func PatchParams(t *testing.T, ginFunc func(c *gin.Context), url, fulUrl string, body interface{}) *httptest.ResponseRecorder {
	engin := testInitGin(t)

	bodyByte, _ := json.Marshal(body)

	t.Log("createAuthorizationReqByte", string(bodyByte))

	request := httptest.NewRequest(http.MethodPatch, fulUrl, bytes.NewBuffer(bodyByte))
	request.Header.Set("Content-Type", "application/json; charset=UTF-8")

	response := httptest.NewRecorder()

	engin.PATCH(url, ginFunc)
	// todo del
	request.Header.Add("Authorization", token)
	engin.ServeHTTP(response, request)

	return response
}

func Delete(t *testing.T, ginFunc func(c *gin.Context)) *httptest.ResponseRecorder {
	engin := testInitGin(t)
	uri := "/api/delete"

	request := httptest.NewRequest(http.MethodPost, uri, nil)
	request.Header.Set("Content-Type", "application/json; charset=UTF-8")

	response := httptest.NewRecorder()

	engin.DELETE(uri, ginFunc)
	// todo del
	request.Header.Add("Authorization", token)
	engin.ServeHTTP(response, request)

	return response
}

func DeleteBody(t *testing.T, ginFunc func(c *gin.Context), req interface{}) *httptest.ResponseRecorder {
	engin := testInitGin(t)

	reqByte, _ := json.Marshal(req)

	t.Log("createAuthorizationReqByte", string(reqByte))

	uri := "/api/delete_body"

	request := httptest.NewRequest(http.MethodDelete, uri, bytes.NewBuffer(reqByte))
	request.Header.Set("Content-Type", "application/json; charset=UTF-8")

	response := httptest.NewRecorder()

	engin.DELETE(uri, ginFunc)
	// todo del
	request.Header.Add("Authorization", token)
	engin.ServeHTTP(response, request)

	return response
}

func DeleteParams(t *testing.T, ginFunc func(c *gin.Context), url, fulUrl string) *httptest.ResponseRecorder {
	engin := testInitGin(t)

	request := httptest.NewRequest(http.MethodDelete, fulUrl, nil)
	request.Header.Set("Content-Type", "application/json; charset=UTF-8")

	response := httptest.NewRecorder()

	engin.DELETE(url, ginFunc)
	// todo del
	request.Header.Add("Authorization", token)
	engin.ServeHTTP(response, request)

	return response
}
