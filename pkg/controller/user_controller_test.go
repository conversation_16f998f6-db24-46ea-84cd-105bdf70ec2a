package controller

import (
	"chat_note_api/constant"
	"chat_note_api/pkg/vo"
	"testing"

	"github.com/davecgh/go-spew/spew"
)

func TestRegister(t *testing.T) {
	testInitGin(t)

	response := Post(t, Register, &vo.RegisterReq{
		Email:            "<EMAIL>",
		NickName:         "CrazyWolf",
		Avatar:           "https://www.google.com",
		Password:         "123456",
		VerificationCode: "648541",
	})
	spew.Dump(response.Body)
}

func TestSendVerificationCode(t *testing.T) {
	testInitGin(t)

	response := Post(t, SendVerificationCode, &vo.SendVerificationCodeReq{
		Email:  "<EMAIL>",
		VcType: constant.VerificationCodeTypeRegister,
	})
	spew.Dump(response.Body)
}

func TestLogin(t *testing.T) {
	testInitGin(t)

	response := Post(t, Login, &vo.LoginReq{
		Email:    "<EMAIL>",
		Password: "123456",
	})
	spew.Dump(response.Body)
}

func TestSendResetPasswordVerificationCode(t *testing.T) {
	testInitGin(t)

	response := Post(t, SendVerificationCode, &vo.SendVerificationCodeReq{
		Email:  "<EMAIL>",
		VcType: constant.VerificationCodeTypeResetPassword,
	})
	spew.Dump(response.Body)
}

func TestResetPassword(t *testing.T) {

	response := Post(t, ResetPassword, &vo.ResetPasswordReq{
		Email:            "<EMAIL>",
		VerificationCode: "147160",
		NewPassword:      "123456",
	})
	spew.Dump(response.Body)
}
