package controller

import (
	"chat_note_api/pkg/service"
	"chat_note_api/pkg/vo"
	"chat_note_api/tools"
	"net/http"

	"github.com/gin-gonic/gin"
)

// 上传图片文件
func UploadImage(c *gin.Context) {
	ctx, log := tools.GenContext(c)
	log.Info("UploadImage")

	file, err := c.FormFile("file")
	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusBadRequest, err)
		return
	}

	uploadReq := &vo.UploadImageReq{
		File: file,
	}

	res, err := service.UploadService.UploadImage(ctx, uploadReq)
	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusInternalServerError, err)
		return
	}

	DefaultResponse(c, res)
}

// 通过URL上传图片
func UploadImageByUrl(c *gin.Context) {
	ctx, log := tools.GenContext(c)
	log.Info("UploadImageByUrl")

	uploadReq := &vo.UploadImageByUrlReq{}
	err := c.ShouldBind(&uploadReq)
	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusBadRequest, err)
		return
	}

	res, err := service.UploadService.UploadImageByUrl(ctx, uploadReq)
	if err != nil {
		log.Error(err)
		ResponseWithError(c, http.StatusInternalServerError, err)
		return
	}

	DefaultResponse(c, res)
}
