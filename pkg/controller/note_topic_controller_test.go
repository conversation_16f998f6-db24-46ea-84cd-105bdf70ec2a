package controller

import (
	"chat_note_api/pkg/vo"
	"fmt"
	"testing"
	"time"

	"github.com/davecgh/go-spew/spew"
	"github.com/google/uuid"
)

func TestCreatedNoteTopic(t *testing.T) {

	response := Post(t, CreatedNoteTopic, &vo.CreatedNoteTopicReq{
		Title:       uuid.NewString(),
		Description: uuid.NewString(),
		Avatar:      uuid.NewString(),
	})
	spew.Dump(response.Body)

}

func TestListNoteTopic(t *testing.T) {
	testInitGin(t)
	start := time.Now()
	response := Get(t, ListNoteTopic, map[string]string{})
	fmt.Println("cost:", time.Since(start))
	spew.Dump(response.Body)
}
