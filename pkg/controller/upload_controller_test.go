package controller

import (
	"bytes"
	"chat_note_api/pkg/vo"
	"encoding/json"
	"io"
	"mime/multipart"
	"net/http"
	"net/http/httptest"
	"os"
	"path/filepath"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
)

func TestUploadImage(t *testing.T) {
	// 设置测试模式
	gin.SetMode(gin.TestMode)

	// 创建测试图片文件
	testImagePath := createTestImage(t)
	defer os.Remove(testImagePath)

	// 创建multipart form
	var buf bytes.Buffer
	writer := multipart.NewWriter(&buf)

	// 添加文件字段
	file, err := os.Open(testImagePath)
	assert.NoError(t, err)
	defer file.Close()

	part, err := writer.CreateFormFile("file", filepath.Base(testImagePath))
	assert.NoError(t, err)

	_, err = io.Copy(part, file)
	assert.NoError(t, err)

	err = writer.Close()
	assert.NoError(t, err)

	// 创建HTTP请求
	req := httptest.NewRequest("POST", "/api/v1/upload/image", &buf)
	req.Header.Set("Content-Type", writer.FormDataContentType())
	req.Header.Set("Authorization", "test-token")

	// 创建响应记录器
	w := httptest.NewRecorder()

	// 创建Gin上下文
	c, _ := gin.CreateTestContext(w)
	c.Request = req

	// 调用控制器方法
	UploadImage(c)

	// 验证响应
	assert.Equal(t, http.StatusOK, w.Code)

	var response map[string]interface{}
	err = json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)

	// 验证响应结构
	assert.Equal(t, float64(200), response["status"])
	assert.Equal(t, "success", response["message"])
	assert.NotNil(t, response["data"])

	data := response["data"].(map[string]interface{})
	assert.NotEmpty(t, data["url"])
	assert.NotEmpty(t, data["file_name"])
	assert.Greater(t, data["size"], float64(0))
}

func TestUploadImageByUrl(t *testing.T) {
	// 设置测试模式
	gin.SetMode(gin.TestMode)

	// 创建测试请求
	requestBody := vo.UploadImageByUrlReq{
		Url: "https://via.placeholder.com/150.png",
	}

	jsonData, err := json.Marshal(requestBody)
	assert.NoError(t, err)

	req := httptest.NewRequest("POST", "/api/v1/upload/image-by-url", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "test-token")

	// 创建响应记录器
	w := httptest.NewRecorder()

	// 创建Gin上下文
	c, _ := gin.CreateTestContext(w)
	c.Request = req

	// 调用控制器方法
	UploadImageByUrl(c)

	// 验证响应
	assert.Equal(t, http.StatusOK, w.Code)

	var response map[string]interface{}
	err = json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)

	// 验证响应结构
	assert.Equal(t, float64(200), response["status"])
	assert.Equal(t, "success", response["message"])
	assert.NotNil(t, response["data"])
}

// 创建测试图片文件
func createTestImage(t *testing.T) string {
	// 创建一个简单的PNG图片数据（1x1像素的透明PNG）
	pngData := []byte{
		0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A, // PNG signature
		0x00, 0x00, 0x00, 0x0D, 0x49, 0x48, 0x44, 0x52, // IHDR chunk
		0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x01, // 1x1 pixels
		0x08, 0x06, 0x00, 0x00, 0x00, 0x1F, 0x15, 0xC4,
		0x89, 0x00, 0x00, 0x00, 0x0A, 0x49, 0x44, 0x41, // IDAT chunk
		0x54, 0x78, 0x9C, 0x63, 0x00, 0x01, 0x00, 0x00,
		0x05, 0x00, 0x01, 0x0D, 0x0A, 0x2D, 0xB4, 0x00,
		0x00, 0x00, 0x00, 0x49, 0x45, 0x4E, 0x44, 0xAE, // IEND chunk
		0x42, 0x60, 0x82,
	}

	// 创建临时文件
	tmpFile, err := os.CreateTemp("", "test_image_*.png")
	assert.NoError(t, err)

	_, err = tmpFile.Write(pngData)
	assert.NoError(t, err)

	err = tmpFile.Close()
	assert.NoError(t, err)

	return tmpFile.Name()
}

func TestUploadImageInvalidFile(t *testing.T) {
	// 设置测试模式
	gin.SetMode(gin.TestMode)

	// 创建multipart form with invalid file
	var buf bytes.Buffer
	writer := multipart.NewWriter(&buf)

	// 添加非图片文件
	part, err := writer.CreateFormFile("file", "test.txt")
	assert.NoError(t, err)

	_, err = part.Write([]byte("This is not an image"))
	assert.NoError(t, err)

	err = writer.Close()
	assert.NoError(t, err)

	// 创建HTTP请求
	req := httptest.NewRequest("POST", "/api/v1/upload/image", &buf)
	req.Header.Set("Content-Type", writer.FormDataContentType())
	req.Header.Set("Authorization", "test-token")

	// 创建响应记录器
	w := httptest.NewRecorder()

	// 创建Gin上下文
	c, _ := gin.CreateTestContext(w)
	c.Request = req

	// 调用控制器方法
	UploadImage(c)

	// 验证响应 - 应该返回错误
	assert.Equal(t, http.StatusInternalServerError, w.Code)
}

func TestUploadImageByUrlInvalidUrl(t *testing.T) {
	// 设置测试模式
	gin.SetMode(gin.TestMode)

	// 创建无效URL的测试请求
	requestBody := vo.UploadImageByUrlReq{
		Url: "invalid-url",
	}

	jsonData, err := json.Marshal(requestBody)
	assert.NoError(t, err)

	req := httptest.NewRequest("POST", "/api/v1/upload/image-by-url", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "test-token")

	// 创建响应记录器
	w := httptest.NewRecorder()

	// 创建Gin上下文
	c, _ := gin.CreateTestContext(w)
	c.Request = req

	// 调用控制器方法
	UploadImageByUrl(c)

	// 验证响应 - 应该返回错误
	assert.Equal(t, http.StatusBadRequest, w.Code)
}
