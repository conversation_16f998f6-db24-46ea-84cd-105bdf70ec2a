package controller

import (
	"chat_note_api/constant"
	"chat_note_api/pkg/service"
	"chat_note_api/pkg/tokenkit"
	"chat_note_api/tools"
	"errors"
	"net/http"
	"regexp"
	"strings"

	"github.com/gin-contrib/cors"
	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/sirupsen/logrus"
)

func SetRequestId(context *gin.Context) {
	context.Set(constant.MiddlewareKeyRequestId, strings.ReplaceAll(uuid.NewString(), "-", ""))
}

func CorsConfig() gin.HandlerFunc {
	// cors
	corsConfig := cors.DefaultConfig()
	corsConfig.AllowHeaders = append(corsConfig.AllowHeaders, "Authorization", "company_id")
	corsConfig.AllowOriginFunc = func(origin string) bool {
		_, err := regexp.MatchString(`mobvista.com|spotmaxtech.com|localhost|zon`, origin)
		if err != nil {
			logrus.Errorln(err)
		}
		return true // TODO
	}
	corsConfig.AllowCredentials = true
	return cors.New(corsConfig)
}

func MiddlewareToken(c *gin.Context) {
	ctx, log := tools.GenContext(c)

	accessToken := c.GetHeader("Authorization")
	if accessToken == "" {
		log.Warn("Authorization empty")
		ResponseWithError(c, http.StatusUnauthorized, errors.New("Authorization empty"))
		c.Abort()
		return
	}

	token, err := tokenkit.NewTokenFromStr(accessToken)
	if err != nil {
		ResponseWithError(c, http.StatusUnauthorized, err)
		c.Abort()
		return
	}

	user, err := service.UserService.GetUserById(ctx, token.UserId)
	if err != nil {
		ResponseWithError(c, http.StatusUnauthorized, err)
		c.Abort()
		return
	}

	c.Set(constant.MiddlewareKeyUserId, user.Id)
	c.Set(constant.MiddlewareKeyUserEmail, user.Email)

	c.Next()
}
