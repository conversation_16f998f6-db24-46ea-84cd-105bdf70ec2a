package controller

import (
	"crypto/md5"
	"encoding/hex"
	"sort"
)

type Code struct {
	Code string `json:"code"`
}

func generateSignature(params map[string]string, secret string) string {
	keys := make([]string, 0, len(params))
	for k := range params {
		keys = append(keys, k)
	}
	sort.Strings(keys)

	var kvList string
	for _, key := range keys {
		kvList += key + "=" + params[key]
	}
	originSign := kvList + secret

	hasher := md5.New()
	hasher.Write([]byte(originSign))
	return hex.EncodeToString(hasher.Sum(nil))
}
