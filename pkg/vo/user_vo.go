package vo

import "chat_note_api/constant"

type UserRes struct {
	Id            string `json:"id"`
	NickName      string `json:"nick_name"`
	Avatar        string `json:"avatar"`
	Email         string `json:"email"`
	Password      string `json:"-"`
	VipExpireTime int64  `json:"vip_expire_time"`                          // vip到期时间
	UpdatedTime   int64  `json:"updated_time" gorm:"autoUpdateTime:milli"` // 使用时间戳毫秒数填充更新时间
	CreatedTime   int64  `json:"created_time" gorm:"autoCreateTime"`       // 使用时间戳秒数填充创建时间
	DeleteTime    int64  `json:"delete_time" `                             // 使用时间戳秒数填充创建时间
}

type LoginReq struct {
	Email    string `json:"email" binding:"required,email"`
	Password string `json:"password" binding:"required,min=6"`
}

type RegisterReq struct {
	Email            string `json:"email" binding:"required,email"`
	NickName         string `json:"nick_name" binding:"required"`
	Avatar           string `json:"avatar" binding:"required"`
	Password         string `json:"password" binding:"required,min=6"`
	VerificationCode string `json:"verification_code" binding:"required,len=6"`
}

type SendVerificationCodeReq struct {
	Email  string                        `json:"email" binding:"required,email"`
	VcType constant.VerificationCodeType `json:"vc_type" binding:"required"`
}
