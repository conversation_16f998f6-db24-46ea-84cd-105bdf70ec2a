package vo

type CreatedNoteTopicReq struct {
	Title       string `json:"title" binding:"required"`
	Description string `json:"description"`
	Avatar      string `json:"avatar"`
}

type CreatedNoteTopicRes struct {
	Id          int64  `json:"id,string"`
	UserId      string `json:"user_id"`
	TopicId     string `json:"topic_id"`
	Title       string `json:"title"`
	Description string `json:"description"`
	Avatar      string `json:"avatar"`
	CreatedTime int64  `json:"created_time"`
	UpdatedTime int64  `json:"updated_time"`
	DeleteTime  int64  `json:"delete_time"`
}
type ListNoteTopicRes struct {
	Id          int64  `json:"id,string"`
	UserId      string `json:"user_id"`
	TopicId     string `json:"topic_id"`
	Title       string `json:"title"`
	Description string `json:"description"`
	Avatar      string `json:"avatar"`
	CreatedTime int64  `json:"created_time"`
	UpdatedTime int64  `json:"updated_time"`
	DeleteTime  int64  `json:"delete_time"`
}
