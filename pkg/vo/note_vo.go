package vo

type CreatedNoteReq struct {
	TopicId  string `json:"topic_id" binding:"required"`
	NoteType string `json:"note_type" binding:"required"`
	Content  string `json:"content" binding:"required"`
}

type CreatedNoteRes struct {
	Id          int64  `json:"id"`
	UserId      string `json:"user_id"`
	TopicId     string `json:"topic_id"`
	NoteType    string `json:"note_type"`
	Content     string `json:"content"`
	CreatedTime int64  `json:"created_time"`
	UpdatedTime int64  `json:"updated_time"`
	DeleteTime  int64  `json:"delete_time"`
}

type ListNoteReq struct {
	TopicId  string `json:"topic_id" form:"topic_id" binding:"required"`
	LasterId int64  `json:"laster_id,string" form:"laster_id"`
}

type ListNoteRes struct {
	Id          int64  `json:"id"`
	UserId      string `json:"user_id"`
	TopicId     string `json:"topic_id"`
	NoteType    string `json:"note_type"`
	Content     string `json:"content"`
	CreatedTime int64  `json:"created_time"`
	UpdatedTime int64  `json:"updated_time"`
	DeleteTime  int64  `json:"delete_time"`
}
type DeleteNoteReq struct {
	TopicId string `json:"topic_id" binding:"required"`
	NoteId  int64  `json:"note_id,string" binding:"required"`
}
