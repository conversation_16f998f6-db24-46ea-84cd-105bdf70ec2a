package model

import "chat_note_api/constant"

type UserModel struct {
	Id            string `json:"id"`
	Email         string `json:"email"`
	NickName      string `json:"nick_name"`
	Avatar        string `json:"avatar"`
	Password      string `json:"password"`
	VipExpireTime int64  `json:"vip_expire_time"`                          // vip到期时间
	UpdatedTime   int64  `json:"updated_time" gorm:"autoUpdateTime:milli"` // 使用时间戳毫秒数填充更新时间
	CreatedTime   int64  `json:"created_time" gorm:"autoCreateTime"`       // 使用时间戳秒数填充创建时间
	DeleteTime    int64  `json:"delete_time" `                             // 使用时间戳秒数填充创建时间
}

func (a UserModel) TableName() string {
	return constant.USER_TABLE_NAME
}
