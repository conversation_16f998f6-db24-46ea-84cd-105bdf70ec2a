package model

type Sysconfig struct {
	Id          int64  `json:"id" `
	CompanyId   uint   `json:"company_id" gorm:"type:int;not null"`
	Name        string `json:"name" gorm:"type:varchar(100);not null"`   // 配置名称
	Key         string `json:"key" gorm:"type:varchar(100);not null"`    // 配置key
	Value       string `json:"value" gorm:"type:varchar(100);not null"`  // 配置值
	Desc        string `json:"desc" gorm:"type:varchar(200);not null"`   // 描述
	UpdatedTime int64  `json:"updated_time" gorm:"autoUpdateTime:milli"` // 修改时间
	CreatedTime int64  `json:"created_time" gorm:"autoCreateTime:milli"` // 创建时间
	DeleteTime  int64  `json:"delete_time"`                              // 删除时间
}
