package model

import "chat_note_api/constant"

type VerificationCode struct {
	Id          string `json:"id"`
	Email       string `json:"email"`
	Phone       string `json:"phone"`
	Code        string `json:"code"`
	UpdatedTime int64  `json:"updated_time" gorm:"autoUpdateTime:milli"` // 使用时间戳毫秒数填充更新时间
	CreatedTime int64  `json:"created_time" gorm:"autoCreateTime"`       // 使用时间戳秒数填充创建时间
	DeleteTime  int64  `json:"delete_time" `                             // 使用时间戳秒数填充创建时间
}

func (v VerificationCode) TableName() string {
	return constant.VERIFICATION_CODE_TABLE_NAME
}
