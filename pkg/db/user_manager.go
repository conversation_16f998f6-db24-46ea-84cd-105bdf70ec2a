package db

import (
	"chat_note_api/constant"
	"chat_note_api/pkg/model"
	"chat_note_api/tools"
	"context"
	"fmt"
	"time"

	"github.com/aliyun/aliyun-tablestore-go-sdk/tablestore"
)

type userManager struct {
	client *tablestore.TableStoreClient
}

func (u *userManager) UpdateUser(ctx context.Context, email string, updateData map[string]interface{}) error {
	userModel, err := u.GetUserByEmail(ctx, email)
	if err != nil {
		return err
	}

	// 构建更新请求
	updateRowChange := &tablestore.UpdateRowChange{
		TableName: userModel.TableName(),
	}

	// 设置主键
	updateRowChange.PrimaryKey = &tablestore.PrimaryKey{
		PrimaryKeys: []*tablestore.PrimaryKeyColumn{{
			ColumnName: "id",
			Value:      userModel.Id,
		}},
	}

	// 设置更新数据
	for columnName, value := range updateData {
		updateRowChange.PutColumn(columnName, value)
	}

	// 设置更新条件
	updateRowChange.SetCondition(tablestore.RowExistenceExpectation_EXPECT_EXIST)

	// 发送更新请求
	updateRequest := &tablestore.UpdateRowRequest{
		UpdateRowChange: updateRowChange,
	}

	_, err = u.client.UpdateRow(updateRequest)
	return err
}

func (u *userManager) CreateUser(ctx context.Context, user *model.UserModel) error {

	putPk := &tablestore.PrimaryKey{}
	putPk.AddPrimaryKeyColumn("id", user.Id)

	putRowChange := &tablestore.PutRowChange{
		PrimaryKey: putPk,
		TableName:  user.TableName(),
	}

	putRowChange.AddColumn("email", user.Email)
	putRowChange.AddColumn("nick_name", user.NickName)
	putRowChange.AddColumn("avatar", user.Avatar)
	putRowChange.AddColumn("password", user.Password)
	putRowChange.AddColumn("vip_expire_time", time.Now().Unix())
	putRowChange.AddColumn("updated_time", time.Now().Unix())
	putRowChange.AddColumn("created_time", time.Now().Unix())
	putRowChange.AddColumn("delete_time", int64(0))

	putRowChange.SetCondition(tablestore.RowExistenceExpectation_IGNORE)

	putRowRequest := &tablestore.PutRowRequest{
		PutRowChange: putRowChange,
	}

	_, err := u.client.PutRow(putRowRequest)
	return err
}

func (u *userManager) GetUserByEmail(ctx context.Context, email string) (*model.UserModel, error) {
	id := tools.Md5(email)
	return u.GetUserById(ctx, id)
}

// GetUserById 通过用户ID获取用户信息
func (u *userManager) GetUserById(ctx context.Context, id string) (*model.UserModel, error) {

	// 构建 GetRowRequest，模仿 SearchRequest 的构建方式
	// getReq := u.buildGetRowRequest(userModel.TableName(), id)
	// 构建主键，确保类型正确
	getRowPK := &tablestore.PrimaryKey{
		PrimaryKeys: []*tablestore.PrimaryKeyColumn{{
			ColumnName: "id",
			Value:      id,
		}},
	}

	// 构建查询条件，类似 SearchRequest 的结构
	criteria := &tablestore.SingleRowQueryCriteria{
		PrimaryKey: getRowPK,
		TableName:  constant.USER_TABLE_NAME,
		MaxVersion: 1,
	}

	// 构建请求，类似 SearchRequest
	getReq := &tablestore.GetRowRequest{
		SingleRowQueryCriteria: criteria,
	}

	// 执行查询
	getResp, err := u.client.GetRow(getReq)
	if err != nil {
		return nil, err
	}

	// 解析用户数据
	user, err := u.parseUserFromGetRowResponse(getResp, id)
	if err != nil {
		return nil, err
	}

	return user, nil
}

// parseUserFromGetRowResponse 解析 GetRowResponse 为 UserModel
func (u *userManager) parseUserFromGetRowResponse(getResp *tablestore.GetRowResponse, primaryKeyValue string) (*model.UserModel, error) {
	if len(getResp.Columns) == 0 {
		return nil, fmt.Errorf("用户数据不存在")
	}

	// 解析用户数据
	user := &model.UserModel{}

	user.Id = primaryKeyValue

	// 解析属性列
	for _, col := range getResp.Columns {
		switch col.ColumnName {
		case "email":
			if email, ok := col.Value.(string); ok {
				user.Email = email
			}
		case "nick_name":
			if nickName, ok := col.Value.(string); ok {
				user.NickName = nickName
			}
		case "avatar":
			if avatar, ok := col.Value.(string); ok {
				user.Avatar = avatar
			}
		case "password":
			if password, ok := col.Value.(string); ok {
				user.Password = password
			}
		case "vip_expire_time":
			if expireTime, ok := col.Value.(int64); ok {
				user.VipExpireTime = expireTime
			}
		case "updated_time":
			if updatedTime, ok := col.Value.(int64); ok {
				user.UpdatedTime = updatedTime
			}
		case "created_time":
			if createdTime, ok := col.Value.(int64); ok {
				user.CreatedTime = createdTime
			}
		case "delete_time":
			if deleteTime, ok := col.Value.(int64); ok {
				user.DeleteTime = deleteTime
			}
		}
	}

	return user, nil
}

// CheckIndexExists 检查索引是否存在
func (u *userManager) CheckIndexExists(ctx context.Context, tableName, indexName string) error {
	// 尝试描述索引来检查是否存在
	describeSearchIndexRequest := &tablestore.DescribeSearchIndexRequest{
		TableName: tableName,
		IndexName: indexName,
	}

	_, err := u.client.DescribeSearchIndex(describeSearchIndexRequest)
	return err
}
