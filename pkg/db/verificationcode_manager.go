package db

import (
	"chat_note_api/constant"
	"chat_note_api/pkg/model"
	"chat_note_api/pkg/vo"
	"chat_note_api/tools"
	"context"
	"fmt"
	"time"

	"github.com/aliyun/aliyun-tablestore-go-sdk/tablestore"
)

type verificationCodeManager struct {
	client *tablestore.TableStoreClient
}

func (v *verificationCodeManager) SendVerificationCode(ctx context.Context, sendVerificationCodeReq *vo.SendVerificationCodeReq, code string) error {
	putPk := &tablestore.PrimaryKey{}
	putPk.AddPrimaryKeyColumn("id", tools.Md5(sendVerificationCodeReq.Email))
	putPk.AddPrimaryKeyColumn("type", string(sendVerificationCodeReq.VcType))

	putRowChange := &tablestore.PutRowChange{
		PrimaryKey: putPk,
		TableName:  constant.VERIFICATION_CODE_TABLE_NAME,
	}

	putRowChange.SetCondition(tablestore.RowExistenceExpectation_IGNORE)
	putRowChange.AddColumn("email", sendVerificationCodeReq.Email)
	putRowChange.AddColumn("code", code)
	putRowChange.AddColumn("phone", "")
	putRowChange.AddColumn("updated_time", time.Now().Unix())
	putRowChange.AddColumn("created_time", time.Now().Unix())
	putRowChange.AddColumn("delete_time", int64(0))

	putRowRequest := &tablestore.PutRowRequest{
		PutRowChange: putRowChange,
	}

	_, err := v.client.PutRow(putRowRequest)
	return err
}

func (v *verificationCodeManager) GetVerificationCode(ctx context.Context, vcType constant.VerificationCodeType, email string) (*model.VerificationCode, error) {
	id := tools.Md5(email)
	getRowPK := &tablestore.PrimaryKey{
		PrimaryKeys: []*tablestore.PrimaryKeyColumn{{
			ColumnName: "id",
			Value:      id,
		}, {
			ColumnName: "type",
			Value:      string(vcType),
		}},
	}

	// 构建查询条件，类似 SearchRequest 的结构
	criteria := &tablestore.SingleRowQueryCriteria{
		PrimaryKey: getRowPK,
		TableName:  constant.VERIFICATION_CODE_TABLE_NAME,
		MaxVersion: 1,
	}

	// 构建请求，类似 SearchRequest
	getReq := &tablestore.GetRowRequest{
		SingleRowQueryCriteria: criteria,
	}

	// 执行查询
	getResp, err := v.client.GetRow(getReq)
	if err != nil {
		return nil, err
	}

	if len(getResp.Columns) == 0 {
		return nil, fmt.Errorf("验证码不存在")
	}
	// 解析用户数据
	verificationCode, err := v.parseVerificationCodeFromGetRowResponse(getResp, id)
	if err != nil {
		return nil, err
	}

	return verificationCode, nil
}

func (v *verificationCodeManager) parseVerificationCodeFromGetRowResponse(getResp *tablestore.GetRowResponse, primaryKeyValue string) (*model.VerificationCode, error) {

	// 解析用户数据
	verificationCode := &model.VerificationCode{}

	verificationCode.Id = primaryKeyValue
	for _, col := range getResp.Columns {
		switch col.ColumnName {
		case "email":
			if email, ok := col.Value.(string); ok {
				verificationCode.Email = email
			}
		case "code":
			if code, ok := col.Value.(string); ok {
				verificationCode.Code = code
			}
		case "phone":
			if phone, ok := col.Value.(string); ok {
				verificationCode.Phone = phone
			}
		case "updated_time":
			if updatedTime, ok := col.Value.(int64); ok {
				verificationCode.UpdatedTime = updatedTime
			}
		case "created_time":
			if createdTime, ok := col.Value.(int64); ok {
				verificationCode.CreatedTime = createdTime
			}
		case "delete_time":
			if deleteTime, ok := col.Value.(int64); ok {
				verificationCode.DeleteTime = deleteTime
			}
		}
	}

	return verificationCode, nil
}
