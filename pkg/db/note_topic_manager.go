package db

import (
	"chat_note_api/constant"
	"chat_note_api/pkg/model"
	"chat_note_api/tools"
	"context"
	"time"

	"github.com/aliyun/aliyun-tablestore-go-sdk/tablestore"
)

type noteTopicManager struct {
	client *tablestore.TableStoreClient
}

func (n *noteTopicManager) ListNoteTopic(ctx context.Context, userId string) ([]*model.NoteTopicModel, error) {
	_, log := tools.GenContext(ctx)
	log.Info("ListNote")

	notes := make([]*model.NoteTopicModel, 0)

	// 使用do-while模式重构复杂的for循环
	lasterId := int64(0)
	for {
		getRangeResponse, err := n.GetNoteTopicBatch(userId, lasterId)
		if err != nil {
			return nil, err
		}

		// 如果没有更多数据，退出循环
		if len(getRangeResponse.Rows) == 0 {
			break
		}

		// 处理当前批次的数据
		if err := n.processNoteTopicBatch(getRangeResponse, &notes); err != nil {
			return nil, err
		}

		// 更新下一次查询的起始位置
		if getRangeResponse.NextStartPrimaryKey != nil {
			for _, primaryKeys := range getRangeResponse.NextStartPrimaryKey.PrimaryKeys {
				if primaryKeys.ColumnName == "id" {
					lasterId = primaryKeys.Value.(int64)
					break
				}
			}

		} else {
			break
		}
	}

	return notes, nil
}

// processNoteTopicBatch 处理单个批次的数据
func (n *noteTopicManager) processNoteTopicBatch(getRangeResponse *tablestore.GetRangeResponse, notes *[]*model.NoteTopicModel) error {
	for _, row := range getRangeResponse.Rows {
		note := n.parsenoteTopic(row)
		if note.DeleteTime > 0 {
			continue
		}
		*notes = append(*notes, note)
	}
	return nil
}

func (n *noteTopicManager) GetNoteTopicBatch(userId string, lasterId int64) (*tablestore.GetRangeResponse, error) {
	primaryKeys := []*tablestore.PrimaryKeyColumn{{ColumnName: "user_id", Value: userId}}

	start_pk := &tablestore.PrimaryKey{}
	if lasterId > 0 {
		start_pk.PrimaryKeys = append(primaryKeys, &tablestore.PrimaryKeyColumn{ColumnName: "id", Value: lasterId})
	} else {
		start_pk.PrimaryKeys = append(primaryKeys, &tablestore.PrimaryKeyColumn{ColumnName: "id", PrimaryKeyOption: tablestore.MAX})
	}

	end_pk := &tablestore.PrimaryKey{
		PrimaryKeys: append(primaryKeys, &tablestore.PrimaryKeyColumn{ColumnName: "id", PrimaryKeyOption: tablestore.MIN}),
	}

	rangeRowQueryCriteria := &tablestore.RangeRowQueryCriteria{
		TableName:       constant.NOTE_TOPIC_TABLE_NAME,
		StartPrimaryKey: start_pk,
		EndPrimaryKey:   end_pk,
		MaxVersion:      1,
		Limit:           100,
		Direction:       tablestore.BACKWARD,
	}

	getRangeRequest := &tablestore.GetRangeRequest{
		RangeRowQueryCriteria: rangeRowQueryCriteria,
	}

	getRangeResponse, err := n.client.GetRange(getRangeRequest)
	if err != nil {
		return nil, err
	}
	return getRangeResponse, nil
}

func (*noteTopicManager) parsenoteTopic(row *tablestore.Row) *model.NoteTopicModel {
	noteTopic := &model.NoteTopicModel{}

	for _, primaryKeyColumn := range row.PrimaryKey.PrimaryKeys {
		if primaryKeyColumn.ColumnName == "id" {
			noteTopic.Id = primaryKeyColumn.Value.(int64)
		}
		if primaryKeyColumn.ColumnName == "user_id" {
			noteTopic.UserId = primaryKeyColumn.Value.(string)
		}
	}
	for _, col := range row.Columns {
		switch col.ColumnName {
		case "title":
			if title, ok := col.Value.(string); ok {
				noteTopic.Title = title
			}
		case "description":
			if description, ok := col.Value.(string); ok {
				noteTopic.Description = description
			}
		case "avatar":
			if avatar, ok := col.Value.(string); ok {
				noteTopic.Avatar = avatar
			}
		case "updated_time":
			if updatedTime, ok := col.Value.(int64); ok {
				noteTopic.UpdatedTime = updatedTime
			}
		case "created_time":
			if createdTime, ok := col.Value.(int64); ok {
				noteTopic.CreatedTime = createdTime
			}
		case "delete_time":
			if deleteTime, ok := col.Value.(int64); ok {
				noteTopic.DeleteTime = deleteTime
			}
		}
	}
	return noteTopic
}

func (n *noteTopicManager) CreatedNoteTopic(ctx context.Context, noteTopicModel *model.NoteTopicModel) (*model.NoteTopicModel, error) {
	_, log := tools.GenContext(ctx)
	log.Info("CreatedNoteTopic")

	putPk := &tablestore.PrimaryKey{}
	putPk.AddPrimaryKeyColumn("user_id", noteTopicModel.UserId)
	putPk.AddPrimaryKeyColumnWithAutoIncrement("id")

	putRowChange := &tablestore.PutRowChange{
		PrimaryKey: putPk,
		TableName:  constant.NOTE_TOPIC_TABLE_NAME,
		ReturnType: tablestore.ReturnType_RT_PK, // 设置返回主键，否则插入成功无法获取自增id
	}

	putRowChange.SetCondition(tablestore.RowExistenceExpectation_IGNORE)
	putRowChange.AddColumn("title", noteTopicModel.Title)
	putRowChange.AddColumn("description", noteTopicModel.Description)
	putRowChange.AddColumn("avatar", noteTopicModel.Avatar)
	putRowChange.AddColumn("updated_time", time.Now().Unix())
	putRowChange.AddColumn("created_time", time.Now().Unix())
	putRowChange.AddColumn("delete_time", int64(0))

	putRowRequest := &tablestore.PutRowRequest{
		PutRowChange: putRowChange,
	}

	response, err := n.client.PutRow(putRowRequest)
	if err != nil {
		return nil, err
	}

	for _, primaryKey := range response.PrimaryKey.PrimaryKeys {
		if primaryKey.ColumnName == "id" {
			noteTopicModel.Id = primaryKey.Value.(int64)
			break
		}
	}
	return noteTopicModel, err

}

// func (n *noteTopicManager) GetNoteTopics(ctx context.Context, userId string, noteTopicIds []string) ([]*model.NoteTopicModel, error) {
// 	noteTopics := make([]*model.NoteTopicModel, 0)

// 	_, log := tools.GenContext(ctx)
// 	log.Info("GetNoteTopic")

// 	batchGetReq := &tablestore.BatchGetRowRequest{}
// 	mqCriteria := &tablestore.MultiRowQueryCriteria{}

// 	for _, noteTopicId := range noteTopicIds {
// 		pkToGet := new(tablestore.PrimaryKey)
// 		pkToGet.AddPrimaryKeyColumn("user_id", userId)
// 		pkToGet.AddPrimaryKeyColumn("id", noteTopicId)
// 		mqCriteria.AddRow(pkToGet)
// 		mqCriteria.MaxVersion = 1
// 	}

// 	mqCriteria.TableName = constant.NOTE_TOPIC_TABLE_NAME
// 	batchGetReq.MultiRowQueryCriteria = append(batchGetReq.MultiRowQueryCriteria, mqCriteria)
// 	batchGetResponse, err := n.client.BatchGetRow(batchGetReq)
// 	if err != nil {
// 		log.Error(err)
// 		return nil, err
// 	}
// 	for _, rows := range batchGetResponse.TableToRowsResult {
// 		if len(rows) < 1 {
// 			continue
// 		}
// 		for _, row := range rows {
// 			noteTopic := n.parsenoteTopic(row)
// 			if noteTopic.DeleteTime > 0 {
// 				continue
// 			}
// 			noteTopics = append(noteTopics, noteTopic)
// 		}
// 	}

// 	// 构建查询条件，类似 SearchRequest 的
// 	return noteTopics, nil
// }
