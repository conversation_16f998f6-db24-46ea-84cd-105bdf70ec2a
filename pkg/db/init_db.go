package db

import (
	"os"

	"github.com/alibabacloud-go/tea/tea"
	"github.com/aliyun/aliyun-tablestore-go-sdk/tablestore"
	"github.com/aliyun/credentials-go/credentials"
	"github.com/sirupsen/logrus"
)

var (
	UserManager             *userManager
	SysconfigManager        *sysconfigManager
	VerificationCodeManager *verificationCodeManager
	NoteTopicManager        *noteTopicManager
	NoteManager             *noteManager
)

func newCredentials() (*credentials.CredentialModel, error) {
	credential, err := credentials.NewCredential(nil)
	if err != nil {
		return nil, err
	}
	return credential.GetCredential()
}

func InitDBManagerMain() error {
	_, err := InitDBManager()
	return err
}
func InitDBManager() (*tablestore.TableStoreClient, error) {
	// yourInstanceName 填写您的实例名称
	instanceName := os.Getenv("TABLESTORE_INSTANCENAME")
	// yourEndpoint 填写您的实例访问地址
	endpoint := os.Getenv("TABLESTORE_ENDPOINT")
	credential, err := newCredentials()
	if err != nil {
		logrus.Errorf("get credentials failed, %v", err)
		return nil, err
	}

	// 初始化表格存储客户端
	client := tablestore.NewClient(endpoint,
		instanceName,
		tea.StringValue(credential.AccessKeyId),
		tea.StringValue(credential.AccessKeySecret))

	UserManager = &userManager{client: client}
	SysconfigManager = &sysconfigManager{client: client}
	VerificationCodeManager = &verificationCodeManager{client: client}
	NoteTopicManager = &noteTopicManager{client: client}
	NoteManager = &noteManager{client: client}
	return client, nil
}
