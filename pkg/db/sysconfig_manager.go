package db

import (
	"chat_note_api/pkg/model"
	"context"

	"github.com/aliyun/aliyun-tablestore-go-sdk/tablestore"
)

type sysconfigManager struct {
	client *tablestore.TableStoreClient
}

// AddSysConfig sys config
func (m *sysconfigManager) AddSysConfig(ctx context.Context, sysConfig *model.Sysconfig) error {
	return nil
}

func (m *sysconfigManager) UpdateSysConfig(ctx context.Context, sysConfig *model.Sysconfig) error {
	return nil
}

func (m *sysconfigManager) FindSysConfig(sysConfig model.Sysconfig) (*model.Sysconfig, error) {
	return nil, nil
}
