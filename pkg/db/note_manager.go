package db

import (
	"chat_note_api/constant"
	"chat_note_api/pkg/model"
	"chat_note_api/pkg/vo"
	"chat_note_api/tools"
	"context"

	"github.com/aliyun/aliyun-tablestore-go-sdk/tablestore"
)

type noteManager struct {
	client *tablestore.TableStoreClient
}

func (n *noteManager) CreatedNote(ctx context.Context, note *model.Note) (*model.Note, error) {
	putPk := &tablestore.PrimaryKey{}
	putPk.AddPrimaryKeyColumn("user_id", note.UserId)
	putPk.AddPrimaryKeyColumn("topic_id", note.TopicId)
	putPk.AddPrimaryKeyColumnWithAutoIncrement("id")

	putRowChange := &tablestore.PutRowChange{
		PrimaryKey: putPk,
		TableName:  constant.NOTE_TABLE_NAME,
		ReturnType: tablestore.ReturnType_RT_PK, // 设置返回主键，否则插入成功无法获取自增id
	}

	putRowChange.SetCondition(tablestore.RowExistenceExpectation_IGNORE)
	putRowChange.AddColumn("note_type", note.NoteType)
	putRowChange.AddColumn("content", note.Content)
	putRowChange.AddColumn("updated_time", note.UpdatedTime)
	putRowChange.AddColumn("created_time", note.CreatedTime)
	putRowChange.AddColumn("delete_time", note.DeleteTime)

	putRowRequest := &tablestore.PutRowRequest{
		PutRowChange: putRowChange,
	}

	putRowResponse, err := n.client.PutRow(putRowRequest)
	if err != nil {
		return nil, err
	}

	for _, primaryKey := range putRowResponse.PrimaryKey.PrimaryKeys {
		if primaryKey.ColumnName == "id" {
			note.Id = primaryKey.Value.(int64)
			break
		}
	}
	return note, nil
}

func (n *noteManager) ListNote(ctx context.Context, userId string, listNoteReq *vo.ListNoteReq) ([]*model.Note, int64, error) {
	_, log := tools.GenContext(ctx)
	log.Info("ListNote")

	primaryKeys := []*tablestore.PrimaryKeyColumn{{
		ColumnName: "user_id", Value: userId},
		{ColumnName: "topic_id", Value: listNoteReq.TopicId},
	}

	start_pk := &tablestore.PrimaryKey{}
	if listNoteReq.LasterId > 0 {
		start_pk.PrimaryKeys = append(primaryKeys, &tablestore.PrimaryKeyColumn{ColumnName: "id", Value: listNoteReq.LasterId})
	} else {
		start_pk.PrimaryKeys = append(primaryKeys, &tablestore.PrimaryKeyColumn{ColumnName: "id", PrimaryKeyOption: tablestore.MAX})
	}

	end_pk := &tablestore.PrimaryKey{
		PrimaryKeys: append(primaryKeys, &tablestore.PrimaryKeyColumn{ColumnName: "id", PrimaryKeyOption: tablestore.MIN}),
	}

	rangeRowQueryCriteria := &tablestore.RangeRowQueryCriteria{
		TableName:       constant.NOTE_TABLE_NAME,
		StartPrimaryKey: start_pk,
		EndPrimaryKey:   end_pk,
		MaxVersion:      1,
		Limit:           10,
		Direction:       tablestore.BACKWARD,
	}

	getRangeRequest := &tablestore.GetRangeRequest{
		RangeRowQueryCriteria: rangeRowQueryCriteria,
	}

	getRangeResponse, err := n.client.GetRange(getRangeRequest)
	if err != nil {
		return nil, 0, err
	}

	notes := make([]*model.Note, 0)
	for _, row := range getRangeResponse.Rows {
		note := n.parseNote(row)
		if note.DeleteTime > 0 {
			continue
		}
		notes = append(notes, note)
	}
	lasterId := int64(0)
	if getRangeResponse.NextStartPrimaryKey != nil {
		for _, primaryKeys := range getRangeResponse.NextStartPrimaryKey.PrimaryKeys {
			if primaryKeys.ColumnName == "id" {
				lasterId = primaryKeys.Value.(int64)
				break
			}
		}
	}
	return notes, lasterId, nil

}

func (n *noteManager) parseNote(row *tablestore.Row) *model.Note {
	note := &model.Note{}

	for _, primaryKeyColumn := range row.PrimaryKey.PrimaryKeys {
		if primaryKeyColumn.ColumnName == "id" {
			note.Id = primaryKeyColumn.Value.(int64)
		}
		if primaryKeyColumn.ColumnName == "user_id" {
			note.UserId = primaryKeyColumn.Value.(string)
		}
		if primaryKeyColumn.ColumnName == "topic_id" {
			note.TopicId = primaryKeyColumn.Value.(string)
		}
	}

	for _, col := range row.Columns {
		switch col.ColumnName {
		case "note_type":
			if noteType, ok := col.Value.(string); ok {
				note.NoteType = noteType
			}
		case "content":
			if content, ok := col.Value.(string); ok {
				note.Content = content
			}
		case "updated_time":
			if updatedTime, ok := col.Value.(int64); ok {
				note.UpdatedTime = updatedTime
			}
		case "created_time":
			if createdTime, ok := col.Value.(int64); ok {
				note.CreatedTime = createdTime
			}
		case "delete_time":
			if deleteTime, ok := col.Value.(int64); ok {
				note.DeleteTime = deleteTime
			}
		}
	}

	return note
}

func (n *noteManager) DeleteNote(ctx context.Context, userId string, deleteNoteReq *vo.DeleteNoteReq) error {
	_, log := tools.GenContext(ctx)
	log.Info("DeleteNote")

	deletePk := &tablestore.PrimaryKey{}
	deletePk.AddPrimaryKeyColumn("user_id", userId)
	deletePk.AddPrimaryKeyColumn("topic_id", deleteNoteReq.TopicId)
	deletePk.AddPrimaryKeyColumn("id", deleteNoteReq.NoteId)

	deleteRowChange := &tablestore.DeleteRowChange{
		PrimaryKey: deletePk,
		TableName:  constant.NOTE_TABLE_NAME,
		Condition: &tablestore.RowCondition{
			RowExistenceExpectation: tablestore.RowExistenceExpectation_EXPECT_EXIST,
		},
	}

	deleteRowRequest := &tablestore.DeleteRowRequest{
		DeleteRowChange: deleteRowChange,
	}
	//deleteRowReq.DeleteRowChange.SetCondition(tablestore.RowExistenceExpectation_IGNORE)

	_, err := n.client.DeleteRow(deleteRowRequest)
	return err
}
