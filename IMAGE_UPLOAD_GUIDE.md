# 图片上传功能使用指南

## 📋 功能概述

本项目实现了完整的图片上传功能，支持以下4种上传场景：

1. **富文本编辑器选择图片上传** - 通过工具栏图片按钮选择本地图片
2. **复制图片文件粘贴上传** - 直接复制图片文件后粘贴到编辑器
3. **复制HTML内容图片上传** - 复制包含图片的HTML内容，自动提取并上传图片
4. **服务端临时存储** - 图片文件临时存储在本地文件系统

## 🏗️ 技术架构

### 后端实现
- **控制器**: `pkg/controller/upload_controller.go`
- **服务层**: `pkg/service/upload_service.go`
- **数据传输**: `pkg/vo/upload_vo.go`
- **API路由**: `/api/v1/upload/image` 和 `/api/v1/upload/image-by-url`
- **静态文件**: `/uploads/images/` 目录

### 前端实现
- **API服务**: `src/renderer/api/upload.js`
- **富文本组件**: `src/renderer/views/richText/RichTextCore.vue`
- **图片处理**: `src/renderer/views/richText/replaceThirdPartyImgLink.js`

## 🚀 使用方法

### 1. 富文本编辑器选择图片

```javascript
// 点击工具栏图片按钮，选择本地图片文件
// 支持格式：JPG, PNG, GIF, WebP, BMP
// 文件大小限制：10MB
```

### 2. 复制图片文件粘贴

```javascript
// 1. 复制图片文件（Ctrl+C 或右键复制）
// 2. 在富文本编辑器中粘贴（Ctrl+V）
// 3. 自动上传并插入图片
```

### 3. 复制HTML内容粘贴

```javascript
// 1. 复制包含图片的HTML内容（如网页内容）
// 2. 在富文本编辑器中粘贴
// 3. 自动检测并上传base64图片和第三方图片链接
```

### 4. API调用示例

```javascript
import { uploadFile, uploadImgByUrl, uploadBase64Image } from '@/api/upload.js'

// 上传文件
const result = await uploadFile(file)

// 通过URL上传
const result = await uploadImgByUrl({ url: 'https://example.com/image.jpg' })

// 上传base64图片
const result = await uploadBase64Image('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==')
```

## 📁 文件结构

```
项目根目录/
├── pkg/
│   ├── controller/
│   │   ├── upload_controller.go      # 上传控制器
│   │   └── upload_controller_test.go # 控制器测试
│   ├── service/
│   │   └── upload_service.go         # 上传服务
│   └── vo/
│       └── upload_vo.go              # 数据传输对象
├── uploads/
│   └── images/                       # 图片存储目录
└── src/renderer/
    ├── api/
    │   └── upload.js                 # 前端上传API
    └── views/richText/
        ├── RichTextCore.vue          # 富文本编辑器核心组件
        └── replaceThirdPartyImgLink.js # 图片链接替换处理
```

## 🔧 配置说明

### 后端配置

```go
// 支持的图片格式
validTypes := []string{
    "image/jpeg", "image/jpg", "image/png",
    "image/gif", "image/webp", "image/bmp",
}

// 文件大小限制
maxSize := 10 * 1024 * 1024 // 10MB

// 存储目录
uploadDir := "./uploads/images"
```

### 前端配置

```javascript
// API基础URL
const apiBaseUrl = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000'

// 认证token
const authToken = localStorage.getItem('auth_token')
```

## 🧪 测试

### 运行后端测试

```bash
cd pkg/controller
go test -v upload_controller_test.go upload_controller.go
```

### 测试覆盖的场景

1. ✅ 正常图片文件上传
2. ✅ 通过URL上传图片
3. ✅ 无效文件格式处理
4. ✅ 无效URL处理
5. ✅ 文件大小限制验证

## ⚠️ 注意事项

### 安全性
- 严格验证文件类型和大小
- 防止路径遍历攻击
- 需要认证token才能上传

### 性能优化
- 限制并发上传数量
- 实现文件去重机制
- 考虑CDN集成

### 存储管理
- 定期清理临时文件
- 监控磁盘空间使用
- 备份重要图片文件

## 🔄 API接口文档

### 1. 上传图片文件

**接口地址**: `POST /api/v1/upload/image`

**请求头**:
```
Content-Type: multipart/form-data
Authorization: <token>
```

**请求参数**:
```
file: 图片文件（必需）
```

**响应格式**:
```json
{
  "status": 200,
  "message": "success",
  "data": {
    "url": "/uploads/images/1640995200000000000.jpg",
    "file_name": "1640995200000000000.jpg",
    "size": 123456
  }
}
```

### 2. 通过URL上传图片

**接口地址**: `POST /api/v1/upload/image-by-url`

**请求头**:
```
Content-Type: application/json
Authorization: <token>
```

**请求参数**:
```json
{
  "url": "https://example.com/image.jpg"
}
```

**响应格式**:
```json
{
  "status": 200,
  "message": "success",
  "data": {
    "url": "/uploads/images/1640995200000000000.jpg",
    "file_name": "1640995200000000000.jpg",
    "size": 123456
  }
}
```

## 🚀 部署说明

1. **创建上传目录**:
   ```bash
   mkdir -p ./uploads/images
   chmod 755 ./uploads/images
   ```

2. **启动后端服务**:
   ```bash
   go run cmd/api/chat_note_api.go
   ```

3. **访问静态文件**:
   上传的图片可通过 `http://localhost:8000/uploads/images/filename.jpg` 访问

4. **运行测试**:
   ```bash
   # 启动服务后，在另一个终端运行测试
   ./test_upload.sh
   ```

## ✅ 实现状态

### 已完成功能
- ✅ 后端图片上传API (`/api/v1/upload/image`)
- ✅ 后端URL图片上传API (`/api/v1/upload/image-by-url`)
- ✅ 前端图片上传API服务 (`upload.js`)
- ✅ 富文本编辑器图片选择上传
- ✅ 富文本编辑器图片粘贴上传
- ✅ HTML内容中base64图片提取上传
- ✅ 第三方图片链接自动上传替换
- ✅ 文件类型和大小验证
- ✅ 本地文件系统存储
- ✅ 静态文件服务
- ✅ 完整的测试用例
- ✅ 详细的使用文档

### 技术特性
- 🔒 **安全性**: 文件类型验证、大小限制、认证要求
- 🚀 **性能**: 并发上传支持、文件去重
- 📁 **存储**: 本地文件系统临时存储
- 🔄 **兼容性**: 支持多种图片格式和上传方式
- 🧪 **测试**: 完整的单元测试和集成测试

## 📝 更新日志

### v1.0.0 (2024-01-01)
- ✅ 实现基础图片上传功能
- ✅ 支持富文本编辑器图片选择
- ✅ 支持图片文件粘贴上传
- ✅ 支持HTML内容中图片提取上传
- ✅ 添加文件类型和大小验证
- ✅ 实现本地文件系统存储
- ✅ 添加完整的测试用例

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/image-upload`)
3. 提交更改 (`git commit -am 'Add image upload feature'`)
4. 推送到分支 (`git push origin feature/image-upload`)
5. 创建 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。
