# 自动更新故障排除指南

本文档提供ChatNote自动更新功能的常见问题解决方案和调试方法。

## 🔍 诊断工具

### 1. 快速诊断脚本

创建并运行诊断脚本：

```bash
# 创建诊断脚本
cat > diagnose-update.js << 'EOF'
const { autoUpdater } = require('electron-updater')
const log = require('electron-log')

autoUpdater.logger = log
autoUpdater.logger.transports.file.level = 'info'

console.log('开始诊断自动更新功能...')
console.log('当前版本:', require('./package.json').version)
console.log('更新服务器:', process.env.UPDATE_SERVER_URL)

autoUpdater.checkForUpdatesAndNotify()
  .then(result => {
    console.log('检查结果:', result)
  })
  .catch(error => {
    console.error('检查失败:', error)
  })
EOF

# 运行诊断
node diagnose-update.js
```

### 2. OSS连接测试

```bash
# 测试OSS连接
npm run setup:oss

# 手动测试更新清单
curl -v https://your-bucket.oss-cn-hangzhou.aliyuncs.com/releases/latest.yml
```

## ❌ 常见错误及解决方案

### 1. OSS相关错误

#### 错误: `AccessDenied`
```
Error: AccessDenied: The request signature we calculated does not match
```

**原因**: AccessKey权限不足或配置错误

**解决方案**:
```bash
# 1. 检查环境变量
echo $OSS_ACCESS_KEY_ID
echo $OSS_ACCESS_KEY_SECRET
echo $OSS_BUCKET

# 2. 验证RAM用户权限
# 确保用户具有以下权限:
# - AliyunOSSFullAccess 或
# - 自定义策略包含 oss:GetObject, oss:PutObject, oss:DeleteObject

# 3. 重新配置
cp .env.example .env
# 编辑 .env 文件，填入正确的配置
```

#### 错误: `NoSuchBucket`
```
Error: NoSuchBucket: The specified bucket does not exist
```

**原因**: 存储桶不存在或名称错误

**解决方案**:
```bash
# 1. 检查存储桶名称
echo $OSS_BUCKET

# 2. 在阿里云控制台确认存储桶存在
# 3. 检查区域设置是否正确
echo $OSS_REGION
```

#### 错误: `InvalidAccessKeyId`
```
Error: InvalidAccessKeyId: The OSS Access Key Id you provided does not exist
```

**原因**: AccessKey ID错误或已失效

**解决方案**:
```bash
# 1. 重新获取AccessKey
# 2. 检查AccessKey是否激活
# 3. 确认AccessKey未过期
```

### 2. 构建相关错误

#### 错误: 构建失败
```
Error: Cannot resolve dependency
```

**解决方案**:
```bash
# 1. 清理并重新安装依赖
rm -rf node_modules package-lock.json
npm install

# 2. 检查Node.js版本
node --version  # 需要 18+

# 3. 清理构建缓存
rm -rf out release
npm run build:prod
```

#### 错误: 代码签名失败
```
Error: Command failed: codesign
```

**解决方案**:
```bash
# 1. 检查证书配置
security find-identity -v -p codesigning

# 2. 临时禁用代码签名（仅开发环境）
export CSC_IDENTITY_AUTO_DISCOVERY=false

# 3. 配置正确的证书
export CSC_LINK=path/to/certificate.p12
export CSC_KEY_PASSWORD=certificate_password
```

### 3. 更新检查错误

#### 错误: 网络连接失败
```
Error: net::ERR_NETWORK_CHANGED
```

**解决方案**:
```bash
# 1. 检查网络连接
ping your-bucket.oss-cn-hangzhou.aliyuncs.com

# 2. 检查防火墙设置
# 3. 尝试使用代理
export HTTP_PROXY=http://proxy:port
export HTTPS_PROXY=http://proxy:port
```

#### 错误: 更新清单格式错误
```
Error: Cannot parse update info
```

**解决方案**:
```bash
# 1. 验证清单文件格式
curl -s https://your-bucket.oss-cn-hangzhou.aliyuncs.com/releases/latest.yml | head -20

# 2. 重新生成清单文件
node scripts/generate-update-manifest.js

# 3. 检查YAML格式
npm install -g js-yaml
js-yaml ./release/latest.yml
```

### 4. 下载和安装错误

#### 错误: 下载中断
```
Error: Download interrupted
```

**解决方案**:
```bash
# 1. 检查网络稳定性
# 2. 增加重试次数
# 在主进程中添加:
autoUpdater.autoDownload = false
autoUpdater.autoInstallOnAppQuit = true

# 3. 手动重试下载
autoUpdater.downloadUpdate()
```

#### 错误: 文件校验失败
```
Error: SHA256 checksum mismatch
```

**解决方案**:
```bash
# 1. 重新上传文件
npm run upload:oss

# 2. 验证本地文件完整性
shasum -a 256 ./release/ChatNote-1.0.0.dmg

# 3. 清理缓存重新下载
rm -rf ~/Library/Caches/your-app-name/pending
```

## 🔧 调试方法

### 1. 启用详细日志

```javascript
// 在主进程中添加
const log = require('electron-log')
autoUpdater.logger = log
autoUpdater.logger.transports.file.level = 'info'

// 或设置环境变量
process.env.ELECTRON_ENABLE_LOGGING = true
```

### 2. 监听所有更新事件

```javascript
autoUpdater.on('checking-for-update', () => {
  console.log('Checking for update...')
})

autoUpdater.on('update-available', (info) => {
  console.log('Update available:', info)
})

autoUpdater.on('update-not-available', (info) => {
  console.log('Update not available:', info)
})

autoUpdater.on('error', (err) => {
  console.log('Error in auto-updater:', err)
})

autoUpdater.on('download-progress', (progressObj) => {
  console.log('Download progress:', progressObj)
})

autoUpdater.on('update-downloaded', (info) => {
  console.log('Update downloaded:', info)
})
```

### 3. 手动测试更新流程

```bash
# 1. 构建测试版本
npm version patch --no-git-tag-version
npm run build:release

# 2. 上传到测试环境
UPDATE_SERVER_URL=https://test-bucket.oss-cn-hangzhou.aliyuncs.com/releases npm run upload:oss

# 3. 安装旧版本应用
# 4. 修改应用配置指向测试服务器
# 5. 触发更新检查
```

## 📊 日志分析

### 1. 日志位置

```bash
# macOS
~/Library/Logs/ChatNote/main.log

# Windows
%USERPROFILE%\AppData\Roaming\ChatNote\logs\main.log

# Linux
~/.config/ChatNote/logs/main.log
```

### 2. 关键日志信息

查找以下关键词：
- `checking-for-update`: 开始检查更新
- `update-available`: 发现新版本
- `download-progress`: 下载进度
- `update-downloaded`: 下载完成
- `error`: 错误信息

### 3. 日志分析脚本

```bash
# 提取更新相关日志
grep -i "update\|error" ~/Library/Logs/ChatNote/main.log | tail -50

# 分析下载进度
grep "download-progress" ~/Library/Logs/ChatNote/main.log | tail -10
```

## 🛠️ 高级调试

### 1. 模拟不同网络环境

```bash
# 模拟慢速网络
sudo tc qdisc add dev eth0 root netem delay 100ms rate 1mbit

# 模拟网络中断
sudo tc qdisc add dev eth0 root netem loss 50%

# 恢复网络
sudo tc qdisc del dev eth0 root
```

### 2. 测试不同版本场景

```bash
# 测试版本回退
npm version 0.9.0 --no-git-tag-version

# 测试跨大版本更新
npm version 2.0.0 --no-git-tag-version

# 测试预发布版本
npm version 1.0.0-beta.1 --no-git-tag-version
```

### 3. 性能分析

```javascript
// 添加性能监控
const startTime = Date.now()

autoUpdater.on('update-downloaded', () => {
  const downloadTime = Date.now() - startTime
  console.log(`Download completed in ${downloadTime}ms`)
})
```

## 📞 获取帮助

如果以上方法都无法解决问题，请：

1. 收集完整的错误日志
2. 记录复现步骤
3. 提供系统环境信息
4. 创建GitHub Issue或联系技术支持

### 环境信息收集脚本

```bash
echo "=== 系统信息 ==="
uname -a
echo "=== Node.js版本 ==="
node --version
echo "=== npm版本 ==="
npm --version
echo "=== 应用版本 ==="
cat package.json | grep version
echo "=== 环境变量 ==="
env | grep OSS
echo "=== 网络测试 ==="
ping -c 3 your-bucket.oss-cn-hangzhou.aliyuncs.com
```
