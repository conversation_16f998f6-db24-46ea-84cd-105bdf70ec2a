# 阿里云OSS上传操作指南

本指南详细说明如何配置和使用阿里云OSS作为Electron应用的自动更新服务器。

## 前置准备

### 1. 创建阿里云OSS存储桶

1. 登录[阿里云控制台](https://oss.console.aliyun.com/)
2. 创建新的存储桶（Bucket）
3. 选择合适的地域（建议选择离用户最近的地域）
4. 设置存储类型为"标准存储"
5. 设置读写权限为"公共读"

### 2. 获取访问密钥

1. 进入[访问控制RAM控制台](https://ram.console.aliyun.com/)
2. 创建新用户或使用现有用户
3. 为用户分配OSS相关权限：
   - `AliyunOSSFullAccess`（完全访问权限）
   - 或者自定义权限策略（仅包含必要的OSS操作权限）
4. 获取AccessKey ID和AccessKey Secret

## 配置步骤

### 1. 环境变量配置

复制 `.env.example` 文件为 `.env`，并填入实际配置：

```bash
cp .env.example .env
```

编辑 `.env` 文件：

```env
# 阿里云OSS配置
OSS_ACCESS_KEY_ID=your_actual_access_key_id
OSS_ACCESS_KEY_SECRET=your_actual_access_key_secret
OSS_BUCKET=your_bucket_name
OSS_REGION=oss-cn-hangzhou
UPDATE_SERVER_URL=https://your_bucket_name.oss-cn-hangzhou.aliyuncs.com/releases
```

### 2. 安装依赖

确保已安装必要的依赖：

```bash
npm install
```

### 3. 初始化OSS配置

运行OSS设置脚本：

```bash
node scripts/setup-oss.js
```

此脚本会：
- 验证OSS连接
- 配置CORS规则
- 设置存储桶访问策略
- 创建必要的目录结构
- 进行连接测试

## 使用方法

### 1. 构建和发布

#### 发布当前平台版本：
```bash
npm run release
```

#### 发布所有平台版本：
```bash
npm run release:all
```

#### 仅构建不上传：
```bash
npm run build:release
```

#### 仅上传已构建的文件：
```bash
npm run upload:oss
```

### 2. 版本管理

#### 升级补丁版本（1.0.0 → 1.0.1）：
```bash
npm run version:patch
```

#### 升级次要版本（1.0.0 → 1.1.0）：
```bash
npm run version:minor
```

#### 升级主要版本（1.0.0 → 2.0.0）：
```bash
npm run version:major
```

## 目录结构

OSS存储桶中的文件结构：

```
your-bucket/
├── releases/
│   ├── latest.yml              # 最新版本清单
│   ├── 1.0.0/
│   │   ├── ChatNote-1.0.0.dmg  # macOS安装包
│   │   ├── ChatNote-1.0.0.exe  # Windows安装包
│   │   ├── ChatNote-1.0.0.AppImage # Linux安装包
│   │   ├── latest.yml          # 版本清单
│   │   └── latest-mac.yml      # macOS更新清单
│   ├── 1.0.1/
│   │   └── ...
│   └── archive/                # 归档版本
```

## 自动更新流程

1. **应用启动**：应用启动5秒后自动检查更新
2. **检查更新**：向OSS服务器请求 `latest.yml` 文件
3. **版本比较**：比较本地版本与服务器版本
4. **下载更新**：如有新版本，提示用户下载
5. **安装更新**：下载完成后提示用户重启安装

## 手动操作

### 上传单个文件

```javascript
const OSS = require('ali-oss')

const client = new OSS({
  region: 'oss-cn-hangzhou',
  accessKeyId: 'your_access_key_id',
  accessKeySecret: 'your_access_key_secret',
  bucket: 'your_bucket_name'
})

// 上传文件
await client.put('releases/1.0.0/app.dmg', './release/app.dmg')
```

### 生成更新清单

```javascript
const manifest = {
  version: '1.0.0',
  releaseDate: '2024-01-01T00:00:00.000Z',
  files: [
    {
      name: 'ChatNote-1.0.0.dmg',
      url: 'https://bucket.oss-cn-hangzhou.aliyuncs.com/releases/1.0.0/ChatNote-1.0.0.dmg',
      size: 123456789,
      sha256: 'abc123...'
    }
  ]
}
```

## 故障排除

### 常见错误

1. **AccessDenied**
   - 检查AccessKey权限
   - 确认存储桶访问策略

2. **NoSuchBucket**
   - 确认存储桶名称正确
   - 确认存储桶已创建

3. **InvalidAccessKeyId**
   - 检查AccessKey ID是否正确
   - 确认AccessKey未过期

4. **上传失败**
   - 检查网络连接
   - 确认文件路径正确
   - 检查存储桶空间是否充足

### 调试方法

1. **启用详细日志**：
   ```bash
   DEBUG=* npm run upload:oss
   ```

2. **测试OSS连接**：
   ```bash
   node scripts/setup-oss.js
   ```

3. **手动验证上传**：
   访问 `https://your_bucket.oss-cn-hangzhou.aliyuncs.com/releases/latest.yml`

## 安全建议

1. **最小权限原则**：仅授予必要的OSS权限
2. **定期轮换密钥**：定期更新AccessKey
3. **环境变量保护**：不要将 `.env` 文件提交到版本控制
4. **HTTPS访问**：确保使用HTTPS访问OSS资源
5. **访问日志**：启用OSS访问日志监控

## 成本优化

1. **生命周期管理**：设置旧版本文件自动删除规则
2. **存储类型**：考虑使用低频访问存储类型存储旧版本
3. **CDN加速**：配置阿里云CDN加速下载
4. **压缩传输**：启用Gzip压缩减少传输成本
