# 环境配置说明

## 概述

项目支持多环境配置，通过环境变量来管理不同环境下的API地址和其他配置项。

## 环境文件

项目包含以下环境配置文件：

- `.env.development` - 开发环境配置
- `.env.test` - 测试环境配置  
- `.env.production` - 生产环境配置
- `.env.example` - 环境变量示例文件

## 环境变量说明

### API配置
- `VITE_API_BASE_URL` - API服务器基础地址

### 应用配置
- `VITE_APP_NAME` - 应用名称
- `VITE_APP_VERSION` - 应用版本

### 功能开关
- `VITE_AUTH_ENABLED` - 是否启用认证功能
- `VITE_ENABLE_API_SYNC` - 是否启用API同步
- `VITE_ENABLE_OFFLINE_MODE` - 是否启用离线模式
- `VITE_ENABLE_AUTO_SAVE` - 是否启用自动保存

## 不同环境的API地址

### 开发环境
```
VITE_API_BASE_URL=http://localhost:8000
```

### 测试环境
```
VITE_API_BASE_URL=https://test-api.chatnote.com
```

### 生产环境
```
VITE_API_BASE_URL=https://api.chatnote.com
```

## 使用方法

### 开发模式

```bash
# 使用开发环境配置
npm run dev

# 使用测试环境配置
npm run dev:test

# 使用生产环境配置
npm run dev:prod
```

### 构建模式

```bash
# 构建开发版本
npm run build:dev

# 构建测试版本
npm run build:test

# 构建生产版本
npm run build:prod
```

### Electron构建

```bash
# 构建开发版本的Electron应用
npm run electron:build:dev

# 构建测试版本的Electron应用
npm run electron:build:test

# 构建生产版本的Electron应用
npm run electron:build
```

## 配置文件结构

项目使用统一的配置管理系统：

- `src/config/index.ts` - 主配置文件，从环境变量读取配置
- `src/utils/api.ts` - API工具类，统一管理API请求

## API使用示例

```typescript
import { apiClient, API_ENDPOINTS } from '@/utils/api'

// 发送验证码
const response = await apiClient.post(API_ENDPOINTS.SEND_VERIFICATION_CODE, { email })

// 用户登录
const response = await apiClient.post(API_ENDPOINTS.USER_LOGIN, {
  email: form.email,
  password: form.password
})
```

## 注意事项

1. 环境变量必须以 `VITE_` 前缀开头才能在前端代码中访问
2. 修改环境变量后需要重启开发服务器
3. 生产环境的API地址需要根据实际部署情况进行调整
4. 敏感信息（如API密钥）不应该放在前端环境变量中
