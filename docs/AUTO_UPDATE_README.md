# ChatNote 自动更新功能

ChatNote 现已集成完整的自动更新功能，支持跨平台自动检查、下载和安装更新。

## ✨ 功能特性

- 🔄 **自动检查更新**: 应用启动时自动检查新版本
- 📥 **后台下载**: 静默下载更新包，不影响使用
- 🎯 **智能提醒**: 友好的用户界面提示更新
- 📊 **进度显示**: 实时显示下载进度和速度
- 🔒 **安全验证**: SHA256文件完整性校验
- 🌍 **跨平台支持**: 支持 macOS、Windows、Linux
- ☁️ **云端分发**: 基于阿里云OSS的高速分发

## 🚀 用户体验

### 更新检查
- 应用启动5秒后自动检查更新
- 检查过程不会影响应用使用
- 支持手动检查更新

### 更新提醒
- 发现新版本时显示友好提示
- 展示版本号和更新内容
- 用户可选择立即更新或稍后提醒

### 下载过程
- 后台静默下载，不阻塞应用
- 实时显示下载进度和速度
- 显示已下载/总大小信息

### 安装更新
- 下载完成后提示用户重启
- 支持立即安装或稍后安装
- 重启后自动完成更新安装

## 🛠️ 开发者指南

### 快速发布

```bash
# 发布补丁版本 (1.0.0 → 1.0.1)
npm run release

# 发布次要版本 (1.0.0 → 1.1.0)
npm run release:minor

# 发布主要版本 (1.0.0 → 2.0.0)
npm run release:major
```

### 平台特定发布

```bash
# 仅发布 macOS 版本
npm run release:mac

# 仅发布 Windows 版本
npm run release:win

# 仅发布 Linux 版本
npm run release:linux

# 发布所有平台
npm run release:all
```

### 手动控制

```bash
# 仅构建，不上传
npm run build:release

# 仅上传已构建的文件
npm run upload:oss

# 初始化OSS配置
npm run setup:oss
```

## 📋 配置要求

### 环境变量

创建 `.env` 文件并配置以下变量：

```env
# 阿里云OSS配置
OSS_ACCESS_KEY_ID=your_access_key_id
OSS_ACCESS_KEY_SECRET=your_access_key_secret
OSS_BUCKET=your_bucket_name
OSS_REGION=oss-cn-hangzhou
UPDATE_SERVER_URL=https://your_bucket_name.oss-cn-hangzhou.aliyuncs.com/releases
```

### 阿里云OSS设置

1. 创建OSS存储桶
2. 设置公共读权限
3. 配置CORS规则
4. 获取访问密钥

详细配置请参考 [自动更新配置指南](./自动更新配置指南.md)

## 🔧 技术实现

### 核心组件

- **electron-updater**: 自动更新核心库
- **UpdateManager.vue**: 更新UI组件
- **主进程更新逻辑**: 集成在 `src/main/index.ts`
- **OSS上传脚本**: 自动化发布流程

### 更新流程

```mermaid
sequenceDiagram
    participant App as Electron App
    participant OSS as 阿里云OSS
    participant User as 用户

    App->>OSS: 检查更新清单
    OSS-->>App: 返回版本信息
    
    alt 有新版本
        App->>User: 显示更新提示
        User->>App: 确认更新
        App->>OSS: 下载更新包
        OSS-->>App: 返回更新文件
        App->>User: 提示重启安装
        User->>App: 确认重启
        App->>App: 安装更新并重启
    else 无新版本
        App->>App: 继续正常运行
    end
```

### 文件结构

```
releases/
├── latest.yml              # 最新版本清单
├── 1.0.0/
│   ├── ChatNote-1.0.0.dmg  # macOS 安装包
│   ├── ChatNote-1.0.0.exe  # Windows 安装包
│   ├── ChatNote-1.0.0.AppImage # Linux 安装包
│   └── latest-mac.yml      # macOS 更新清单
└── 1.0.1/
    └── ...
```

## 🔍 故障排除

### 常见问题

1. **更新检查失败**
   - 检查网络连接
   - 验证OSS配置
   - 查看控制台错误

2. **下载失败**
   - 检查OSS权限
   - 验证文件完整性
   - 重试下载

3. **安装失败**
   - 检查文件权限
   - 确认磁盘空间
   - 查看安装日志

### 调试方法

```bash
# 启用详细日志
DEBUG=electron-updater npm start

# 测试OSS连接
npm run setup:oss

# 验证更新清单
curl https://your-bucket.oss-cn-hangzhou.aliyuncs.com/releases/latest.yml
```

## 📚 相关文档

- [自动更新配置指南](./自动更新配置指南.md) - 完整配置说明
- [OSS上传操作指南](./OSS上传操作指南.md) - OSS配置详解
- [electron-updater 官方文档](https://www.electron.build/auto-update)

## 🤝 贡献

欢迎提交Issue和Pull Request来改进自动更新功能。

## 📄 许可证

本项目采用 MIT 许可证。
