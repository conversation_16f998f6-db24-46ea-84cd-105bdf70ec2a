# Electron应用自动更新配置指南

本指南详细说明如何为ChatNote Electron应用配置完整的自动更新功能，包括electron-updater集成、阿里云OSS配置、构建发布流程等。

## 目录

1. [快速开始](#快速开始)
2. [系统架构](#系统架构)
3. [前置准备](#前置准备)
4. [配置步骤](#配置步骤)
5. [使用方法](#使用方法)
6. [故障排除](#故障排除)
7. [最佳实践](#最佳实践)

## 快速开始

如果您急于开始，请按以下步骤快速配置：

### 1. 配置环境变量
```bash
cp .env.example .env
# 编辑 .env 文件，填入您的阿里云OSS配置
```

### 2. 初始化OSS
```bash
npm run setup:oss
```

### 3. 发布第一个版本
```bash
npm run release
```

### 4. 测试自动更新
构建完成后，安装生成的应用，然后发布新版本测试更新功能。

详细配置请继续阅读下面的章节。

## 系统架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Electron App  │    │  阿里云OSS存储   │    │   用户设备      │
│                 │    │                 │    │                 │
│ ┌─────────────┐ │    │ ┌─────────────┐ │    │ ┌─────────────┐ │
│ │ AutoUpdater │◄┼────┼►│ 更新清单文件 │ │    │ │ 应用自动更新 │ │
│ └─────────────┘ │    │ └─────────────┘ │    │ └─────────────┘ │
│ ┌─────────────┐ │    │ ┌─────────────┐ │    │                 │
│ │ 更新UI组件   │ │    │ │ 安装包文件   │ │    │                 │
│ └─────────────┘ │    │ └─────────────┘ │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 前置准备

### 1. 环境要求

- Node.js 18+
- npm 或 yarn
- Git
- 阿里云账号
- 代码签名证书（可选，用于生产环境）

### 2. 依赖安装

确保已安装所有必要依赖：

```bash
npm install
```

主要依赖包括：
- `electron-updater`: 自动更新核心库
- `ali-oss`: 阿里云OSS SDK
- `dotenv`: 环境变量管理
- `js-yaml`: YAML文件处理

## 配置步骤

### 1. 阿里云OSS配置

#### 创建OSS存储桶
1. 登录阿里云控制台
2. 创建新的OSS存储桶
3. 设置读写权限为"公共读"
4. 记录存储桶名称和区域

#### 获取访问密钥
1. 创建RAM用户
2. 分配OSS权限
3. 获取AccessKey ID和Secret

#### 配置环境变量
复制 `.env.example` 为 `.env`：

```bash
cp .env.example .env
```

编辑 `.env` 文件：

```env
# 阿里云OSS配置
OSS_ACCESS_KEY_ID=your_access_key_id
OSS_ACCESS_KEY_SECRET=your_access_key_secret
OSS_BUCKET=your_bucket_name
OSS_REGION=oss-cn-hangzhou
UPDATE_SERVER_URL=https://your_bucket_name.oss-cn-hangzhou.aliyuncs.com/releases
```

### 2. 初始化OSS设置

运行OSS设置脚本：

```bash
npm run setup:oss
```

此脚本会自动：
- 验证OSS连接
- 配置CORS规则
- 设置访问策略
- 创建目录结构
- 进行连接测试

### 3. 更新package.json配置

确保package.json中的electron-builder配置正确：

```json
{
  "build": {
    "publish": {
      "provider": "generic",
      "url": "https://your-bucket.oss-cn-hangzhou.aliyuncs.com/releases"
    }
  }
}
```

## 使用方法

### 1. 开发阶段

#### 测试自动更新功能
```bash
# 构建开发版本
npm run build:dev

# 启动应用测试
npm run electron:dev
```

#### 检查更新逻辑
应用会在启动5秒后自动检查更新，或者可以手动触发检查。

### 2. 发布流程

#### 快速发布（补丁版本）
```bash
npm run release
```

#### 发布不同版本类型
```bash
# 补丁版本 (1.0.0 → 1.0.1)
npm run release

# 次要版本 (1.0.0 → 1.1.0)
npm run release:minor

# 主要版本 (1.0.0 → 2.0.0)
npm run release:major
```

#### 发布特定平台
```bash
# 仅发布macOS版本
npm run release:mac

# 仅发布Windows版本
npm run release:win

# 仅发布Linux版本
npm run release:linux

# 发布所有平台
npm run release:all
```

#### 手动控制发布流程
```bash
# 使用发布管理器
node scripts/release-manager.js --minor --all --skip-git

# 仅构建不上传
npm run build:release

# 仅上传已构建文件
npm run upload:oss
```

### 3. 版本管理

#### 自动版本升级
```bash
# 升级并推送到Git
npm run version:patch
npm run version:minor
npm run version:major
```

#### 手动版本管理
```bash
# 仅升级版本号
npm version patch --no-git-tag-version
npm version minor --no-git-tag-version
npm version major --no-git-tag-version
```

## 故障排除

### 常见问题

#### 1. OSS连接失败
**错误**: `AccessDenied` 或 `InvalidAccessKeyId`

**解决方案**:
- 检查AccessKey是否正确
- 确认RAM用户权限
- 验证存储桶名称和区域

```bash
# 测试OSS连接
npm run setup:oss
```

#### 2. 构建失败
**错误**: 构建过程中出现错误

**解决方案**:
- 检查Node.js版本
- 清理node_modules重新安装
- 检查构建日志

```bash
# 清理并重新安装
rm -rf node_modules package-lock.json
npm install

# 详细构建日志
DEBUG=* npm run build:release
```

#### 3. 更新检查失败
**错误**: 应用无法检查更新

**解决方案**:
- 检查更新服务器URL
- 验证网络连接
- 查看控制台错误日志

```bash
# 验证更新清单文件
curl https://your-bucket.oss-cn-hangzhou.aliyuncs.com/releases/latest.yml
```

#### 4. 自动更新不工作
**错误**: 应用不会自动更新

**解决方案**:
- 确认应用已打包（不是开发模式）
- 检查版本号格式
- 验证更新清单文件

### 调试方法

#### 启用详细日志
```bash
# 设置环境变量
export DEBUG=electron-updater

# 或在代码中启用
autoUpdater.logger = require('electron-log')
autoUpdater.logger.transports.file.level = 'info'
```

#### 检查更新清单
```bash
# 验证清单文件格式
node -e "console.log(JSON.stringify(require('./release/manifest.json'), null, 2))"
```

#### 测试更新流程
```bash
# 模拟版本检查
curl -s https://your-bucket.oss-cn-hangzhou.aliyuncs.com/releases/latest.yml
```

## 最佳实践

### 1. 版本管理
- 使用语义化版本号 (Semantic Versioning)
- 每次发布前更新CHANGELOG.md
- 使用Git标签标记发布版本

### 2. 安全考虑
- 定期轮换AccessKey
- 使用最小权限原则
- 启用代码签名（生产环境）
- 不要将敏感信息提交到版本控制

### 3. 性能优化
- 配置CDN加速下载
- 使用增量更新（如果支持）
- 设置合理的检查更新频率

### 4. 用户体验
- 提供清晰的更新提示
- 显示下载进度
- 允许用户选择更新时机
- 提供更新日志

### 5. 监控和维护
- 监控更新成功率
- 定期清理旧版本文件
- 设置存储生命周期规则
- 备份重要版本

## 文件结构

```
project/
├── scripts/
│   ├── upload-to-oss.js          # OSS上传脚本
│   ├── setup-oss.js              # OSS初始化脚本
│   ├── release-manager.js        # 发布管理器
│   └── generate-update-manifest.js # 清单生成器
├── build/
│   └── entitlements.mac.plist    # macOS权限文件
├── docs/
│   ├── 自动更新配置指南.md        # 本文档
│   └── OSS上传操作指南.md         # OSS操作指南
├── src/
│   ├── main/index.ts             # 主进程（包含更新逻辑）
│   ├── preload/index.ts          # 预加载脚本
│   └── renderer/
│       └── components/
│           └── UpdateManager.vue  # 更新UI组件
├── .env.example                  # 环境变量模板
├── package.json                  # 项目配置
└── README.md                     # 项目说明
```

## 支持和帮助

如果遇到问题，请：

1. 查看本文档的故障排除部分
2. 检查项目的GitHub Issues
3. 查看electron-updater官方文档
4. 联系技术支持团队

---

**注意**: 本配置适用于ChatNote应用，使用前请根据实际项目需求进行调整。
