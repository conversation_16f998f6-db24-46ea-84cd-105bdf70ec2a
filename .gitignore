# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

node_modules
.DS_Store
dist
dist-ssr
coverage
*.local

/cypress/videos/
/cypress/screenshots/

# Editor directories and files
.vscode/*
!.vscode/extensions.json
.idea
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

*.tsbuildinfo
dist-electron
.env
.env.test
.env.local
.env.*.local
release
out

# Debug and temporary files
debug_*
test-*.sh
test-*.md
quick-test.*
*.debug.log
*.test.log

# Temporary documentation
xuqiu.md
使用示例.md
认证系统使用说明.md
项目总结.md

# IDE and editor files
.vscode/settings.json
.vscode/launch.json
*.swp
*.swo
*~
out
release