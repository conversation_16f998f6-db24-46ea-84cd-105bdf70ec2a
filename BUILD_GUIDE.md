# Electron-Vite 多平台编译指南

## 项目配置概述

本项目已成功从 `vite-plugin-electron` 迁移到 `electron-vite` 构建工具，支持 macOS、Windows、Linux 多平台编译。

## 目录结构

```
chat-note-app/
├── src/
│   ├── main/           # 主进程代码
│   │   └── index.ts
│   ├── preload/        # 预加载脚本
│   │   └── index.ts
│   └── renderer/       # 渲染进程代码
│       ├── main.ts
│       ├── App.vue
│       ├── components/
│       ├── views/
│       └── ...
├── out/                # 构建输出目录
│   ├── main/
│   ├── preload/
│   └── renderer/
├── release/            # 打包输出目录
├── electron.vite.config.ts
├── package.json
└── index.html
```

## 可用的编译脚本

### 基础构建命令
```bash
# 开发模式
npm run dev

# 构建（生产模式）
npm run build

# 预览构建结果
npm run preview
```

### 单平台编译
```bash
# macOS 编译（支持 x64 + arm64）
npm run electron:build:mac

# Windows 编译（x64）
npm run electron:build:win

# Linux 编译（x64）
npm run electron:build:linux
```

### 多架构编译
```bash
# macOS Intel 芯片（x64）
npm run electron:build:mac-x64

# macOS Apple 芯片（arm64）
npm run electron:build:mac-arm64

# Windows x64
npm run electron:build:win-x64

# Linux x64
npm run electron:build:linux-x64
```

### 全平台编译
```bash
# 一次性编译所有平台
npm run electron:build:all
```

## 输出文件说明

### macOS
- `聊天式笔记-0.0.0.dmg` - Intel 芯片版本
- `聊天式笔记-0.0.0-arm64.dmg` - Apple 芯片版本

### Windows
- `聊天式笔记 Setup 0.0.0.exe` - Windows 安装程序

### Linux
- `聊天式笔记-0.0.0.AppImage` - Linux AppImage 格式

## 配置文件说明

### electron.vite.config.ts
```typescript
import { resolve } from 'path'
import { defineConfig, externalizeDepsPlugin } from 'electron-vite'
import vue from '@vitejs/plugin-vue'
import vueDevTools from 'vite-plugin-vue-devtools'

export default defineConfig({
  main: {
    plugins: [externalizeDepsPlugin()]
  },
  preload: {
    plugins: [externalizeDepsPlugin()]
  },
  renderer: {
    root: '.',
    plugins: [vue(), vueDevTools()],
    resolve: {
      alias: {
        '@': resolve('src/renderer')
      }
    },
    build: {
      rollupOptions: {
        input: resolve(__dirname, 'index.html')
      }
    },
    server: {
      host: '127.0.0.1',
      port: 3000
    }
  }
})
```

### package.json 构建配置
```json
{
  "main": "out/main/index.js",
  "build": {
    "appId": "com.chatnote.app",
    "productName": "聊天式笔记",
    "directories": {
      "output": "release"
    },
    "files": [
      "out/**/*",
      "node_modules/**/*",
      "package.json"
    ],
    "mac": {
      "category": "public.app-category.productivity",
      "target": [
        {
          "target": "dmg",
          "arch": ["x64", "arm64"]
        }
      ]
    },
    "win": {
      "target": [
        {
          "target": "nsis",
          "arch": ["x64"]
        }
      ]
    },
    "linux": {
      "target": [
        {
          "target": "AppImage",
          "arch": ["x64"]
        }
      ]
    }
  }
}
```

## 注意事项

1. **依赖管理**: 使用 `externalizeDepsPlugin()` 自动外部化依赖
2. **路径配置**: 主进程中不需要手动定义 `__filename` 和 `__dirname`
3. **构建输出**: 使用 `out/` 目录而不是 `dist-electron/`
4. **代码签名**: macOS 版本会自动使用开发者证书签名
5. **图标配置**: 需要在 `public/` 目录添加应用图标

## 开发建议

1. 开发时使用 `npm run dev` 启动热重载
2. 构建前确保所有依赖已正确安装
3. 首次构建可能需要下载 Electron 二进制文件，请耐心等待
4. 跨平台编译建议在对应平台上进行，以确保最佳兼容性

## 故障排除

如果遇到构建问题：
1. 清理 `out/` 和 `release/` 目录
2. 重新安装依赖：`rm -rf node_modules && npm install`
3. 检查 Node.js 版本兼容性
4. 确保网络连接正常（需要下载 Electron 二进制文件）
