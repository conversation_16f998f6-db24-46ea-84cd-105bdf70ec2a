# API认证配置说明

## 概述

项目已实现了自动化的API认证管理系统，可以根据配置自动为需要认证的接口添加 `Authorization` header，而对于用户相关的登录、注册等接口则不添加认证头。

## 功能特性

1. **自动认证管理** - 根据接口路径自动判断是否需要添加Authorization header
2. **配置化白名单** - 支持精确匹配和通配符匹配的接口白名单
3. **统一API客户端** - 提供统一的API调用接口和token管理方法
4. **错误处理** - 自动处理401认证错误

## 配置文件

### 认证配置 (`src/config/index.ts`)

```typescript
auth: {
  // 不需要 Authorization header 的接口列表（精确匹配）
  noAuthEndpoints: [
    '/api/v1/user/send-verification-code',
    '/api/v1/user/register',
    '/api/v1/user/login',
    '/api/v1/user/reset-password',
    '/api/v1/system/config',
  ],
  // 支持通配符匹配的接口模式
  noAuthPatterns: [
    '/api/v1/user/send-*',
    '/api/v1/user/login*',
    '/api/v1/user/register*',
    '/api/v1/system/*',
  ],
}
```

### 添加新的白名单接口

如果需要添加新的不需要认证的接口，有两种方式：

1. **精确匹配** - 在 `noAuthEndpoints` 数组中添加完整的接口路径
2. **通配符匹配** - 在 `noAuthPatterns` 数组中添加带通配符的模式

## API客户端使用

### 基本用法

```typescript
import { apiClient } from '@/utils/api'

// GET请求（会自动添加认证头，如果接口需要的话）
const response = await apiClient.get('/api/v1/user/profile')

// POST请求
const response = await apiClient.post('/api/v1/notes', { title: '新笔记' })

// 登录请求（不会添加认证头）
const response = await apiClient.post('/api/v1/user/login', {
  email: '<EMAIL>',
  password: 'password'
})
```

### Token管理

```typescript
// 设置认证token
apiClient.setAuthToken('your-jwt-token')

// 获取当前token
const token = apiClient.getAuthToken()

// 清除token
apiClient.clearAuthToken()

// 检查接口是否需要认证
const needsAuth = apiClient.needsAuth('/api/v1/user/login') // false
const needsAuth = apiClient.needsAuth('/api/v1/user/profile') // true
```

## 认证流程

1. **用户登录** - 调用登录接口（不需要认证头）
2. **保存Token** - 登录成功后自动保存token到localStorage和API客户端
3. **自动认证** - 后续API请求自动根据配置添加认证头
4. **错误处理** - 401错误时自动记录日志，可扩展为自动登出或刷新token

## 接口分类

### 不需要认证的接口
- 用户注册: `/api/v1/user/register`
- 用户登录: `/api/v1/user/login`
- 发送验证码: `/api/v1/user/send-verification-code`
- 重置密码: `/api/v1/user/reset-password`
- 系统配置: `/api/v1/system/*`

### 需要认证的接口
- 用户资料: `/api/v1/user/profile`
- 笔记相关: `/api/v1/notes/*`
- 标签相关: `/api/v1/note-tags/*`
- 主题相关: `/api/v1/note-themes/*`
- 笔记话题: `/api/v1/note_topic/*`

## 测试工具

项目提供了测试工具来验证认证配置：

```typescript
import { runAllTests } from '@/utils/auth-test'

// 在浏览器控制台运行测试
runAllTests()
```

## 扩展配置

如果需要添加新的不需要认证的接口，只需要在 `src/config/index.ts` 中的 `auth` 配置中添加：

```typescript
// 添加精确匹配的接口
noAuthEndpoints: [
  // ... 现有接口
  '/api/v1/new/endpoint',
],

// 或添加通配符模式
noAuthPatterns: [
  // ... 现有模式
  '/api/v1/public/*',
]
```

## 注意事项

1. **Token格式** - 系统使用 `{token}` 格式的Authorization header
2. **存储位置** - Token存储在localStorage中，key为 `auth_token`
3. **自动清理** - 登出时会自动清理token和用户信息
4. **错误处理** - 401错误会记录日志，可根据需要扩展处理逻辑

## 后续优化建议

1. **Token刷新** - 实现自动token刷新机制
2. **请求重试** - 401错误后自动重试机制
3. **离线处理** - 网络错误时的离线处理
4. **安全增强** - 添加token加密存储
