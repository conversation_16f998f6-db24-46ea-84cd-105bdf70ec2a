#!/usr/bin/env node

/**
 * 简单的应用测试脚本
 * 检查应用是否能正常启动
 */

const { spawn } = require('child_process')
const path = require('path')

console.log('🧪 测试应用启动...')

// 启动应用
const appProcess = spawn('npm', ['run', 'dev'], {
  cwd: __dirname,
  stdio: 'inherit'
})

// 监听进程事件
appProcess.on('error', (error) => {
  console.error('❌ 应用启动失败:', error.message)
  process.exit(1)
})

appProcess.on('exit', (code) => {
  if (code === 0) {
    console.log('✅ 应用正常退出')
  } else {
    console.error(`❌ 应用异常退出，退出码: ${code}`)
  }
})

// 5秒后自动关闭测试
setTimeout(() => {
  console.log('\n⏰ 测试时间到，关闭应用...')
  appProcess.kill('SIGTERM')
  
  setTimeout(() => {
    console.log('✅ 测试完成')
    process.exit(0)
  }, 2000)
}, 5000)

console.log('📱 应用应该在几秒内启动...')
console.log('💡 如果看到应用窗口，说明自动更新功能集成成功！')
