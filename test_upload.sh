#!/bin/bash

# 图片上传功能测试脚本

echo "🚀 开始测试图片上传功能..."

# 检查服务是否运行
echo "📡 检查API服务状态..."
if ! curl -s http://localhost:8000/api/v1/system/config > /dev/null; then
    echo "❌ API服务未运行，请先启动服务："
    echo "   go run cmd/api/chat_note_api.go"
    exit 1
fi

echo "✅ API服务运行正常"

# 创建测试图片
echo "🖼️  创建测试图片..."
cat > test_image.png << 'EOF'
iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==
EOF

# 将base64转换为二进制文件
echo "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==" | base64 -d > test_image.png

echo "✅ 测试图片创建成功"

# 测试1: 上传图片文件
echo "📤 测试1: 上传图片文件..."
UPLOAD_RESPONSE=$(curl -s -X POST \
  -H "Authorization: test-token" \
  -F "file=@test_image.png" \
  http://localhost:8000/api/v1/upload/image)

echo "响应: $UPLOAD_RESPONSE"

if echo "$UPLOAD_RESPONSE" | grep -q '"status":200'; then
    echo "✅ 图片文件上传测试通过"
    
    # 提取上传的图片URL
    IMAGE_URL=$(echo "$UPLOAD_RESPONSE" | grep -o '"/uploads/images/[^"]*"' | tr -d '"')
    echo "📷 上传的图片URL: $IMAGE_URL"
    
    # 测试图片访问
    if curl -s "http://localhost:8000$IMAGE_URL" > /dev/null; then
        echo "✅ 图片访问测试通过"
    else
        echo "❌ 图片访问测试失败"
    fi
else
    echo "❌ 图片文件上传测试失败"
fi

# 测试2: 通过URL上传图片
echo "🌐 测试2: 通过URL上传图片..."
URL_UPLOAD_RESPONSE=$(curl -s -X POST \
  -H "Authorization: test-token" \
  -H "Content-Type: application/json" \
  -d '{"url":"https://via.placeholder.com/150.png"}' \
  http://localhost:8000/api/v1/upload/image-by-url)

echo "响应: $URL_UPLOAD_RESPONSE"

if echo "$URL_UPLOAD_RESPONSE" | grep -q '"status":200'; then
    echo "✅ URL图片上传测试通过"
else
    echo "❌ URL图片上传测试失败"
fi

# 测试3: 无效文件类型
echo "📄 测试3: 无效文件类型..."
echo "This is not an image" > test_text.txt

INVALID_RESPONSE=$(curl -s -X POST \
  -H "Authorization: test-token" \
  -F "file=@test_text.txt" \
  http://localhost:8000/api/v1/upload/image)

echo "响应: $INVALID_RESPONSE"

if echo "$INVALID_RESPONSE" | grep -q '"status":500'; then
    echo "✅ 无效文件类型测试通过（正确拒绝）"
else
    echo "❌ 无效文件类型测试失败（应该被拒绝）"
fi

# 测试4: 无认证token
echo "🔒 测试4: 无认证token..."
NO_AUTH_RESPONSE=$(curl -s -X POST \
  -F "file=@test_image.png" \
  http://localhost:8000/api/v1/upload/image)

echo "响应: $NO_AUTH_RESPONSE"

if echo "$NO_AUTH_RESPONSE" | grep -q '"status":401'; then
    echo "✅ 认证测试通过（正确要求认证）"
else
    echo "❌ 认证测试失败（应该要求认证）"
fi

# 清理测试文件
echo "🧹 清理测试文件..."
rm -f test_image.png test_text.txt

echo "🎉 图片上传功能测试完成！"

# 显示上传目录状态
echo "📁 上传目录状态:"
ls -la uploads/images/ 2>/dev/null || echo "上传目录不存在或为空"
