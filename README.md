# 聊天式笔记应用

一个基于 Vue 3 + Electron 的现代化聊天式笔记应用，让您像使用聊天软件一样记录和管理笔记。

## 功能特性

### 已实现功能

- ✅ **聊天式界面**: 仿微信聊天界面的笔记记录体验
- ✅ **主题管理**: 创建、编辑、删除笔记主题
- ✅ **消息管理**: 在主题内添加、编辑、删除笔记消息
- ✅ **收藏功能**: 收藏重要的笔记主题
- ✅ **搜索功能**: 快速搜索主题和消息内容
- ✅ **本地存储**: 使用 localStorage 持久化数据
- ✅ **响应式设计**: 适配不同屏幕尺寸
- ✅ **暗色主题**: 自动跟随系统主题

### 开发中功能

- 🚧 **标签系统**: 为笔记主题添加和管理标签
- 🚧 **用户系统**: 用户注册、登录和信息管理
- 🚧 **云同步**: 跨设备同步笔记数据
- 🚧 **文件附件**: 支持图片、文件等多媒体内容
- 🚧 **导入导出**: 笔记数据的导入和导出功能

## 技术栈

- **前端框架**: Vue 3 + TypeScript
- **状态管理**: Pinia
- **路由**: Vue Router
- **UI 组件库**: Naive UI
- **构建工具**: Vite
- **跨平台**: Electron
- **图标**: Ionicons 5

## 开发指南

### 环境要求

- Node.js 18+
- npm 或 yarn

### 安装依赖

```bash
npm install
```

### 开发模式

启动 Web 版本（推荐用于开发）：
```bash
npm run dev
```

启动 Electron 版本：
```bash
npm run electron:dev
```

### 构建

构建 Web 版本：
```bash
npm run build
```

构建 Electron 应用：
```bash
# 构建当前平台
npm run electron:build

# 构建 macOS 版本
npm run electron:build:mac
```

## 使用说明

### 基本操作

1. **创建主题**: 点击主题列表顶部的 "+" 按钮创建新的笔记主题
2. **发送消息**: 在聊天界面底部输入框中输入内容，按 Enter 发送
3. **收藏主题**: 右键点击主题，选择"收藏"
4. **搜索**: 在主题列表顶部搜索框中输入关键词搜索主题

### 快捷键

- `Enter`: 发送消息
- `Shift + Enter`: 换行

### 数据存储

应用使用浏览器的 localStorage 存储数据，数据保存在本地。

## 许可证

MIT License

如果现有富文本编辑器不好用 考虑替换为后main这个
https://github.com/wangeditor-team/wangEditor?tab=readme-ov-file