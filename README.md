
google ai gemini 无法对接，原因：香港ip无法访问
1https://aistudio.google.com/app/apikey
https://ai.google.dev/gemini-api/docs/models/gemini?hl=zh-cn



curl 'http://localhost:8080/api/v1/app/update_app' \
  -X 'PUT' \
  -H 'accept: application/json, text/plain, */*' \
  -H 'accept-language: zh-CN,zh;q=0.9' \
  -H 'authorization: ZXlKaGJHY2lPaUpJVXpJMU5pSXNJblI1Y0NJNklrcFhWQ0o5LmV5SmpiMjF3WVc1NUlqb3hMQ0psZUhBaU9qRTNNalUxTURVNU9UY3NJbWxoZENJNk1UY3lORGt3TVRFNU55d2ljM1ZpSWpveE5UYzFNbjAuZllwdGQzUkg2dkk5RzV4U3p5d21Vcmg1bGN3MmROc2M4TlN2TnNIdHNDbw' \
  -H 'content-type: application/json' \
  -H 'origin: http://localhost:8080' \
  -H 'priority: u=1, i' \
  -H 'referer: http://localhost:8080/' \
  -H 'sec-ch-ua: "Chromium";v="128", "Not;A=Brand";v="24", "Google Chrome";v="128"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "macOS"' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: cross-site' \
  -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  --data-raw '{"id":"1829054832475156480","name":"TestJayce13","icon":"https://resource.spotmaxtech.com/mrmax/icon/default.png","description":"eeeeeee","base_app":false,"public":false,"type":"agent","llm_id":"1813878758420078592","doc_ids":[],"tool_ids":[],"system_prompy":"You are a helpful assistant.","update_fields":["name","icon","description","base_app","public","type","llm_id","doc_ids","tool_ids","system_prompy"]}'



  chat_with_maxagent : 和 MaxAgent app 聊天


支付
  https://7-pay.cn/index.html