# API 配置
VITE_API_BASE_URL=http://localhost:8000

# 应用配置
VITE_APP_NAME=Chat Note App
VITE_APP_VERSION=1.0.0

# 开发环境配置
VITE_DEV_MODE=true
VITE_DEBUG_MODE=false

# 功能开关
VITE_ENABLE_API_SYNC=false
VITE_ENABLE_OFFLINE_MODE=true
VITE_ENABLE_AUTO_SAVE=true

# 存储配置
VITE_STORAGE_PREFIX=chat_note_
VITE_MAX_STORAGE_SIZE=50MB

# 认证配置
VITE_AUTH_ENABLED=true
VITE_JWT_EXPIRY=7d

# 阿里云OSS配置（用于自动更新）
# 复制此文件为 .env 并填入实际的配置信息

# OSS访问密钥ID
OSS_ACCESS_KEY_ID=your_access_key_id

# OSS访问密钥Secret
OSS_ACCESS_KEY_SECRET=your_access_key_secret

# OSS存储桶名称
OSS_BUCKET=your_bucket_name

# OSS区域
OSS_REGION=oss-cn-hangzhou

# OSS自定义域名（可选）
OSS_ENDPOINT=your_custom_domain.com

# 更新服务器URL（构建时使用）
UPDATE_SERVER_URL=https://your_bucket_name.oss-cn-hangzhou.aliyuncs.com/releases
