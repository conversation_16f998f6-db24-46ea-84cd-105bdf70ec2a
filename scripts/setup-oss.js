#!/usr/bin/env node

/**
 * 阿里云OSS设置脚本
 * 用于初始化OSS存储桶和配置CORS等设置
 */

const OSS = require('ali-oss')
const fs = require('fs')
const path = require('path')

// 从环境变量读取配置
require('dotenv').config()

const config = {
  region: process.env.OSS_REGION || 'oss-cn-hangzhou',
  accessKeyId: process.env.OSS_ACCESS_KEY_ID,
  accessKeySecret: process.env.OSS_ACCESS_KEY_SECRET,
  bucket: process.env.OSS_BUCKET,
  endpoint: process.env.OSS_ENDPOINT
}

// 验证配置
function validateConfig() {
  const required = ['accessKeyId', 'accessKeySecret', 'bucket']
  const missing = required.filter(key => !config[key])
  
  if (missing.length > 0) {
    console.error('❌ 缺少必要的环境变量:')
    missing.forEach(key => {
      console.error(`   OSS_${key.toUpperCase()}`)
    })
    console.error('\n请在 .env 文件中设置以上环境变量。')
    process.exit(1)
  }
}

// 创建OSS客户端
function createOSSClient() {
  return new OSS(config)
}

// 设置CORS规则
async function setupCORS(client) {
  console.log('🔧 配置CORS规则...')
  
  const corsRules = [
    {
      allowedOrigin: '*',
      allowedMethod: ['GET', 'HEAD'],
      allowedHeader: ['*'],
      exposeHeader: ['ETag', 'x-oss-request-id'],
      maxAgeSeconds: 3600
    }
  ]
  
  try {
    await client.putBucketCORS(config.bucket, corsRules)
    console.log('✅ CORS规则配置成功')
  } catch (error) {
    console.error('❌ CORS规则配置失败:', error.message)
    throw error
  }
}

// 设置存储桶策略（公共读取）
async function setupBucketPolicy(client) {
  console.log('🔧 配置存储桶访问策略...')
  
  try {
    await client.putBucketACL(config.bucket, 'public-read')
    console.log('✅ 存储桶访问策略配置成功')
  } catch (error) {
    console.error('❌ 存储桶访问策略配置失败:', error.message)
    throw error
  }
}

// 创建必要的目录结构
async function createDirectories(client) {
  console.log('📁 创建目录结构...')
  
  const directories = [
    'releases/',
    'releases/latest/',
    'releases/archive/'
  ]
  
  for (const dir of directories) {
    try {
      // 创建一个空的占位文件来确保目录存在
      await client.put(`${dir}.gitkeep`, Buffer.from(''))
      console.log(`✅ 创建目录: ${dir}`)
    } catch (error) {
      console.error(`❌ 创建目录失败: ${dir}`, error.message)
    }
  }
}

// 上传测试文件
async function uploadTestFile(client) {
  console.log('🧪 上传测试文件...')
  
  const testContent = JSON.stringify({
    message: 'OSS配置测试成功',
    timestamp: new Date().toISOString(),
    version: '1.0.0'
  }, null, 2)
  
  try {
    const result = await client.put('releases/test.json', Buffer.from(testContent))
    console.log('✅ 测试文件上传成功:', result.url)
    
    // 删除测试文件
    await client.delete('releases/test.json')
    console.log('✅ 测试文件清理完成')
  } catch (error) {
    console.error('❌ 测试文件上传失败:', error.message)
    throw error
  }
}

// 主设置函数
async function setupOSS() {
  console.log('🚀 开始配置阿里云OSS...\n')
  
  // 验证配置
  validateConfig()
  
  // 创建OSS客户端
  const client = createOSSClient()
  
  console.log(`📦 存储桶: ${config.bucket}`)
  console.log(`🌍 区域: ${config.region}`)
  console.log(`🔗 访问域名: https://${config.bucket}.${config.region}.aliyuncs.com\n`)
  
  try {
    // 检查存储桶是否存在
    console.log('🔍 检查存储桶状态...')
    await client.getBucketInfo(config.bucket)
    console.log('✅ 存储桶连接成功\n')
    
    // 配置CORS
    await setupCORS(client)
    
    // 配置访问策略
    await setupBucketPolicy(client)
    
    // 创建目录结构
    await createDirectories(client)
    
    // 上传测试文件
    await uploadTestFile(client)
    
    console.log('\n🎉 OSS配置完成!')
    console.log(`📍 更新服务器URL: https://${config.bucket}.${config.region}.aliyuncs.com/releases`)
    console.log('\n💡 提示:')
    console.log('1. 请将更新服务器URL添加到package.json的publish配置中')
    console.log('2. 确保在构建时设置UPDATE_SERVER_URL环境变量')
    console.log('3. 运行 npm run release 来构建并上传应用')
    
  } catch (error) {
    console.error('\n❌ OSS配置失败:', error.message)
    
    if (error.code === 'NoSuchBucket') {
      console.error('\n💡 存储桶不存在，请先在阿里云控制台创建存储桶')
    } else if (error.code === 'AccessDenied') {
      console.error('\n💡 访问被拒绝，请检查AccessKey权限')
    }
    
    process.exit(1)
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  setupOSS()
}

module.exports = {
  setupOSS,
  createOSSClient,
  validateConfig
}
