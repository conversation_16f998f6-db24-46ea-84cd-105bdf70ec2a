#!/usr/bin/env node

/**
 * 自动更新配置验证脚本
 * 检查所有必要的配置是否正确
 */

const fs = require('fs')
const path = require('path')

console.log('🔍 验证自动更新配置...\n')

let hasErrors = false

// 检查文件是否存在
function checkFileExists(filePath, description) {
  if (fs.existsSync(filePath)) {
    console.log(`✅ ${description}: ${filePath}`)
    return true
  } else {
    console.log(`❌ ${description} 不存在: ${filePath}`)
    hasErrors = true
    return false
  }
}

// 检查package.json配置
function checkPackageJson() {
  console.log('📦 检查 package.json 配置...')
  
  const packagePath = path.join(__dirname, '../package.json')
  if (!checkFileExists(packagePath, 'package.json')) return
  
  const packageJson = JSON.parse(fs.readFileSync(packagePath, 'utf8'))
  
  // 检查依赖
  const requiredDeps = ['electron-updater']
  const requiredDevDeps = ['ali-oss', 'dotenv', 'js-yaml']
  
  requiredDeps.forEach(dep => {
    if (packageJson.dependencies && packageJson.dependencies[dep]) {
      console.log(`✅ 依赖: ${dep}`)
    } else {
      console.log(`❌ 缺少依赖: ${dep}`)
      hasErrors = true
    }
  })
  
  requiredDevDeps.forEach(dep => {
    if (packageJson.devDependencies && packageJson.devDependencies[dep]) {
      console.log(`✅ 开发依赖: ${dep}`)
    } else {
      console.log(`❌ 缺少开发依赖: ${dep}`)
      hasErrors = true
    }
  })
  
  // 检查构建配置
  if (packageJson.build && packageJson.build.publish) {
    console.log(`✅ electron-builder publish 配置`)
  } else {
    console.log(`❌ 缺少 electron-builder publish 配置`)
    hasErrors = true
  }
  
  // 检查脚本
  const requiredScripts = ['release', 'upload:oss', 'setup:oss']
  requiredScripts.forEach(script => {
    if (packageJson.scripts && packageJson.scripts[script]) {
      console.log(`✅ 脚本: ${script}`)
    } else {
      console.log(`❌ 缺少脚本: ${script}`)
      hasErrors = true
    }
  })
}

// 检查源代码文件
function checkSourceFiles() {
  console.log('\n📁 检查源代码文件...')
  
  const files = [
    ['src/main/index.ts', '主进程文件'],
    ['src/preload/index.ts', '预加载脚本'],
    ['src/renderer/components/UpdateManager.vue', '更新管理组件'],
    ['src/renderer/components/MainLayout.vue', '主布局组件'],
    ['src/renderer/types/electron.d.ts', '类型定义文件']
  ]
  
  files.forEach(([filePath, description]) => {
    checkFileExists(path.join(__dirname, '..', filePath), description)
  })
}

// 检查脚本文件
function checkScriptFiles() {
  console.log('\n🔧 检查脚本文件...')
  
  const scripts = [
    ['scripts/upload-to-oss.js', 'OSS上传脚本'],
    ['scripts/setup-oss.js', 'OSS设置脚本'],
    ['scripts/release-manager.js', '发布管理器'],
    ['scripts/generate-update-manifest.js', '清单生成器']
  ]
  
  scripts.forEach(([filePath, description]) => {
    checkFileExists(path.join(__dirname, '..', filePath), description)
  })
}

// 检查构建配置
function checkBuildFiles() {
  console.log('\n🏗️ 检查构建配置...')
  
  const buildFiles = [
    ['build/entitlements.mac.plist', 'macOS权限文件'],
    ['.env.example', '环境变量模板']
  ]
  
  buildFiles.forEach(([filePath, description]) => {
    checkFileExists(path.join(__dirname, '..', filePath), description)
  })
}

// 检查文档
function checkDocumentation() {
  console.log('\n📚 检查文档文件...')
  
  const docs = [
    ['docs/自动更新配置指南.md', '配置指南'],
    ['docs/OSS上传操作指南.md', 'OSS操作指南'],
    ['docs/自动更新故障排除.md', '故障排除指南'],
    ['docs/AUTO_UPDATE_README.md', '功能说明'],
    ['AUTO_UPDATE_IMPLEMENTATION_SUMMARY.md', '实现总结']
  ]
  
  docs.forEach(([filePath, description]) => {
    checkFileExists(path.join(__dirname, '..', filePath), description)
  })
}

// 检查环境变量
function checkEnvironment() {
  console.log('\n🌍 检查环境配置...')
  
  const envPath = path.join(__dirname, '../.env')
  if (fs.existsSync(envPath)) {
    console.log('✅ .env 文件存在')
    
    const envContent = fs.readFileSync(envPath, 'utf8')
    const requiredVars = [
      'OSS_ACCESS_KEY_ID',
      'OSS_ACCESS_KEY_SECRET', 
      'OSS_BUCKET',
      'OSS_REGION',
      'UPDATE_SERVER_URL'
    ]
    
    requiredVars.forEach(varName => {
      if (envContent.includes(varName)) {
        console.log(`✅ 环境变量: ${varName}`)
      } else {
        console.log(`⚠️  环境变量未配置: ${varName}`)
      }
    })
  } else {
    console.log('⚠️  .env 文件不存在，请从 .env.example 复制并配置')
  }
}

// 检查主进程代码
function checkMainProcess() {
  console.log('\n⚡ 检查主进程集成...')
  
  const mainPath = path.join(__dirname, '../src/main/index.ts')
  if (!fs.existsSync(mainPath)) return
  
  const mainContent = fs.readFileSync(mainPath, 'utf8')
  
  const checks = [
    ['electron-updater', 'electron-updater 导入'],
    ['autoUpdater.setFeedURL', '更新服务器配置'],
    ['autoUpdater.on', '更新事件监听'],
    ['check-for-updates', '检查更新IPC处理'],
    ['download-update', '下载更新IPC处理'],
    ['install-update', '安装更新IPC处理']
  ]
  
  checks.forEach(([searchText, description]) => {
    if (mainContent.includes(searchText)) {
      console.log(`✅ ${description}`)
    } else {
      console.log(`❌ 缺少: ${description}`)
      hasErrors = true
    }
  })
}

// 主函数
function main() {
  checkPackageJson()
  checkSourceFiles()
  checkScriptFiles()
  checkBuildFiles()
  checkDocumentation()
  checkEnvironment()
  checkMainProcess()
  
  console.log('\n' + '='.repeat(50))
  
  if (hasErrors) {
    console.log('❌ 配置验证失败，请检查上述错误')
    console.log('\n💡 建议:')
    console.log('1. 确保所有必要文件都存在')
    console.log('2. 检查 package.json 配置')
    console.log('3. 配置 .env 环境变量')
    console.log('4. 参考文档进行修复')
    process.exit(1)
  } else {
    console.log('✅ 自动更新配置验证通过！')
    console.log('\n🚀 下一步:')
    console.log('1. 配置 .env 文件中的阿里云OSS信息')
    console.log('2. 运行 npm run setup:oss 初始化OSS')
    console.log('3. 运行 npm run release 发布第一个版本')
    console.log('4. 测试自动更新功能')
  }
}

// 运行验证
main()
