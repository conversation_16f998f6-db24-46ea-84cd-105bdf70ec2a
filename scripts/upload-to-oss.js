#!/usr/bin/env node

/**
 * 阿里云OSS上传脚本
 * 用于将构建产物上传到阿里云OSS，支持自动更新
 */

const OSS = require('ali-oss')
const fs = require('fs')
const path = require('path')
const crypto = require('crypto')

// 配置信息（从环境变量读取）
const config = {
  region: process.env.OSS_REGION || 'oss-cn-hangzhou',
  accessKeyId: process.env.OSS_ACCESS_KEY_ID,
  accessKeySecret: process.env.OSS_ACCESS_KEY_SECRET,
  bucket: process.env.OSS_BUCKET,
  endpoint: process.env.OSS_ENDPOINT
}

// 验证配置
function validateConfig() {
  const required = ['accessKeyId', 'accessKeySecret', 'bucket']
  const missing = required.filter(key => !config[key])
  
  if (missing.length > 0) {
    console.error('❌ 缺少必要的环境变量:')
    missing.forEach(key => {
      console.error(`   OSS_${key.toUpperCase()}`)
    })
    console.error('\n请设置以上环境变量后重试。')
    process.exit(1)
  }
}

// 创建OSS客户端
function createOSSClient() {
  return new OSS(config)
}

// 计算文件SHA256
function calculateSHA256(filePath) {
  const fileBuffer = fs.readFileSync(filePath)
  const hashSum = crypto.createHash('sha256')
  hashSum.update(fileBuffer)
  return hashSum.digest('hex')
}

// 获取文件信息
function getFileInfo(filePath) {
  const stats = fs.statSync(filePath)
  return {
    size: stats.size,
    sha256: calculateSHA256(filePath),
    lastModified: stats.mtime
  }
}

// 上传单个文件
async function uploadFile(client, localPath, remotePath) {
  try {
    console.log(`📤 上传文件: ${path.basename(localPath)}`)
    
    const result = await client.put(remotePath, localPath, {
      headers: {
        'Cache-Control': 'no-cache'
      }
    })
    
    console.log(`✅ 上传成功: ${result.url}`)
    return result
  } catch (error) {
    console.error(`❌ 上传失败: ${localPath}`)
    console.error(error.message)
    throw error
  }
}

// 生成更新清单文件
function generateUpdateManifest(version, files) {
  const manifest = {
    version,
    releaseDate: new Date().toISOString(),
    files: files.map(file => ({
      name: path.basename(file.localPath),
      url: file.result.url,
      size: file.info.size,
      sha256: file.info.sha256
    }))
  }
  
  return manifest
}

// 上传构建产物
async function uploadBuilds() {
  console.log('🚀 开始上传构建产物到阿里云OSS...\n')
  
  // 验证配置
  validateConfig()
  
  // 创建OSS客户端
  const client = createOSSClient()
  
  // 读取package.json获取版本信息
  const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'))
  const version = packageJson.version
  
  console.log(`📦 应用版本: ${version}`)
  console.log(`🪣 OSS存储桶: ${config.bucket}`)
  console.log(`🌍 OSS区域: ${config.region}\n`)
  
  // 构建产物目录
  const releaseDir = path.join(__dirname, '../release')
  
  if (!fs.existsSync(releaseDir)) {
    console.error('❌ 构建产物目录不存在，请先执行构建命令')
    process.exit(1)
  }
  
  // 获取所有构建文件
  const files = fs.readdirSync(releaseDir)
    .filter(file => {
      // 过滤需要上传的文件类型
      const ext = path.extname(file).toLowerCase()
      return ['.dmg', '.zip', '.exe', '.appimage', '.yml', '.yaml'].includes(ext)
    })
    .map(file => {
      const localPath = path.join(releaseDir, file)
      return {
        localPath,
        remotePath: `releases/${version}/${file}`,
        info: getFileInfo(localPath)
      }
    })
  
  if (files.length === 0) {
    console.error('❌ 没有找到可上传的构建文件')
    process.exit(1)
  }
  
  console.log('📋 待上传文件:')
  files.forEach(file => {
    console.log(`   ${path.basename(file.localPath)} (${(file.info.size / 1024 / 1024).toFixed(2)} MB)`)
  })
  console.log()
  
  // 上传文件
  const uploadResults = []
  for (const file of files) {
    try {
      const result = await uploadFile(client, file.localPath, file.remotePath)
      uploadResults.push({
        ...file,
        result
      })
    } catch (error) {
      console.error('❌ 上传过程中出现错误，停止上传')
      process.exit(1)
    }
  }
  
  // 生成并上传更新清单
  console.log('\n📝 生成更新清单...')
  const manifest = generateUpdateManifest(version, uploadResults)
  const manifestPath = path.join(releaseDir, 'latest.yml')
  fs.writeFileSync(manifestPath, JSON.stringify(manifest, null, 2))
  
  await uploadFile(client, manifestPath, 'releases/latest.yml')
  await uploadFile(client, manifestPath, `releases/${version}/latest.yml`)
  
  // 上传完成
  console.log('\n🎉 所有文件上传完成!')
  console.log(`📍 更新服务器URL: https://${config.bucket}.${config.region}.aliyuncs.com/releases`)
  console.log(`🔗 最新版本清单: https://${config.bucket}.${config.region}.aliyuncs.com/releases/latest.yml`)
  
  return {
    version,
    files: uploadResults,
    manifest,
    updateServerUrl: `https://${config.bucket}.${config.region}.aliyuncs.com/releases`
  }
}

// 主函数
async function main() {
  try {
    await uploadBuilds()
  } catch (error) {
    console.error('\n❌ 上传失败:', error.message)
    process.exit(1)
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main()
}

module.exports = {
  uploadBuilds,
  createOSSClient,
  validateConfig
}
