#!/usr/bin/env node

/**
 * 发布管理脚本
 * 自动化版本管理、构建、上传和发布流程
 */

const fs = require('fs')
const path = require('path')
const { execSync } = require('child_process')
const crypto = require('crypto')

// 从环境变量读取配置
require('dotenv').config()

// 获取当前版本
function getCurrentVersion() {
  const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'))
  return packageJson.version
}

// 更新版本号
function updateVersion(type = 'patch') {
  console.log(`📈 升级${type}版本...`)
  
  try {
    const output = execSync(`npm version ${type} --no-git-tag-version`, { encoding: 'utf8' })
    const newVersion = output.trim().replace('v', '')
    console.log(`✅ 版本已更新至: ${newVersion}`)
    return newVersion
  } catch (error) {
    console.error('❌ 版本更新失败:', error.message)
    throw error
  }
}

// 生成更新日志
function generateChangelog(version) {
  console.log('📝 生成更新日志...')
  
  const changelogPath = path.join(__dirname, '../CHANGELOG.md')
  const date = new Date().toISOString().split('T')[0]
  
  let changelog = ''
  if (fs.existsSync(changelogPath)) {
    changelog = fs.readFileSync(changelogPath, 'utf8')
  } else {
    changelog = '# 更新日志\n\n'
  }
  
  const newEntry = `## [${version}] - ${date}\n\n### 新增\n- 功能更新\n\n### 修复\n- Bug修复\n\n### 改进\n- 性能优化\n\n`
  
  // 在第一个版本条目之前插入新条目
  const lines = changelog.split('\n')
  const insertIndex = lines.findIndex(line => line.startsWith('## [')) || 2
  lines.splice(insertIndex, 0, ...newEntry.split('\n'))
  
  fs.writeFileSync(changelogPath, lines.join('\n'))
  console.log(`✅ 更新日志已生成: ${changelogPath}`)
}

// 构建应用
function buildApp(platforms = ['current']) {
  console.log('🔨 开始构建应用...')
  
  try {
    // 先构建渲染进程
    console.log('📦 构建渲染进程...')
    execSync('npm run build:prod', { stdio: 'inherit' })
    
    // 根据平台构建
    if (platforms.includes('all')) {
      console.log('🏗️ 构建所有平台...')
      execSync('npm run electron:build:all', { stdio: 'inherit' })
    } else if (platforms.includes('mac')) {
      console.log('🍎 构建macOS版本...')
      execSync('npm run electron:build:mac', { stdio: 'inherit' })
    } else if (platforms.includes('win')) {
      console.log('🪟 构建Windows版本...')
      execSync('npm run electron:build:win', { stdio: 'inherit' })
    } else if (platforms.includes('linux')) {
      console.log('🐧 构建Linux版本...')
      execSync('npm run electron:build:linux', { stdio: 'inherit' })
    } else {
      console.log('🔧 构建当前平台...')
      execSync('npm run electron:build', { stdio: 'inherit' })
    }
    
    console.log('✅ 应用构建完成')
  } catch (error) {
    console.error('❌ 构建失败:', error.message)
    throw error
  }
}

// 生成文件清单
function generateManifest(version) {
  console.log('📋 生成文件清单...')
  
  const releaseDir = path.join(__dirname, '../release')
  if (!fs.existsSync(releaseDir)) {
    throw new Error('构建产物目录不存在')
  }
  
  const files = fs.readdirSync(releaseDir)
    .filter(file => {
      const ext = path.extname(file).toLowerCase()
      return ['.dmg', '.zip', '.exe', '.appimage', '.yml', '.yaml'].includes(ext)
    })
    .map(file => {
      const filePath = path.join(releaseDir, file)
      const stats = fs.statSync(filePath)
      const fileBuffer = fs.readFileSync(filePath)
      const hash = crypto.createHash('sha256').update(fileBuffer).digest('hex')
      
      return {
        name: file,
        size: stats.size,
        sha256: hash,
        url: `${process.env.UPDATE_SERVER_URL}/${version}/${file}`
      }
    })
  
  const manifest = {
    version,
    releaseDate: new Date().toISOString(),
    files,
    releaseNotes: `ChatNote ${version} 版本更新`
  }
  
  // 保存清单文件
  const manifestPath = path.join(releaseDir, 'latest.yml')
  fs.writeFileSync(manifestPath, JSON.stringify(manifest, null, 2))
  
  console.log(`✅ 文件清单已生成: ${manifestPath}`)
  return manifest
}

// 上传到OSS
function uploadToOSS() {
  console.log('☁️ 上传到阿里云OSS...')
  
  try {
    execSync('npm run upload:oss', { stdio: 'inherit' })
    console.log('✅ 上传完成')
  } catch (error) {
    console.error('❌ 上传失败:', error.message)
    throw error
  }
}

// Git操作
function gitOperations(version) {
  console.log('📝 提交Git更改...')
  
  try {
    // 添加所有更改
    execSync('git add .', { stdio: 'inherit' })
    
    // 提交更改
    execSync(`git commit -m "chore: release v${version}"`, { stdio: 'inherit' })
    
    // 创建标签
    execSync(`git tag v${version}`, { stdio: 'inherit' })
    
    console.log('✅ Git操作完成')
    
    // 询问是否推送
    const readline = require('readline').createInterface({
      input: process.stdin,
      output: process.stdout
    })
    
    return new Promise((resolve) => {
      readline.question('是否推送到远程仓库? (y/N): ', (answer) => {
        readline.close()
        
        if (answer.toLowerCase() === 'y' || answer.toLowerCase() === 'yes') {
          try {
            execSync('git push', { stdio: 'inherit' })
            execSync('git push --tags', { stdio: 'inherit' })
            console.log('✅ 推送完成')
          } catch (error) {
            console.error('❌ 推送失败:', error.message)
          }
        }
        resolve()
      })
    })
  } catch (error) {
    console.error('❌ Git操作失败:', error.message)
    throw error
  }
}

// 主发布函数
async function release(options = {}) {
  const {
    versionType = 'patch',
    platforms = ['current'],
    skipBuild = false,
    skipUpload = false,
    skipGit = false
  } = options
  
  console.log('🚀 开始发布流程...\n')
  
  try {
    // 获取当前版本
    const currentVersion = getCurrentVersion()
    console.log(`📦 当前版本: ${currentVersion}`)
    
    // 更新版本号
    const newVersion = updateVersion(versionType)
    
    // 生成更新日志
    generateChangelog(newVersion)
    
    // 构建应用
    if (!skipBuild) {
      buildApp(platforms)
    }
    
    // 生成文件清单
    const manifest = generateManifest(newVersion)
    
    // 上传到OSS
    if (!skipUpload) {
      uploadToOSS()
    }
    
    // Git操作
    if (!skipGit) {
      await gitOperations(newVersion)
    }
    
    console.log('\n🎉 发布完成!')
    console.log(`📍 新版本: ${newVersion}`)
    console.log(`🔗 更新服务器: ${process.env.UPDATE_SERVER_URL}`)
    console.log(`📋 文件数量: ${manifest.files.length}`)
    
  } catch (error) {
    console.error('\n❌ 发布失败:', error.message)
    process.exit(1)
  }
}

// 命令行参数解析
function parseArgs() {
  const args = process.argv.slice(2)
  const options = {}
  
  for (let i = 0; i < args.length; i++) {
    const arg = args[i]
    
    switch (arg) {
      case '--patch':
        options.versionType = 'patch'
        break
      case '--minor':
        options.versionType = 'minor'
        break
      case '--major':
        options.versionType = 'major'
        break
      case '--all':
        options.platforms = ['all']
        break
      case '--mac':
        options.platforms = ['mac']
        break
      case '--win':
        options.platforms = ['win']
        break
      case '--linux':
        options.platforms = ['linux']
        break
      case '--skip-build':
        options.skipBuild = true
        break
      case '--skip-upload':
        options.skipUpload = true
        break
      case '--skip-git':
        options.skipGit = true
        break
      case '--help':
        console.log(`
使用方法: node scripts/release-manager.js [选项]

版本选项:
  --patch     升级补丁版本 (默认)
  --minor     升级次要版本
  --major     升级主要版本

平台选项:
  --all       构建所有平台
  --mac       仅构建macOS
  --win       仅构建Windows
  --linux     仅构建Linux

跳过选项:
  --skip-build    跳过构建
  --skip-upload   跳过上传
  --skip-git      跳过Git操作

示例:
  node scripts/release-manager.js --minor --all
  node scripts/release-manager.js --patch --mac --skip-git
        `)
        process.exit(0)
        break
    }
  }
  
  return options
}

// 如果直接运行此脚本
if (require.main === module) {
  const options = parseArgs()
  release(options)
}

module.exports = {
  release,
  updateVersion,
  buildApp,
  generateManifest,
  uploadToOSS
}
