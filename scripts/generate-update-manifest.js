#!/usr/bin/env node

/**
 * 更新清单生成器
 * 为electron-updater生成兼容的更新清单文件
 */

const fs = require('fs')
const path = require('path')
const crypto = require('crypto')
const yaml = require('js-yaml')

// 计算文件哈希
function calculateFileHash(filePath, algorithm = 'sha512') {
  const fileBuffer = fs.readFileSync(filePath)
  const hashSum = crypto.createHash(algorithm)
  hashSum.update(fileBuffer)
  return hashSum.digest('base64')
}

// 获取文件大小
function getFileSize(filePath) {
  const stats = fs.statSync(filePath)
  return stats.size
}

// 生成macOS更新清单
function generateMacManifest(version, files, baseUrl) {
  const dmgFile = files.find(f => f.name.endsWith('.dmg'))
  const zipFile = files.find(f => f.name.endsWith('.zip'))
  
  if (!dmgFile && !zipFile) {
    throw new Error('未找到macOS安装文件 (.dmg 或 .zip)')
  }
  
  const mainFile = zipFile || dmgFile
  
  const manifest = {
    version,
    releaseDate: new Date().toISOString(),
    url: `${baseUrl}/${version}/${mainFile.name}`,
    sha512: mainFile.sha512,
    size: mainFile.size
  }
  
  return manifest
}

// 生成Windows更新清单
function generateWinManifest(version, files, baseUrl) {
  const exeFile = files.find(f => f.name.endsWith('.exe'))
  
  if (!exeFile) {
    throw new Error('未找到Windows安装文件 (.exe)')
  }
  
  const manifest = {
    version,
    releaseDate: new Date().toISOString(),
    url: `${baseUrl}/${version}/${exeFile.name}`,
    sha512: exeFile.sha512,
    size: exeFile.size
  }
  
  return manifest
}

// 生成Linux更新清单
function generateLinuxManifest(version, files, baseUrl) {
  const appImageFile = files.find(f => f.name.endsWith('.AppImage'))
  
  if (!appImageFile) {
    throw new Error('未找到Linux安装文件 (.AppImage)')
  }
  
  const manifest = {
    version,
    releaseDate: new Date().toISOString(),
    url: `${baseUrl}/${version}/${appImageFile.name}`,
    sha512: appImageFile.sha512,
    size: appImageFile.size
  }
  
  return manifest
}

// 生成通用更新清单
function generateGenericManifest(version, files, baseUrl) {
  const manifest = {
    version,
    releaseDate: new Date().toISOString(),
    files: files.map(file => ({
      url: `${baseUrl}/${version}/${file.name}`,
      sha512: file.sha512,
      size: file.size
    }))
  }
  
  return manifest
}

// 主函数
function generateManifests() {
  console.log('📋 生成更新清单文件...')
  
  // 读取package.json获取版本信息
  const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'))
  const version = packageJson.version
  
  // 构建产物目录
  const releaseDir = path.join(__dirname, '../release')
  
  if (!fs.existsSync(releaseDir)) {
    console.error('❌ 构建产物目录不存在，请先执行构建命令')
    process.exit(1)
  }
  
  // 获取基础URL
  const baseUrl = process.env.UPDATE_SERVER_URL || 'https://your-bucket.oss-cn-hangzhou.aliyuncs.com/releases'
  
  console.log(`📦 版本: ${version}`)
  console.log(`🔗 基础URL: ${baseUrl}`)
  
  // 扫描构建文件
  const buildFiles = fs.readdirSync(releaseDir)
    .filter(file => {
      const ext = path.extname(file).toLowerCase()
      return ['.dmg', '.zip', '.exe', '.appimage'].includes(ext)
    })
    .map(file => {
      const filePath = path.join(releaseDir, file)
      return {
        name: file,
        path: filePath,
        size: getFileSize(filePath),
        sha512: calculateFileHash(filePath, 'sha512')
      }
    })
  
  if (buildFiles.length === 0) {
    console.error('❌ 没有找到构建文件')
    process.exit(1)
  }
  
  console.log('📁 找到构建文件:')
  buildFiles.forEach(file => {
    console.log(`   ${file.name} (${(file.size / 1024 / 1024).toFixed(2)} MB)`)
  })
  
  // 生成各平台清单
  const manifests = {}
  
  try {
    // macOS清单
    const macFiles = buildFiles.filter(f => f.name.endsWith('.dmg') || f.name.endsWith('.zip'))
    if (macFiles.length > 0) {
      manifests.mac = generateMacManifest(version, macFiles, baseUrl)
      const macYaml = yaml.dump(manifests.mac)
      fs.writeFileSync(path.join(releaseDir, 'latest-mac.yml'), macYaml)
      console.log('✅ 生成macOS清单: latest-mac.yml')
    }
    
    // Windows清单
    const winFiles = buildFiles.filter(f => f.name.endsWith('.exe'))
    if (winFiles.length > 0) {
      manifests.win = generateWinManifest(version, winFiles, baseUrl)
      const winYaml = yaml.dump(manifests.win)
      fs.writeFileSync(path.join(releaseDir, 'latest.yml'), winYaml)
      console.log('✅ 生成Windows清单: latest.yml')
    }
    
    // Linux清单
    const linuxFiles = buildFiles.filter(f => f.name.endsWith('.AppImage'))
    if (linuxFiles.length > 0) {
      manifests.linux = generateLinuxManifest(version, linuxFiles, baseUrl)
      const linuxYaml = yaml.dump(manifests.linux)
      fs.writeFileSync(path.join(releaseDir, 'latest-linux.yml'), linuxYaml)
      console.log('✅ 生成Linux清单: latest-linux.yml')
    }
    
    // 通用清单
    manifests.generic = generateGenericManifest(version, buildFiles, baseUrl)
    fs.writeFileSync(path.join(releaseDir, 'manifest.json'), JSON.stringify(manifests.generic, null, 2))
    console.log('✅ 生成通用清单: manifest.json')
    
    console.log('\n🎉 所有清单文件生成完成!')
    
  } catch (error) {
    console.error('❌ 生成清单失败:', error.message)
    process.exit(1)
  }
  
  return manifests
}

// 如果直接运行此脚本
if (require.main === module) {
  generateManifests()
}

module.exports = {
  generateManifests,
  generateMacManifest,
  generateWinManifest,
  generateLinuxManifest,
  generateGenericManifest
}
