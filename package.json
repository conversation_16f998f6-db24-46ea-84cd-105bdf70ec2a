{"name": "chat-note-app", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "electron-vite dev", "dev:test": "electron-vite dev --mode test", "dev:prod": "electron-vite dev --mode production", "build": "electron-vite build", "build:dev": "electron-vite build --mode development", "build:test": "electron-vite build --mode test", "build:prod": "electron-vite build --mode production", "preview": "electron-vite preview", "type-check": "vue-tsc --build", "electron:dev": "electron-vite dev", "electron:build": "electron-vite build && electron-builder", "electron:build:dev": "electron-vite build --mode development && electron-builder", "electron:build:test": "electron-vite build --mode test && electron-builder", "electron:build:win": "electron-vite build && electron-builder --win", "electron:build:mac": "electron-vite build && electron-builder --mac", "electron:build:linux": "electron-vite build && electron-builder --linux", "electron:build:all": "electron-vite build && electron-builder --mac --win --linux", "electron:build:mac-arm64": "electron-vite build && electron-builder --mac --arm64", "electron:build:mac-x64": "electron-vite build && electron-builder --mac --x64", "electron:build:win-x64": "electron-vite build && electron-builder --win --x64", "electron:build:linux-x64": "electron-vite build && electron-builder --linux --x64", "build:release": "npm run build:prod && npm run electron:build", "build:release:all": "npm run build:prod && npm run electron:build:all", "upload:oss": "node scripts/upload-to-oss.js", "setup:oss": "node scripts/setup-oss.js", "verify:setup": "node scripts/verify-setup.js", "release": "node scripts/release-manager.js --patch", "release:minor": "node scripts/release-manager.js --minor", "release:major": "node scripts/release-manager.js --major", "release:all": "node scripts/release-manager.js --patch --all", "release:mac": "node scripts/release-manager.js --patch --mac", "release:win": "node scripts/release-manager.js --patch --win", "release:linux": "node scripts/release-manager.js --patch --linux", "version:patch": "npm version patch && git push && git push --tags", "version:minor": "npm version minor && git push && git push --tags", "version:major": "npm version major && git push && git push --tags"}, "dependencies": {"@vicons/ionicons5": "^0.13.0", "electron-updater": "^6.6.2", "naive-ui": "^2.41.0", "pinia": "^3.0.1", "vue": "^3.5.13", "vue-router": "^4.5.0"}, "devDependencies": {"@tsconfig/node22": "^22.0.1", "@types/node": "^22.14.0", "@vitejs/plugin-vue": "^5.2.3", "@vue/tsconfig": "^0.7.0", "ali-oss": "^6.21.0", "dotenv": "^16.4.7", "electron": "^36.3.2", "electron-builder": "^26.0.12", "electron-vite": "^3.1.0", "js-yaml": "^4.1.0", "less": "^4.3.0", "npm-run-all2": "^7.0.2", "typescript": "~5.8.0", "vite": "^6.2.4", "vite-plugin-vue-devtools": "^7.7.2", "vue-tsc": "^2.2.8"}, "main": "out/main/index.js", "build": {"appId": "com.chatnote.app", "productName": "ChatNote", "directories": {"output": "release"}, "files": ["out/**/*", "node_modules/**/*", "package.json"], "publish": {"provider": "generic", "url": "https://your-oss-bucket.oss-cn-hangzhou.aliyuncs.com/releases"}, "mac": {"category": "public.app-category.productivity", "target": [{"target": "dmg", "arch": ["x64", "arm64"]}, {"target": "zip", "arch": ["x64", "arm64"]}], "hardenedRuntime": true, "gatekeeperAssess": false, "entitlements": "build/entitlements.mac.plist", "entitlementsInherit": "build/entitlements.mac.plist"}, "win": {"target": [{"target": "nsis", "arch": ["x64"]}], "publisherName": "ChatNote", "verifyUpdateCodeSignature": false}, "linux": {"target": [{"target": "AppImage", "arch": ["x64"]}], "category": "Office"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true, "shortcutName": "ChatNote"}}}