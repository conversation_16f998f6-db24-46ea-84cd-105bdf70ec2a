# 富文本编辑器键盘快捷键

## 📋 概述

富文本编辑器现在支持多种键盘快捷键来发送消息，提供更好的用户体验。

## ⌨️ 支持的快捷键

### 发送消息快捷键

| 快捷键 | 平台 | 说明 |
|--------|------|------|
| `Cmd+Enter` | macOS | Mac 系统的标准发送快捷键 |
| `Ctrl+Enter` | Windows/Linux | Windows 和 Linux 系统的发送快捷键 |
| `Shift+Enter` | 所有平台 | 通用的发送快捷键，适用于所有操作系统 |

### 使用场景

- **Cmd+Enter**: Mac 用户习惯的快捷键组合
- **Ctrl+Enter**: Windows 和 Linux 用户熟悉的快捷键
- **Shift+Enter**: 跨平台通用快捷键，特别适合：
  - 不确定操作系统的场景
  - 单手操作的情况
  - 与其他应用保持一致的体验

## 🔧 技术实现

### 双重绑定策略

为了确保快捷键的可靠性，我们实现了双重绑定策略：

1. **Quill 内置键盘绑定**（主要方案）
2. **DOM 原生事件监听**（备用方案）

### Quill 键盘绑定

```typescript
// Cmd+Enter (Mac)
quill.keyboard.addBinding({
  key: 'Enter',
  metaKey: true
}, handleSend)

// Ctrl+Enter (Windows/Linux)
quill.keyboard.addBinding({
  key: 'Enter',
  ctrlKey: true
}, handleSend)

// Shift+Enter (所有平台)
quill.keyboard.addBinding({
  key: 'Enter',
  shiftKey: true
}, handleSend)
```

### DOM 事件监听

```typescript
const keydownHandler = (event: KeyboardEvent) => {
  if (event.key === 'Enter' && (event.metaKey || event.ctrlKey || event.shiftKey)) {
    event.preventDefault()
    event.stopPropagation()
    handleSend()
  }
}
```

## 🧪 测试方法

### 1. 使用测试页面

访问 `/test` 路由：

1. 在富文本编辑器中输入内容
2. 尝试以下快捷键：
   - `Cmd+Enter` (Mac)
   - `Ctrl+Enter` (Windows/Linux)
   - `Shift+Enter` (所有平台)
3. 观察控制台日志确认快捷键被正确检测

### 2. 调试日志

成功检测快捷键时的日志输出：

```
🎹 检测到按键: {key: "Enter", metaKey: false, ctrlKey: false, shiftKey: true, code: "Enter"}
🔥 DOM事件检测到快捷键: {type: "Shift+Enter"}
📤 RichTextEditor.handleSend 开始执行
```

### 3. 测试按钮

测试页面提供了"测试键盘事件"按钮，可以模拟快捷键事件：

- 模拟 `Cmd+Enter` 事件
- 模拟 `Shift+Enter` 事件

## 🎯 用户体验优化

### 1. 直观的提示

- 测试页面显示所有支持的快捷键
- 编辑器占位符文本包含快捷键提示
- 清晰的键盘图标显示

### 2. 跨平台兼容

- 自动检测操作系统
- 提供通用的 `Shift+Enter` 选项
- 所有快捷键都有相同的功能

### 3. 错误处理

- 双重绑定确保可靠性
- 详细的调试日志
- 优雅的降级处理

## 🔍 故障排除

### 如果快捷键不工作

1. **检查控制台日志**：
   - 查找 `🎹 检测到按键` 日志
   - 确认按键事件被正确捕获

2. **尝试不同的快捷键**：
   - 如果 `Cmd+Enter` 不工作，尝试 `Shift+Enter`
   - 测试所有三种快捷键组合

3. **使用测试按钮**：
   - 点击"测试键盘事件"按钮
   - 验证事件处理器是否正常工作

4. **检查编辑器状态**：
   - 确认编辑器有内容（`hasContent.value = true`）
   - 确认编辑器未被禁用（`props.disabled = false`）

### 常见问题

**Q: 为什么添加 Shift+Enter？**
A: 提供跨平台的通用快捷键，特别适合不确定用户操作系统的场景。

**Q: 快捷键优先级是什么？**
A: 所有快捷键具有相同的优先级和功能，用户可以选择最舒适的组合。

**Q: 是否会与其他快捷键冲突？**
A: 我们使用 `event.preventDefault()` 和 `event.stopPropagation()` 来防止冲突。

## 📁 相关文件

- `src/components/RichTextEditor.vue` - 主要实现文件
- `src/views/TestView.vue` - 测试页面
- `DEBUGGING_GUIDE.md` - 调试指南

## 🚀 未来扩展

可以考虑添加的其他快捷键：

- `Enter` - 插入换行（当前行为）
- `Ctrl+S` / `Cmd+S` - 保存草稿
- `Ctrl+Z` / `Cmd+Z` - 撤销
- `Ctrl+Y` / `Cmd+Y` - 重做
- `Ctrl+B` / `Cmd+B` - 加粗
- `Ctrl+I` / `Cmd+I` - 斜体

## 📝 更新日志

### v1.1.0 (当前版本)
- ✅ 新增 `Shift+Enter` 快捷键支持
- ✅ 更新测试页面说明
- ✅ 改进调试日志输出
- ✅ 增强跨平台兼容性
