import { app, BrowserWindow, ipcMain, Menu, shell } from 'electron'
import { join } from 'node:path'

// 使用CommonJS方式导入electron-updater
const { autoUpdater } = require('electron-updater')

// The built directory structure for electron-vite
//
// ├─┬─┬ out
// │ │ ├── main
// │ │ ├── preload
// │ │ └── renderer
// │
process.env.DIST = join(__dirname, '../renderer')
process.env.VITE_PUBLIC = app.isPackaged ? process.env.DIST : join(process.env.DIST, '../../public')

// 确保路径正确设置
const RENDERER_DIST = join(__dirname, '../renderer')
const VITE_PUBLIC = app.isPackaged ? RENDERER_DIST : join(RENDERER_DIST, '../../public')

// electron-vite 会自动设置 VITE_DEV_SERVER_URL

// 预加载脚本路径
const PRELOAD_PATH = app.isPackaged
  ? join(__dirname, '../preload/index.js')
  : join(__dirname, '../preload/index.js')

let win: BrowserWindow | null

// 自动更新配置
if (app.isPackaged) {
  // 配置更新服务器URL（将在构建时设置）
  autoUpdater.setFeedURL({
    provider: 'generic',
    url: process.env.UPDATE_SERVER_URL || 'https://your-oss-bucket.oss-cn-hangzhou.aliyuncs.com/releases'
  })

  // 自动更新事件监听
  autoUpdater.on('checking-for-update', () => {
    console.log('Checking for update...')
    win?.webContents.send('update-checking')
  })

  autoUpdater.on('update-available', (info: any) => {
    console.log('Update available:', info)
    win?.webContents.send('update-available', info)
  })

  autoUpdater.on('update-not-available', (info: any) => {
    console.log('Update not available:', info)
    win?.webContents.send('update-not-available', info)
  })

  autoUpdater.on('error', (err: any) => {
    console.error('Update error:', err)
    // 只在用户主动检查更新时才发送错误到渲染进程
    if (isManualCheck && win) {
      win.webContents.send('update-error', err.message || String(err), true)
    }
    // 自动检查更新的错误只记录在控制台，不弹出提示
  })

  autoUpdater.on('download-progress', (progressObj: any) => {
    console.log('Download progress:', progressObj)
    win?.webContents.send('update-download-progress', progressObj)
  })

  autoUpdater.on('update-downloaded', (info: any) => {
    console.log('Update downloaded:', info)
    win?.webContents.send('update-downloaded', info)
  })
}

function createWindow() {
  // 调试信息
  console.log('Environment variables:')
  console.log('VITE_DEV_SERVER_URL:', process.env.VITE_DEV_SERVER_URL)
  console.log('DIST:', process.env.DIST)
  console.log('NODE_ENV:', process.env.NODE_ENV)
  console.log('app.isPackaged:', app.isPackaged)

  win = new BrowserWindow({
    width: 1200,
    height: 800,
    minWidth: 800,
    minHeight: 600,
    icon: join(VITE_PUBLIC, 'favicon.ico'),
    webPreferences: {
      preload: PRELOAD_PATH,
      nodeIntegration: false,
      contextIsolation: true,
    },
    titleBarStyle: process.platform === 'darwin' ? 'hiddenInset' : 'hidden', // 根据平台设置标题栏样式
    show: false, // 先不显示，等加载完成后再显示
  })

  // Test active push message to Renderer-process.
  win.webContents.on('did-finish-load', () => {
    win?.webContents.send('main-process-message', (new Date).toLocaleString())
    win?.show() // 加载完成后显示窗口
  })

  // 在开发模式下加载开发服务器，生产模式下加载构建后的文件
  if (process.env.VITE_DEV_SERVER_URL) {
    console.log('Loading dev server URL:', process.env.VITE_DEV_SERVER_URL)
    win.loadURL(process.env.VITE_DEV_SERVER_URL)
    win.webContents.openDevTools()
  } else if (!app.isPackaged && process.env.NODE_ENV === 'development') {
    // 开发模式但没有设置 VITE_DEV_SERVER_URL，尝试默认地址
    const devServerUrl = 'http://127.0.0.1:3000'
    console.log('Loading default dev server URL:', devServerUrl)
    win.loadURL(devServerUrl)
    win.webContents.openDevTools()
  } else {
    // 生产模式下加载构建后的文件
    const indexPath = join(RENDERER_DIST, 'index.html')
    console.log('Loading file:', indexPath)
    console.log('File exists:', require('fs').existsSync(indexPath))
    win.loadFile(indexPath).catch(err => {
      console.error('Failed to load file:', err)
      // 如果加载失败，尝试显示错误信息
      win?.webContents.loadURL('data:text/html,<h1>Failed to load application</h1><p>Error: ' + err.message + '</p>')
    })
  }

  // 处理外部链接
  win.webContents.setWindowOpenHandler(({ url }) => {
    shell.openExternal(url)
    return { action: 'deny' }
  })
}

// 窗口控制 IPC 处理程序
ipcMain.handle('window-minimize', () => {
  if (win) {
    win.minimize()
  }
})

ipcMain.handle('window-maximize', () => {
  if (win) {
    if (win.isMaximized()) {
      win.unmaximize()
    } else {
      win.maximize()
    }
  }
})

ipcMain.handle('window-close', () => {
  if (win) {
    win.close()
  }
})

// 标记是否为用户主动检查更新
let isManualCheck = false

// 自动更新 IPC 处理程序
ipcMain.handle('check-for-updates', async (event, isManual = false) => {
  if (app.isPackaged) {
    try {
      isManualCheck = isManual // 根据参数设置是否为手动检查
      return await autoUpdater.checkForUpdatesAndNotify()
    } catch (error) {
      console.error('Check for updates error:', error)
      // 只有用户主动检查时，才发送错误到渲染进程
      if (isManual && win) {
        win.webContents.send('update-error', error instanceof Error ? error.message : String(error), true)
      }
      return { error: error instanceof Error ? error.message : String(error) }
    } finally {
      isManualCheck = false // 重置标记
    }
  }
  return { error: 'Updates not available in development mode' }
})

ipcMain.handle('download-update', async () => {
  if (app.isPackaged) {
    try {
      return await autoUpdater.downloadUpdate()
    } catch (error) {
      console.error('Download update error:', error)
      return { error: error instanceof Error ? error.message : String(error) }
    }
  }
  return { error: 'Updates not available in development mode' }
})

ipcMain.handle('install-update', () => {
  if (app.isPackaged) {
    autoUpdater.quitAndInstall()
  }
})

ipcMain.handle('get-app-version', () => {
  return app.getVersion()
})

// This method will be called when Electron has finished
// initialization and is ready to create browser windows.
// Some APIs can only be used after this event occurs.
app.whenReady().then(() => {
  createWindow()

  // 在生产环境下启动时检查更新
  if (app.isPackaged) {
    // 延迟5秒后检查更新，避免影响应用启动速度
    setTimeout(() => {
      autoUpdater.checkForUpdatesAndNotify().catch(err => {
        console.error('Auto update check failed:', err)
      })
    }, 5000)
  }

  // 设置应用菜单
  if (process.platform === 'darwin') {
    const template = [
      {
        label: app.getName(),
        submenu: [
          { role: 'about' },
          { type: 'separator' },
          { role: 'services' },
          { type: 'separator' },
          { role: 'hide' },
          { role: 'hideothers' },
          { role: 'unhide' },
          { type: 'separator' },
          { role: 'quit' }
        ]
      },
      {
        label: 'Edit',
        submenu: [
          { role: 'undo' },
          { role: 'redo' },
          { type: 'separator' },
          { role: 'cut' },
          { role: 'copy' },
          { role: 'paste' },
          { role: 'selectall' }
        ]
      },
      {
        label: 'View',
        submenu: [
          { role: 'reload' },
          { role: 'forceReload' },
          { role: 'toggleDevTools' },
          { type: 'separator' },
          { role: 'resetZoom' },
          { role: 'zoomIn' },
          { role: 'zoomOut' },
          { type: 'separator' },
          { role: 'togglefullscreen' }
        ]
      },
      {
        label: 'Window',
        submenu: [
          { role: 'minimize' },
          { role: 'close' }
        ]
      }
    ]
    Menu.setApplicationMenu(Menu.buildFromTemplate(template as any))
  }

  app.on('activate', () => {
    // On macOS it's common to re-create a window in the app when the
    // dock icon is clicked and there are no other windows open.
    if (BrowserWindow.getAllWindows().length === 0) {
      createWindow()
    }
  })
})

// Quit when all windows are closed, except on macOS. There, it's common
// for applications and their menu bar to stay active until the user quits
// explicitly with Cmd + Q.
app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit()
  }
})

app.on('web-contents-created', (_, contents) => {
  contents.setWindowOpenHandler(({ url }) => {
    const parsedUrl = new URL(url)

    if (parsedUrl.origin !== process.env.VITE_DEV_SERVER_URL) {
      return { action: 'deny' }
    }
    return { action: 'allow' }
  })
})
