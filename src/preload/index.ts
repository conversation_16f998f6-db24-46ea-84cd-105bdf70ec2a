import { contextBridge, ipc<PERSON><PERSON><PERSON> } from 'electron'

// --------- Expose some API to the Renderer process ---------
contextBridge.exposeInMainWorld('electronAPI', {
  on(channel, listener) {
    return ipcRenderer.on(channel, (event, ...args) => listener(event, ...args))
  },
  off(channel, listener) {
    return ipcRenderer.off(channel, listener)
  },
  send(channel, ...args) {
    return ipcRenderer.send(channel, ...args)
  },
  invoke(channel, ...args) {
    return ipcRenderer.invoke(channel, ...args)
  },
  // 窗口控制 API
  minimizeWindow() {
    return ipcRenderer.invoke('window-minimize')
  },
  maximizeWindow() {
    return ipcRenderer.invoke('window-maximize')
  },
  closeWindow() {
    return ipcRenderer.invoke('window-close')
  },

  // 自动更新 API
  checkForUpdates(isManual = false) {
    return ipcRenderer.invoke('check-for-updates', isManual)
  },
  downloadUpdate() {
    return ipcRenderer.invoke('download-update')
  },
  installUpdate() {
    return ipcRenderer.invoke('install-update')
  },
  getAppVersion() {
    return ipcRenderer.invoke('get-app-version')
  },

  // 更新事件监听
  onUpdateChecking(callback) {
    ipcRenderer.on('update-checking', callback)
  },
  onUpdateAvailable(callback) {
    ipcRenderer.on('update-available', (event, info) => callback(info))
  },
  onUpdateNotAvailable(callback) {
    ipcRenderer.on('update-not-available', (event, info) => callback(info))
  },
  onUpdateDownloadProgress(callback) {
    ipcRenderer.on('update-download-progress', (event, progress) => callback(progress))
  },
  onUpdateDownloaded(callback) {
    ipcRenderer.on('update-downloaded', (event, info) => callback(info))
  },
  onUpdateError(callback) {
    ipcRenderer.on('update-error', (event, error, showDialog = false) => callback(error, showDialog))
  }
})

// --------- Simple preload setup ---------
console.log('Preload script loaded successfully')
