import './assets/main.css'

import naive from 'naive-ui'
import { createPinia } from 'pinia'
import { createApp } from 'vue'

import App from './App.vue'
import router from './router'

// 导入CSS
// 移除了quill相关的CSS导入

// 添加调试信息
console.log('Renderer process starting...')
console.log('Environment:', {
  NODE_ENV: import.meta.env.NODE_ENV,
  MODE: import.meta.env.MODE,
  BASE_URL: import.meta.env.BASE_URL,
  DEV: import.meta.env.DEV,
  PROD: import.meta.env.PROD
})

// 检查 Electron 环境
const isElectron = typeof window !== 'undefined' && (
  !!(window as any).electronAPI ||
  (window.process && window.process.type === 'renderer') ||
  /electron/i.test(navigator.userAgent)
)
console.log('Is Electron environment:', isElectron)

const app = createApp(App)

// 添加全局错误处理
app.config.errorHandler = (err, instance, info) => {
  console.error('Vue error:', err)
  console.error('Component instance:', instance)
  console.error('Error info:', info)
}

// 添加全局警告处理
app.config.warnHandler = (msg, instance, trace) => {
  console.warn('Vue warning:', msg)
  console.warn('Component instance:', instance)
  console.warn('Trace:', trace)
}

app.use(createPinia())
app.use(router)
app.use(naive)

app.mount('#app')

console.log('Renderer process mounted successfully')
