export interface UpdateInfo {
  version: string
  releaseNotes?: string
  releaseDate?: string
}

export interface DownloadProgress {
  percent: number
  bytesPerSecond: number
  transferred: number
  total: number
}

export interface ElectronAPI {
  on: (channel: string, listener: (...args: any[]) => void) => void
  off: (channel: string, listener: (...args: any[]) => void) => void
  send: (channel: string, ...args: any[]) => void
  invoke: (channel: string, ...args: any[]) => Promise<any>
  minimizeWindow: () => Promise<void>
  maximizeWindow: () => Promise<void>
  closeWindow: () => Promise<void>

  // 自动更新 API
  checkForUpdates: (isManual?: boolean) => Promise<any>
  downloadUpdate: () => Promise<any>
  installUpdate: () => Promise<void>
  getAppVersion: () => Promise<string>

  // 更新事件监听
  onUpdateChecking: (callback: () => void) => void
  onUpdateAvailable: (callback: (info: UpdateInfo) => void) => void
  onUpdateNotAvailable: (callback: (info: any) => void) => void
  onUpdateDownloadProgress: (callback: (progress: DownloadProgress) => void) => void
  onUpdateDownloaded: (callback: (info: UpdateInfo) => void) => void
  onUpdateError: (callback: (error: string, showDialog?: boolean) => void) => void
}

declare global {
  interface Window {
    electronAPI?: ElectronAPI
    process?: {
      type?: string
    }
  }
}
