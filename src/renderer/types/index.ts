// 笔记主题
export interface NoteTheme {
  id: string
  title: string
  description?: string
  avatar?: string
  lastMessage?: string
  lastMessageTime?: Date
  createdAt: Date
  updatedAt: Date
  tags: string[]
  isStarred: boolean
  messageCount: number
}

// 笔记消息
export interface NoteMessage {
  id: string
  themeId: string
  content: string
  type: 'text' | 'rich-text' | 'image' | 'link' | 'file'
  createdAt: Date
  updatedAt?: Date
  isEdited: boolean
  metadata?: {
    fileName?: string
    fileSize?: number
    imageUrl?: string
    linkUrl?: string
    linkTitle?: string
    // 富文本相关
    isRichText?: boolean
    plainText?: string // 用于搜索和预览的纯文本版本
    // 标签相关
    tags?: string[] // 消息中包含的标签名称列表
  }
}

// 标签
export interface Tag {
  id: string
  name: string
  color: string
  createdAt: Date
  usageCount: number
}

// 用户信息
export interface User {
  id: string
  username: string
  email: string
  avatar?: string
  isVip: boolean
  vipExpireTime?: Date
  createdAt: Date
}

// 应用设置
export interface AppSettings {
  theme: 'light' | 'dark' | 'auto'
  fontSize: 'small' | 'medium' | 'large'
  autoSave: boolean
  autoSaveInterval: number // 秒
  showTimestamp: boolean
  enableNotifications: boolean
  language: 'zh-CN' | 'en-US'
}

// 搜索结果
export interface SearchResult {
  type: 'theme' | 'message'
  item: NoteTheme | NoteMessage
  highlightText?: string
}

// 导航菜单项
export interface NavMenuItem {
  id: string
  label: string
  icon: string
  path?: string
  active: boolean
}

// 服务端创建主题请求类型
export interface CreateNoteTopicRequest {
  title: string
  description?: string
  avatar?: string
}

// 服务端创建主题响应类型
export interface CreateNoteTopicResponse {
  id: string
  user_id: string
  topic_id: string
  title: string
  description: string
  avatar: string
  created_time: number
  updated_time: number
  delete_time: number
}

// 服务端创建笔记请求类型
export interface CreateNoteRequest {
  topic_id: string
  note_type: string
  content: string
}

// 服务端创建笔记响应类型
export interface CreateNoteResponse {
  id: number
  user_id: string
  topic_id: string
  note_type: string
  content: string
  created_time: number
  updated_time: number
  delete_time: number
}

// 服务端笔记列表请求类型
export interface ListNoteRequest {
  topic_id: string
  laster_id?: number
}

// 服务端笔记列表响应类型
export interface ListNoteResponse {
  id: number
  user_id: string
  topic_id: string
  note_type: string
  content: string
  created_time: number
  updated_time: number
  delete_time: number
}

// 服务端笔记列表 API 响应类型
export interface ListNoteApiResponse {
  status: number
  message: string
  data: {
    notes: ListNoteResponse[]
    laster_id: string
  }
  time: number
  request_id?: string
}

// 应用状态
export interface AppState {
  currentThemeId: string | null
  selectedNavItem: string
  isLoading: boolean
  searchQuery: string
  searchResults: SearchResult[]
}
