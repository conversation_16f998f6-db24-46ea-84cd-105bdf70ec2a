import type { GlobalThemeOverrides } from 'naive-ui'

// 浅色主题变量覆盖
export const lightThemeOverrides: GlobalThemeOverrides = {
  common: {

    // 背景颜色
    bodyColor: 'rgb(255, 255, 255)',
    cardColor: 'rgb(255, 255, 255)',
    modalColor: 'rgb(255, 255, 255)',
    popoverColor: 'rgb(255, 255, 255)',
    tableColor: 'rgb(255, 255, 255)',

    // 文本颜色
    textColorBase: 'rgb(51, 54, 57)',
    textColor1: 'rgb(51, 54, 57)',
    textColor2: 'rgb(82, 82, 91)',
    textColor3: 'rgb(146, 146, 153)',

    // 主色调
    primaryColor: 'rgb(24, 160, 88)',
    primaryColorHover: 'rgb(54, 179, 126)',
    primaryColorPressed: 'rgb(16, 128, 68)',
    primaryColorSuppl: 'rgb(24, 160, 88)',

    // 信息色
    infoColor: 'rgb(70, 162, 230)',
    infoColorHover: 'rgb(112, 186, 240)',
    infoColorPressed: 'rgb(54, 146, 207)',

    // 成功色
    successColor: 'rgb(82, 196, 26)',
    successColorHover: 'rgb(115, 209, 61)',
    successColorPressed: 'rgb(56, 158, 13)',

    // 警告色
    warningColor: 'rgb(250, 173, 20)',
    warningColorHover: 'rgb(252, 197, 61)',
    warningColorPressed: 'rgb(224, 142, 2)',

    // 错误色
    errorColor: 'rgb(208, 58, 82)',
    errorColorHover: 'rgb(221, 107, 126)',
    errorColorPressed: 'rgb(180, 29, 56)',
  }
}

// 暗色主题变量覆盖
export const darkThemeOverrides: GlobalThemeOverrides = {
  common: {
    // 边框颜色 - 这会自动生成 --n-border-color CSS 变量
    borderColor: '#404040',

    // 分隔线颜色 - 这会自动生成 --n-divider-color CSS 变量
    dividerColor: '#404040',

    // 背景颜色
    bodyColor: 'rgb(16, 16, 20)',
    cardColor: 'rgb(24, 24, 28)',
    modalColor: 'rgb(44, 44, 50)',
    popoverColor: 'rgb(72, 72, 78)',
    tableColor: 'rgb(24, 24, 28)',

    // 文本颜色
    textColorBase: 'rgb(255, 255, 255)',
    textColor1: 'rgb(255, 255, 255)',
    textColor2: 'rgb(224, 224, 230)',
    textColor3: 'rgb(146, 146, 153)',

    // 主色调
    primaryColor: 'rgb(82, 196, 26)',
    primaryColorHover: 'rgb(115, 209, 61)',
    primaryColorPressed: 'rgb(56, 158, 13)',
    primaryColorSuppl: 'rgb(82, 196, 26)',

    // 信息色
    infoColor: 'rgb(70, 162, 230)',
    infoColorHover: 'rgb(112, 186, 240)',
    infoColorPressed: 'rgb(54, 146, 207)',

    // 成功色
    successColor: 'rgb(82, 196, 26)',
    successColorHover: 'rgb(115, 209, 61)',
    successColorPressed: 'rgb(56, 158, 13)',

    // 警告色
    warningColor: 'rgb(250, 173, 20)',
    warningColorHover: 'rgb(252, 197, 61)',
    warningColorPressed: 'rgb(224, 142, 2)',

    // 错误色
    errorColor: 'rgb(208, 58, 82)',
    errorColorHover: 'rgb(221, 107, 126)',
    errorColorPressed: 'rgb(180, 29, 56)',
  }
}

// 导出主题配置函数
export function getThemeOverrides(isDark: boolean): GlobalThemeOverrides {
  return isDark ? darkThemeOverrides : lightThemeOverrides
}
