// 应用配置
export const config = {
  // API 配置
  api: {
    baseUrl: import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000',
    timeout: 10000, // 10秒超时
    retryAttempts: 3,
    retryDelay: 1000, // 1秒重试延迟
  },

  // 应用信息
  app: {
    name: import.meta.env.VITE_APP_NAME || 'Chat Note App',
    version: import.meta.env.VITE_APP_VERSION || '1.0.0',
    devMode: import.meta.env.VITE_DEV_MODE === 'true',
    debugMode: import.meta.env.VITE_DEBUG_MODE === 'true',
  },

  // 功能开关
  features: {
    apiSync: import.meta.env.VITE_ENABLE_API_SYNC === 'true',
    offlineMode: import.meta.env.VITE_ENABLE_OFFLINE_MODE !== 'false', // 默认开启
    autoSave: import.meta.env.VITE_ENABLE_AUTO_SAVE !== 'false', // 默认开启
    authEnabled: import.meta.env.VITE_AUTH_ENABLED === 'true',
  },



  // 存储配置
  storage: {
    prefix: import.meta.env.VITE_STORAGE_PREFIX || 'chat_note_',
    maxSize: import.meta.env.VITE_MAX_STORAGE_SIZE || '50MB',
    keys: {
      themes: 'noteThemes',
      messages: 'noteMessages',
      tags: 'noteTags',
      settings: 'appSettings',
      authToken: 'auth_token',
      lastSync: 'last_sync_time',
    },
  },

  // 认证配置
  auth: {
    // 不需要 Authorization header 的接口列表
    noAuthEndpoints: [
      '/api/v1/user/send-verification-code',
      '/api/v1/user/register',
      '/api/v1/user/login',
      '/api/v1/user/reset-password',
      '/api/v1/system/config',
    ],
    // 支持通配符匹配的接口模式
    noAuthPatterns: [
      '/api/v1/user/send-*',
      '/api/v1/user/login*',
      '/api/v1/user/register*',
      '/api/v1/system/*',
    ],
    jwtExpiry: import.meta.env.VITE_JWT_EXPIRY || '7d',
    refreshThreshold: 24 * 60 * 60 * 1000, // 24小时前刷新token
  },

  // 自动保存配置
  autoSave: {
    interval: 5000, // 5秒自动保存间隔
    debounceDelay: 1000, // 1秒防抖延迟
  },

  // 同步配置
  sync: {
    interval: 30000, // 30秒同步间隔
    batchSize: 100, // 批量同步大小
    maxRetries: 3,
  },

  // UI 配置
  ui: {
    defaultTheme: 'auto' as 'light' | 'dark' | 'auto',
    defaultFontSize: 'medium' as 'small' | 'medium' | 'large',
    animationDuration: 300, // 动画持续时间（毫秒）
    toastDuration: 3000, // 提示消息持续时间（毫秒）
  },

  // 编辑器配置
  editor: {
    placeholder: '输入您的笔记内容...',
    autoFocus: true,
    spellCheck: false,
    maxLength: 10000, // 最大字符数
    mention: {
      triggerChar: '#',
      allowedChars: /^[A-Za-z\sÀ-ÿ\u4e00-\u9fa5]*$/,
      maxSuggestions: 10,
    },
  },

  // 搜索配置
  search: {
    minQueryLength: 2, // 最小搜索长度
    maxResults: 50, // 最大搜索结果数
    debounceDelay: 300, // 搜索防抖延迟
  },

  // 导出配置
  export: {
    formats: ['json', 'markdown', 'html'] as const,
    maxFileSize: 10 * 1024 * 1024, // 10MB
  },

  // 开发配置
  dev: {
    enableConsoleLog: import.meta.env.DEV,
    enablePerformanceMonitoring: import.meta.env.DEV,
    mockApiDelay: 500, // 模拟API延迟（毫秒）
  },
}

// 类型定义
export type Config = typeof config

// 配置验证
export const validateConfig = (): boolean => {
  try {
    // 验证必要的配置项
    if (!config.api.baseUrl) {
      console.warn('API base URL is not configured')
    }

    if (config.features.apiSync && !config.features.authEnabled) {
      console.warn('API sync is enabled but authentication is disabled')
    }

    return true
  } catch (error) {
    console.error('Configuration validation failed:', error)
    return false
  }
}

// 获取存储键名
export const getStorageKey = (key: keyof typeof config.storage.keys): string => {
  return config.storage.prefix + config.storage.keys[key]
}

// 检查功能是否启用
export const isFeatureEnabled = (feature: keyof typeof config.features): boolean => {
  return config.features[feature]
}

// 获取API配置
export const getApiConfig = () => ({
  baseURL: config.api.baseUrl,
  timeout: config.api.timeout,
  headers: {
    'Content-Type': 'application/json',
  },
})

// 检查接口是否需要认证
export const needsAuth = (endpoint: string): boolean => {
  // 检查精确匹配
  if (config.auth.noAuthEndpoints.includes(endpoint)) {
    return false
  }

  // 检查通配符匹配
  for (const pattern of config.auth.noAuthPatterns) {
    if (matchPattern(endpoint, pattern)) {
      return false
    }
  }

  return true
}

// 简单的通配符匹配函数
const matchPattern = (str: string, pattern: string): boolean => {
  // 将通配符模式转换为正则表达式
  const regexPattern = pattern
    .replace(/\*/g, '.*')
    .replace(/\?/g, '.')

  const regex = new RegExp(`^${regexPattern}$`)
  return regex.test(str)
}

// 获取认证配置
export const getAuthConfig = () => config.auth

// 导出默认配置
export default config
