import { describe, it, expect, beforeEach, vi } from 'vitest'
import { themeApi } from '@/services/api'
import type { CreateNoteTopicResponse } from '@/types'

// Mock fetch
global.fetch = vi.fn()

describe('themeApi', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('create', () => {
    it('应该成功创建主题并返回转换后的数据', async () => {
      // 模拟服务端响应
      const mockServerResponse: CreateNoteTopicResponse = {
        id: '123456789',
        user_id: 'user123',
        topic_id: 'topic123',
        title: '测试主题',
        description: '这是一个测试主题',
        avatar: 'https://example.com/avatar.jpg',
        created_time: 1640995200, // 2022-01-01 00:00:00 UTC
        updated_time: 1640995200,
        delete_time: 0
      }

      const mockApiResponse = {
        status: 200,
        data: mockServerResponse
      }

      // Mock fetch 响应
      ;(fetch as any).mockResolvedValueOnce({
        ok: true,
        json: async () => mockApiResponse
      })

      // 调用 API
      const result = await themeApi.create({
        title: '测试主题',
        description: '这是一个测试主题',
        avatar: 'https://example.com/avatar.jpg'
      })

      // 验证结果
      expect(result).toEqual({
        id: '123456789',
        title: '测试主题',
        description: '这是一个测试主题',
        avatar: 'https://example.com/avatar.jpg',
        createdAt: new Date(1640995200 * 1000),
        updatedAt: new Date(1640995200 * 1000),
        tags: [],
        isStarred: false,
        messageCount: 0
      })

      // 验证 fetch 调用
      expect(fetch).toHaveBeenCalledWith(
        'http://localhost:8000/api/v1/note_topic/create',
        expect.objectContaining({
          method: 'POST',
          headers: expect.objectContaining({
            'Content-Type': 'application/json'
          }),
          body: JSON.stringify({
            title: '测试主题',
            description: '这是一个测试主题',
            avatar: 'https://example.com/avatar.jpg'
          })
        })
      )
    })

    it('应该处理空描述和头像', async () => {
      const mockServerResponse: CreateNoteTopicResponse = {
        id: '123456789',
        user_id: 'user123',
        topic_id: 'topic123',
        title: '简单主题',
        description: '',
        avatar: '',
        created_time: 1640995200,
        updated_time: 1640995200,
        delete_time: 0
      }

      ;(fetch as any).mockResolvedValueOnce({
        ok: true,
        json: async () => ({ status: 200, data: mockServerResponse })
      })

      const result = await themeApi.create({
        title: '简单主题'
      })

      expect(result.description).toBeUndefined()
      expect(result.avatar).toBeUndefined()
    })
  })

  describe('getAll', () => {
    it('应该获取所有主题并转换数据格式', async () => {
      const mockServerResponse: CreateNoteTopicResponse[] = [
        {
          id: '1',
          user_id: 'user123',
          topic_id: 'topic1',
          title: '主题1',
          description: '描述1',
          avatar: 'avatar1.jpg',
          created_time: 1640995200,
          updated_time: 1640995200,
          delete_time: 0
        },
        {
          id: '2',
          user_id: 'user123',
          topic_id: 'topic2',
          title: '主题2',
          description: '',
          avatar: '',
          created_time: 1640995300,
          updated_time: 1640995300,
          delete_time: 0
        }
      ]

      ;(fetch as any).mockResolvedValueOnce({
        ok: true,
        json: async () => ({ status: 200, data: mockServerResponse })
      })

      const result = await themeApi.getAll()

      expect(result).toHaveLength(2)
      expect(result[0]).toEqual({
        id: '1',
        title: '主题1',
        description: '描述1',
        avatar: 'avatar1.jpg',
        createdAt: new Date(1640995200 * 1000),
        updatedAt: new Date(1640995200 * 1000),
        tags: [],
        isStarred: false,
        messageCount: 0
      })
      expect(result[1].description).toBeUndefined()
      expect(result[1].avatar).toBeUndefined()
    })
  })
})
