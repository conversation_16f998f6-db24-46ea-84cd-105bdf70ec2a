<script setup lang="ts">
import { NConfigProvider, NMessageProvider, darkTheme, dateZhCN, zhCN } from 'naive-ui'
import { computed, onMounted, provide, ref } from 'vue'
import { RouterView } from 'vue-router'
import MainLayout from './components/MainLayout.vue'
import TitleBar from './components/TitleBar.vue'
import { getThemeOverrides } from './config/theme'
import { useAuthStore } from './stores/auth'

// 认证状态
const authStore = useAuthStore()

// 主题配置
const isDark = ref(false)
const theme = computed(() => isDark.value ? darkTheme : null)
const themeOverrides = computed(() => getThemeOverrides(isDark.value))

// 主题切换函数
const toggleTheme = () => {
  isDark.value = !isDark.value
}

// 提供主题状态给子组件
provide('isDark', isDark)
provide('toggleTheme', toggleTheme)

// 监听系统主题变化
if (window.matchMedia) {
  const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)')
  isDark.value = mediaQuery.matches

  mediaQuery.addEventListener('change', (e) => {
    isDark.value = e.matches
  })
}

// 初始化认证状态
onMounted(() => {
  authStore.initializeAuth()
})
</script>

<template>
  <NConfigProvider
    :theme="theme"
    :theme-overrides="themeOverrides"
    :locale="zhCN"
    :date-locale="dateZhCN"
    class="app-container"
  >
    <NMessageProvider>
      <!-- 顶部标题栏 - 始终显示 -->
      <TitleBar />

      <!-- 根据认证状态显示不同的界面 -->
      <MainLayout v-if="authStore.isAuthenticated" />
      <div v-else class="auth-container">
        <RouterView />
      </div>
    </NMessageProvider>
  </NConfigProvider>
</template>

<style>
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body, #app {
  width: 100%;
  height: 100%;
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  overflow: hidden;
}

.app-container {
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  position: relative;
}

.auth-container {
  width: 100vw;
  height: 100vh;
  padding-top: 32px; /* 为标题栏留出空间 */
  overflow: auto;
}

/* 自定义滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.3);
}

/* 暗色主题下的滚动条 */
.dark ::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
}

.dark ::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.3);
}
</style>
