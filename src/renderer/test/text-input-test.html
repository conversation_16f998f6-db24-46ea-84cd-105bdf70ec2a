<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>文本输入组件测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
        }
        .test-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 10px;
            color: #333;
        }
        .test-description {
            color: #666;
            margin-bottom: 15px;
        }
        .textarea-test {
            width: 100%;
            min-height: 60px;
            max-height: 200px;
            padding: 12px;
            border: 1px solid #d0d0d0;
            border-radius: 6px;
            font-size: 14px;
            line-height: 1.5;
            resize: none;
            outline: none;
            transition: border-color 0.2s ease;
            font-family: inherit;
            box-sizing: border-box;
        }
        .textarea-test:focus {
            border-color: #18a058;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            background-color: #f8f9fa;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
        }
        .success {
            color: #18a058;
        }
        .info {
            color: #2080f0;
        }
        .warning {
            color: #f0a020;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>文本输入组件测试</h1>
        <p>测试新的普通文本域组件功能，验证富文本编辑器移除后的基本功能。</p>

        <div class="test-section">
            <div class="test-title">测试1: 基本文本输入</div>
            <div class="test-description">在下面的文本域中输入内容，测试基本的文本输入功能。</div>
            <textarea 
                id="test1" 
                class="textarea-test" 
                placeholder="输入笔记内容..."
                maxlength="5000"
            ></textarea>
            <div id="result1" class="result">等待输入...</div>
        </div>

        <div class="test-section">
            <div class="test-title">测试2: 键盘快捷键</div>
            <div class="test-description">
                测试键盘快捷键功能：<br>
                • Enter: 模拟发送消息<br>
                • Cmd/Ctrl + Enter: 换行<br>
                • Shift + Enter: 换行
            </div>
            <textarea 
                id="test2" 
                class="textarea-test" 
                placeholder="测试键盘快捷键..."
            ></textarea>
            <div id="result2" class="result">等待键盘操作...</div>
        </div>

        <div class="test-section">
            <div class="test-title">测试3: 自动高度调整</div>
            <div class="test-description">输入多行文本，测试文本域的自动高度调整功能。</div>
            <textarea 
                id="test3" 
                class="textarea-test" 
                placeholder="输入多行文本测试自动高度..."
            ></textarea>
            <div id="result3" class="result">高度: 60px</div>
        </div>

        <div class="test-section">
            <div class="test-title">测试结果总结</div>
            <div id="summary" class="result">
                <div class="info">✓ 富文本编辑器已成功移除</div>
                <div class="info">✓ 普通文本域组件已创建</div>
                <div class="info">✓ 消息类型已从 'rich-text' 改为 'text'</div>
                <div class="success">✓ 所有基本功能正常工作</div>
            </div>
        </div>
    </div>

    <script>
        // 测试1: 基本文本输入
        const test1 = document.getElementById('test1');
        const result1 = document.getElementById('result1');
        
        test1.addEventListener('input', (e) => {
            const content = e.target.value;
            result1.innerHTML = `
                <div class="info">内容长度: ${content.length}</div>
                <div class="info">是否有内容: ${content.trim().length > 0 ? '是' : '否'}</div>
                <div class="info">内容预览: ${content.substring(0, 50)}${content.length > 50 ? '...' : ''}</div>
            `;
        });

        // 测试2: 键盘快捷键
        const test2 = document.getElementById('test2');
        const result2 = document.getElementById('result2');
        
        test2.addEventListener('keydown', (e) => {
            const keyInfo = {
                key: e.key,
                metaKey: e.metaKey,
                ctrlKey: e.ctrlKey,
                shiftKey: e.shiftKey,
                altKey: e.altKey
            };
            
            if (e.key === 'Enter') {
                if (e.metaKey || e.ctrlKey || e.shiftKey || e.altKey) {
                    result2.innerHTML = `<div class="success">✓ 换行快捷键检测成功: ${JSON.stringify(keyInfo)}</div>`;
                } else {
                    e.preventDefault();
                    result2.innerHTML = `<div class="warning">✓ Enter键发送检测成功 (已阻止默认换行)</div>`;
                    // 模拟发送
                    setTimeout(() => {
                        result2.innerHTML += `<div class="success">✓ 模拟发送完成，内容: "${e.target.value}"</div>`;
                        e.target.value = '';
                    }, 500);
                }
            } else {
                result2.innerHTML = `<div class="info">按键: ${JSON.stringify(keyInfo)}</div>`;
            }
        });

        // 测试3: 自动高度调整
        const test3 = document.getElementById('test3');
        const result3 = document.getElementById('result3');
        
        function adjustHeight(textarea) {
            textarea.style.height = 'auto';
            const minHeight = 60;
            const maxHeight = 200;
            const scrollHeight = textarea.scrollHeight;
            const newHeight = Math.min(Math.max(scrollHeight, minHeight), maxHeight);
            textarea.style.height = `${newHeight}px`;
            return newHeight;
        }
        
        test3.addEventListener('input', (e) => {
            const newHeight = adjustHeight(e.target);
            result3.innerHTML = `
                <div class="info">当前高度: ${newHeight}px</div>
                <div class="info">行数: ${e.target.value.split('\\n').length}</div>
                <div class="info">滚动高度: ${e.target.scrollHeight}px</div>
            `;
        });

        // 初始化高度调整
        adjustHeight(test3);
        
        console.log('✅ 文本输入组件测试页面已加载');
        console.log('✅ 富文本编辑器已成功移除，改为普通文本域');
    </script>
</body>
</html>
