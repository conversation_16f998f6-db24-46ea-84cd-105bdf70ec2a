/**
 * 图片上传功能测试
 * 用于验证修复的两个问题：
 * 1. 选择图片发送笔记还是以base64发送了
 * 2. 单独只有图片的时候enter无法发送笔记
 */

// 模拟测试数据
const testImageBase64 = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg=='

// 测试1: 验证内容检测逻辑
function testHasContentDetection() {
  console.log('🧪 测试1: 内容检测逻辑')
  
  // 模拟Quill delta数据
  const deltaWithTextOnly = {
    ops: [
      { insert: 'Hello world' }
    ]
  }
  
  const deltaWithImageOnly = {
    ops: [
      { insert: { image: 'http://example.com/image.jpg' } }
    ]
  }
  
  const deltaWithTextAndImage = {
    ops: [
      { insert: 'Hello ' },
      { insert: { image: 'http://example.com/image.jpg' } },
      { insert: ' world' }
    ]
  }
  
  const deltaEmpty = {
    ops: [
      { insert: '' }
    ]
  }
  
  // 测试内容检测函数
  function hasContent(delta, text) {
    const hasTextContent = text.trim().length > 0
    const hasImageContent = delta?.ops?.some(op => op.insert && op.insert.image) || false
    return hasTextContent || hasImageContent
  }
  
  console.log('✅ 纯文本内容:', hasContent(deltaWithTextOnly, 'Hello world')) // 应该是 true
  console.log('✅ 纯图片内容:', hasContent(deltaWithImageOnly, '')) // 应该是 true
  console.log('✅ 文本+图片内容:', hasContent(deltaWithTextAndImage, 'Hello  world')) // 应该是 true
  console.log('✅ 空内容:', hasContent(deltaEmpty, '')) // 应该是 false
}

// 测试2: 验证内容序列化不包含base64
function testContentSerialization() {
  console.log('🧪 测试2: 内容序列化')
  
  const deltaWithBase64 = {
    ops: [
      { insert: 'Text before image' },
      { insert: { image: testImageBase64 } },
      { insert: 'Text after image' }
    ]
  }
  
  const deltaWithUrl = {
    ops: [
      { insert: 'Text before image' },
      { insert: { image: 'http://example.com/image.jpg' } },
      { insert: 'Text after image' }
    ]
  }
  
  // 模拟处理函数
  function processContent(delta) {
    const processedDelta = {
      ...delta,
      ops: delta.ops.map(op => {
        if (op.insert && op.insert.image) {
          if (op.insert.image.startsWith('data:image/')) {
            console.log('⚠️ 检测到base64图片，这不应该发生在发送时')
          }
          return op
        }
        return op
      })
    }
    return JSON.stringify(processedDelta)
  }
  
  console.log('✅ 处理base64内容:')
  const base64Result = processContent(deltaWithBase64)
  console.log('包含base64:', base64Result.includes('data:image/'))
  
  console.log('✅ 处理URL内容:')
  const urlResult = processContent(deltaWithUrl)
  console.log('包含URL:', urlResult.includes('http://example.com/image.jpg'))
}

// 测试3: 验证图片上传流程
function testImageUploadFlow() {
  console.log('🧪 测试3: 图片上传流程')
  
  // 模拟上传成功的响应
  const mockUploadSuccess = {
    err_code: 0,
    message: 'success',
    data: {
      url: 'http://localhost:8000/uploads/images/123456789.jpg'
    }
  }
  
  // 模拟上传失败的响应
  const mockUploadError = {
    err_code: 1,
    message: '图片格式不支持'
  }
  
  console.log('✅ 上传成功响应:', mockUploadSuccess.err_code === 0)
  console.log('✅ 上传失败响应:', mockUploadError.err_code !== 0)
  console.log('✅ 返回URL格式正确:', mockUploadSuccess.data.url.startsWith('http'))
}

// 运行所有测试
function runAllTests() {
  console.log('🚀 开始运行图片上传功能测试...')
  console.log('=' * 50)
  
  testHasContentDetection()
  console.log('-' * 30)
  
  testContentSerialization()
  console.log('-' * 30)
  
  testImageUploadFlow()
  console.log('-' * 30)
  
  console.log('🎉 所有测试完成!')
}

// 导出测试函数
export {
  testHasContentDetection,
  testContentSerialization,
  testImageUploadFlow,
  runAllTests
}

// 如果直接运行此文件，执行所有测试
if (typeof window !== 'undefined') {
  // 在浏览器环境中运行
  window.imageUploadTest = {
    testHasContentDetection,
    testContentSerialization,
    testImageUploadFlow,
    runAllTests
  }
  
  // 自动运行测试
  runAllTests()
}
