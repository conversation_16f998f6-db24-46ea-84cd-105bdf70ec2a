/**
 * 图片上传相关API
 */

// 获取API基础URL
const getApiBaseUrl = () => {
  return import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000'
}

// 获取认证token
const getAuthToken = () => {
  return localStorage.getItem('auth_token')
}

/**
 * 上传图片文件
 * @param {File} file - 图片文件对象
 * @returns {Promise<Object>} 上传结果
 */
export const uploadFile = async (file) => {
  const formData = new FormData()
  formData.append('file', file)
  
  const response = await fetch(`${getApiBaseUrl()}/api/v1/upload/image`, {
    method: 'POST',
    body: formData,
    headers: {
      'Authorization': getAuthToken()
    }
  })
  
  if (!response.ok) {
    throw new Error(`上传失败: ${response.statusText}`)
  }
  
  const result = await response.json()
  
  // 转换为前端期望的格式
  return {
    err_code: result.status === 200 ? 0 : 1,
    message: result.message || 'success',
    data: {
      url: result.data?.url || ''
    }
  }
}

/**
 * 通过URL上传图片
 * @param {Object} params - 参数对象
 * @param {string} params.url - 图片URL
 * @returns {Promise<Object>} 上传结果
 */
export const uploadImgByUrl = async ({ url }) => {
  const response = await fetch(`${getApiBaseUrl()}/api/v1/upload/image-by-url`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': getAuthToken()
    },
    body: JSON.stringify({ url })
  })
  
  if (!response.ok) {
    throw new Error(`上传失败: ${response.statusText}`)
  }
  
  const result = await response.json()
  
  // 转换为前端期望的格式
  return {
    status: result.status || 200,
    data: {
      url: result.data?.url || ''
    }
  }
}

/**
 * 上传base64图片
 * @param {string} base64Data - base64图片数据
 * @param {string} fileName - 文件名（可选）
 * @returns {Promise<Object>} 上传结果
 */
export const uploadBase64Image = async (base64Data, fileName = 'image.png') => {
  // 将base64转换为Blob
  const byteCharacters = atob(base64Data.split(',')[1])
  const byteNumbers = new Array(byteCharacters.length)
  
  for (let i = 0; i < byteCharacters.length; i++) {
    byteNumbers[i] = byteCharacters.charCodeAt(i)
  }
  
  const byteArray = new Uint8Array(byteNumbers)
  const blob = new Blob([byteArray], { type: 'image/png' })
  
  // 创建File对象
  const file = new File([blob], fileName, { type: 'image/png' })
  
  // 使用现有的uploadFile方法
  return uploadFile(file)
}

/**
 * 批量上传图片
 * @param {File[]} files - 图片文件数组
 * @param {Function} onProgress - 进度回调函数
 * @returns {Promise<Object[]>} 上传结果数组
 */
export const uploadMultipleFiles = async (files, onProgress) => {
  const results = []
  
  for (let i = 0; i < files.length; i++) {
    try {
      const result = await uploadFile(files[i])
      results.push(result)
      
      if (onProgress) {
        onProgress({
          current: i + 1,
          total: files.length,
          percentage: Math.round(((i + 1) / files.length) * 100)
        })
      }
    } catch (error) {
      results.push({
        err_code: 1,
        message: error.message,
        data: null
      })
    }
  }
  
  return results
}

/**
 * 验证图片文件
 * @param {File} file - 文件对象
 * @returns {Object} 验证结果
 */
export const validateImageFile = (file) => {
  const validTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp', 'image/bmp']
  const maxSize = 10 * 1024 * 1024 // 10MB
  
  if (!validTypes.includes(file.type)) {
    return {
      valid: false,
      message: '不支持的图片格式，请选择 JPG、PNG、GIF、WebP 或 BMP 格式的图片'
    }
  }
  
  if (file.size > maxSize) {
    return {
      valid: false,
      message: '图片文件过大，请选择小于10MB的图片'
    }
  }
  
  return {
    valid: true,
    message: '验证通过'
  }
}

/**
 * 从HTML内容中提取图片URL
 * @param {string} htmlContent - HTML内容
 * @returns {string[]} 图片URL数组
 */
export const extractImageUrls = (htmlContent) => {
  const imgRegex = /<img[^>]+src=["']([^"']+)["'][^>]*>/gi
  const urls = []
  let match
  
  while ((match = imgRegex.exec(htmlContent)) !== null) {
    urls.push(match[1])
  }
  
  return urls
}

/**
 * 从HTML内容中提取base64图片
 * @param {string} htmlContent - HTML内容
 * @returns {Object[]} base64图片数组
 */
export const extractBase64Images = (htmlContent) => {
  const base64Regex = /<img[^>]+src=["'](data:image\/[^;]+;base64,[^"']+)["'][^>]*>/gi
  const images = []
  let match
  
  while ((match = base64Regex.exec(htmlContent)) !== null) {
    images.push({
      src: match[1],
      fullMatch: match[0]
    })
  }
  
  return images
}

export default {
  uploadFile,
  uploadImgByUrl,
  uploadBase64Image,
  uploadMultipleFiles,
  validateImageFile,
  extractImageUrls,
  extractBase64Images
}
