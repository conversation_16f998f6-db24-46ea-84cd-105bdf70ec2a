/**
 * 认证配置测试工具
 * 用于测试和验证API认证配置是否正确
 */

import { needsAuth } from '@/config'
import { apiClient } from '@/utils/api'

// 测试接口认证配置
export const testAuthConfig = () => {
  console.log('=== API认证配置测试 ===')
  
  // 测试不需要认证的接口
  const noAuthEndpoints = [
    '/api/v1/user/send-verification-code',
    '/api/v1/user/register',
    '/api/v1/user/login',
    '/api/v1/user/reset-password',
    '/api/v1/system/config',
  ]
  
  console.log('不需要认证的接口:')
  noAuthEndpoints.forEach(endpoint => {
    const needAuth = needsAuth(endpoint)
    console.log(`  ${endpoint}: ${needAuth ? '❌ 需要认证' : '✅ 不需要认证'}`)
  })
  
  // 测试需要认证的接口
  const authEndpoints = [
    '/api/v1/user/profile',
    '/api/v1/notes',
    '/api/v1/note-themes',
    '/api/v1/note-tags',
    '/api/v1/note_topic/create',
    '/api/v1/note_topic/list',
  ]
  
  console.log('\n需要认证的接口:')
  authEndpoints.forEach(endpoint => {
    const needAuth = needsAuth(endpoint)
    console.log(`  ${endpoint}: ${needAuth ? '✅ 需要认证' : '❌ 不需要认证'}`)
  })
  
  // 测试通配符匹配
  const wildcardTests = [
    '/api/v1/user/send-email-code',
    '/api/v1/user/login-with-code',
    '/api/v1/system/health',
    '/api/v1/system/version',
  ]
  
  console.log('\n通配符匹配测试:')
  wildcardTests.forEach(endpoint => {
    const needAuth = needsAuth(endpoint)
    console.log(`  ${endpoint}: ${needAuth ? '需要认证' : '不需要认证'}`)
  })
}

// 测试API客户端方法
export const testApiClient = () => {
  console.log('\n=== API客户端测试 ===')
  
  // 测试token管理
  console.log('当前token:', apiClient.getAuthToken())
  
  // 设置测试token
  apiClient.setAuthToken('test-token-123')
  console.log('设置token后:', apiClient.getAuthToken())
  
  // 测试接口认证检查
  console.log('检查登录接口是否需要认证:', apiClient.needsAuth('/api/v1/user/login'))
  console.log('检查用户资料接口是否需要认证:', apiClient.needsAuth('/api/v1/user/profile'))
  
  // 清除token
  apiClient.clearAuthToken()
  console.log('清除token后:', apiClient.getAuthToken())
}

// 模拟API请求测试
export const testApiRequests = async () => {
  console.log('\n=== API请求测试 ===')
  
  try {
    // 测试不需要认证的接口（这个请求不会添加Authorization header）
    console.log('测试发送验证码接口（不需要认证）...')
    const response1 = await apiClient.post('/api/v1/user/send-verification-code', {
      email: '<EMAIL>'
    })
    console.log('发送验证码接口响应状态:', response1.status)
    
    // 设置token
    apiClient.setAuthToken('test-token-123')
    
    // 测试需要认证的接口（这个请求会添加Authorization header）
    console.log('测试用户资料接口（需要认证）...')
    const response2 = await apiClient.get('/api/v1/user/profile')
    console.log('用户资料接口响应状态:', response2.status)
    
  } catch (error) {
    console.log('API请求测试完成（可能会有网络错误，这是正常的）')
  }
}

// 运行所有测试
export const runAllTests = async () => {
  testAuthConfig()
  testApiClient()
  await testApiRequests()
  console.log('\n=== 测试完成 ===')
}
