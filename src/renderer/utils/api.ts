import { config, needsAuth } from '../config'

/**
 * API 工具类
 * 统一管理API请求配置和方法
 */
export class ApiClient {
  private baseUrl: string
  private timeout: number
  private defaultHeaders: Record<string, string>

  constructor() {
    this.baseUrl = config.api.baseUrl
    this.timeout = config.api.timeout
    this.defaultHeaders = {
      'Content-Type': 'application/json',
      'Accept': '*/*',
      'User-Agent': 'ChatNote/1.0.0'
    }
  }

  /**
   * 获取完整的API URL
   * @param endpoint API端点路径
   * @returns 完整的API URL
   */
  getUrl(endpoint: string): string {
    // 确保endpoint以/开头
    const normalizedEndpoint = endpoint.startsWith('/') ? endpoint : `/${endpoint}`
    return `${this.baseUrl}${normalizedEndpoint}`
  }

  /**
   * 发送HTTP请求
   * @param endpoint API端点
   * @param options 请求选项
   * @returns Promise<Response>
   */
  async request(endpoint: string, options: RequestInit = {}): Promise<Response> {
    const url = this.getUrl(endpoint)

    // 合并默认headers和自定义headers
    const headers: Record<string, string> = {
      ...this.defaultHeaders,
      ...(options.headers as Record<string, string> || {})
    }

    // 检查是否需要添加认证token
    if (needsAuth(endpoint)) {
      const token = localStorage.getItem('auth_token')
      if (token) {
        headers['Authorization'] = `${token}`
      }
    }

    const requestOptions: RequestInit = {
      ...options,
      headers,
    }

    try {
      const response = await fetch(url, requestOptions)

      // 如果是401错误且当前接口需要认证，可能是token过期
      if (response.status === 401 && needsAuth(endpoint)) {
        console.warn('认证失败，可能需要重新登录')
        // 这里可以触发登出逻辑或刷新token
        // 暂时只记录日志，具体处理逻辑可以在后续完善
      }

      return response
    } catch (error) {
      console.error(`API请求失败 [${options.method || 'GET'}] ${url}:`, error)
      throw error
    }
  }

  /**
   * GET请求
   * @param endpoint API端点
   * @param options 请求选项
   * @returns Promise<Response>
   */
  async get(endpoint: string, options: RequestInit = {}): Promise<Response> {
    return this.request(endpoint, { ...options, method: 'GET' })
  }

  /**
   * POST请求
   * @param endpoint API端点
   * @param data 请求数据
   * @param options 请求选项
   * @returns Promise<Response>
   */
  async post(endpoint: string, data?: any, options: RequestInit = {}): Promise<Response> {
    const requestOptions: RequestInit = {
      ...options,
      method: 'POST'
    }

    if (data) {
      requestOptions.body = JSON.stringify(data)
    }

    return this.request(endpoint, requestOptions)
  }

  /**
   * PUT请求
   * @param endpoint API端点
   * @param data 请求数据
   * @param options 请求选项
   * @returns Promise<Response>
   */
  async put(endpoint: string, data?: any, options: RequestInit = {}): Promise<Response> {
    const requestOptions: RequestInit = {
      ...options,
      method: 'PUT'
    }

    if (data) {
      requestOptions.body = JSON.stringify(data)
    }

    return this.request(endpoint, requestOptions)
  }

  /**
   * DELETE请求
   * @param endpoint API端点
   * @param options 请求选项
   * @returns Promise<Response>
   */
  async delete(endpoint: string, options: RequestInit = {}): Promise<Response> {
    return this.request(endpoint, { ...options, method: 'DELETE' })
  }

  /**
   * 检查接口是否需要认证
   * @param endpoint API端点
   * @returns boolean
   */
  needsAuth(endpoint: string): boolean {
    return needsAuth(endpoint)
  }

  /**
   * 获取当前认证token
   * @returns string | null
   */
  getAuthToken(): string | null {
    return localStorage.getItem('auth_token')
  }

  /**
   * 设置认证token
   * @param token 认证token
   */
  setAuthToken(token: string): void {
    localStorage.setItem('auth_token', token)
  }

  /**
   * 清除认证token
   */
  clearAuthToken(): void {
    localStorage.removeItem('auth_token')
  }
}

// 创建全局API客户端实例
export const apiClient = new ApiClient()

// 导出常用的API端点
export const API_ENDPOINTS = {
  // 用户认证相关
  SEND_VERIFICATION_CODE: '/api/v1/user/send-verification-code',
  USER_REGISTER: '/api/v1/user/register',
  USER_LOGIN: '/api/v1/user/login',
  USER_LOGOUT: '/api/v1/user/logout',
  RESET_PASSWORD: '/api/v1/user/reset-password',
  
  // 用户信息相关
  USER_PROFILE: '/api/v1/user/profile',
  UPDATE_PROFILE: '/api/v1/user/profile',
  
  // 笔记相关（预留）
  NOTES: '/api/v1/notes',
  NOTE_THEMES: '/api/v1/note-themes',
  NOTE_TAGS: '/api/v1/note-tags',
} as const

// 类型定义
export type ApiEndpoint = typeof API_ENDPOINTS[keyof typeof API_ENDPOINTS]
