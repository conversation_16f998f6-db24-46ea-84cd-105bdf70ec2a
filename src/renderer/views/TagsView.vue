<template>
  <div class="tags-view">
    <div class="view-header">
      <h1>标签管理</h1>
      <p>管理和组织您的笔记标签，在富文本编辑器中输入 # 可以快速提及标签</p>
    </div>

    <div class="view-content">
      <div class="tags-section">
        <h2>现有标签</h2>
        <div class="tags-list">
          <NTag
            v-for="tag in tagsStore.tagsSortedByUsage"
            :key="tag.id"
            :color="{ color: tag.color, textColor: '#fff' }"
            size="large"
            class="tag-item"
          >
            #{{ tag.name }}
            <template #avatar>
              <NBadge :value="tag.usageCount" />
            </template>
          </NTag>
        </div>
      </div>

      <div class="test-section">
        <h2>标签使用说明</h2>
        <p>标签功能已简化，您可以在笔记中直接使用 #标签名 的格式来引用标签。</p>
      </div>

      <div class="create-section">
        <h2>创建新标签</h2>
        <div class="create-form">
          <NInput
            v-model:value="newTagName"
            placeholder="输入标签名称"
            @keyup.enter="createNewTag"
          />
          <NColorPicker
            v-model:value="newTagColor"
            :modes="['hex']"
          />
          <NButton
            type="primary"
            @click="createNewTag"
            :disabled="!newTagName.trim()"
          >
            创建标签
          </NButton>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { NTag, NBadge, NInput, NColorPicker, NButton, useMessage } from 'naive-ui'
import { useTagsStore } from '@/stores/tags'

const message = useMessage()
const tagsStore = useTagsStore()

// 新标签创建
const newTagName = ref('')
const newTagColor = ref('#18a058')

const createNewTag = () => {
  if (!newTagName.value.trim()) return

  tagsStore.createTag(newTagName.value.trim(), newTagColor.value)
  message.success(`标签 "#${newTagName.value}" 创建成功`)

  newTagName.value = ''
  newTagColor.value = '#18a058'
}
</script>

<style scoped>
.tags-view {
  padding: 24px;
  width: 100%;
  height: 100vh;
  overflow-y: auto;
  box-sizing: border-box;
}

.view-header {
  margin-bottom: 24px;
}

.view-header h1 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: var(--n-text-color-1);
}

.view-header p {
  margin: 0;
  color: var(--n-text-color-2);
}

.view-content {
  display: flex;
  flex-direction: column;
  gap: 32px;
}

.tags-section h2,
.test-section h2,
.create-section h2 {
  margin: 0 0 16px 0;
  font-size: 18px;
  font-weight: 600;
  color: var(--n-text-color-1);
}

.tags-list {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
}

.tag-item {
  margin: 0;
}

.test-section p {
  margin: 0 0 16px 0;
  color: var(--n-text-color-2);
}

.create-form {
  display: flex;
  gap: 12px;
  align-items: center;
  flex-wrap: wrap;
}

.create-form .n-input {
  flex: 1;
  min-width: 200px;
}
</style>
