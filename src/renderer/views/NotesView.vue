<template>
  <div class="notes-view">
    <NSplit direction="horizontal" :default-size="splitSize" :min="0.2" :max="0.3" :resize-trigger-size="2"
      @update:size="handleSizeChange">
      <template #1>
        <!-- 左侧主题列表 -->
        <div class="themes-panel">
          <ThemesList />
        </div>
      </template>
      <template #2>
        <!-- 右侧聊天界面 -->
        <div class="chat-panel">
          <ChatInterface />
        </div>
      </template>
    </NSplit>
  </div>
</template>

<script setup lang="ts">
import ChatInterface from '@/components/ChatInterface.vue'
import ThemesList from '@/components/ThemesList.vue'
import { NSplit } from 'naive-ui'
import { onMounted, ref } from 'vue'

// Split 组件的大小状态（比例值，0-1之间）
const splitSize = ref(0.2) // 默认32%给主题列表

// 处理 Split 大小变化
const handleSizeChange = (size: number) => {
  splitSize.value = size
  // 保存到本地存储
  localStorage.setItem('themesPanelSplitSize', size.toString())
}

// 组件挂载时恢复保存的大小
onMounted(() => {
  const savedSize = localStorage.getItem('themesPanelSplitSize')
  if (savedSize) {
    const size = parseFloat(savedSize)
    if (size >= 0.2 && size <= 0.6) {
      splitSize.value = size
    }
  }
})
</script>

<style scoped>
.notes-view {
  width: 100%;
  height: 100vh;
  overflow: hidden;
}

.themes-panel {
  background-color: var(--n-color);
  height: 100vh;
  overflow: hidden;
}

.chat-panel {
  background-color: var(--n-color);
  height: 100vh;
  overflow: hidden;
}

/* 自定义 Split 组件样式 */
:deep(.n-split) {
  height: 100vh;
}

:deep(.n-split-pane) {
  height: 100vh;
}

</style>
