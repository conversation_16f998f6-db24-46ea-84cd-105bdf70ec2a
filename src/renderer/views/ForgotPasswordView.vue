<template>
  <div class="forgot-password-container">
    <div class="forgot-password-card">
      <div class="forgot-password-header">
        <h1>找回密码</h1>
        <p>重置您的账户密码</p>
      </div>

      <n-form
        ref="formRef"
        :model="{ ...forgotPasswordForm, confirmPassword }"
        :rules="rules"
        size="large"
        @submit.prevent="handleResetPassword"
      >
        <n-form-item path="email" label="邮箱">
          <n-input
            v-model:value="forgotPasswordForm.email"
            placeholder="请输入邮箱地址"
            type="email"
            :disabled="authStore.isLoading"
          />
        </n-form-item>

        <n-form-item path="verificationCode" label="验证码">
          <n-input-group>
            <n-input
              v-model:value="forgotPasswordForm.verificationCode"
              placeholder="请输入验证码"
              :disabled="authStore.isLoading"
              style="flex: 1"
            />
            <n-button
              type="primary"
              :disabled="!forgotPasswordForm.email || authStore.isLoading || countdown > 0"
              :loading="sendingCode"
              @click="handleSendCode"
            >
              {{ countdown > 0 ? `${countdown}s` : '发送验证码' }}
            </n-button>
          </n-input-group>
        </n-form-item>

        <n-form-item path="newPassword" label="新密码">
          <n-input
            v-model:value="forgotPasswordForm.newPassword"
            placeholder="请输入新密码（至少6位）"
            type="password"
            show-password-on="click"
            :disabled="authStore.isLoading"
          />
        </n-form-item>

        <n-form-item path="confirmPassword" label="确认新密码">
          <n-input
            v-model:value="confirmPassword"
            placeholder="请再次输入新密码"
            type="password"
            show-password-on="click"
            :disabled="authStore.isLoading"
            @keyup.enter="handleResetPassword"
          />
        </n-form-item>

        <n-form-item>
          <n-button
            type="primary"
            size="large"
            block
            :loading="authStore.isLoading"
            @click="handleResetPassword"
          >
            重置密码
          </n-button>
        </n-form-item>
      </n-form>

      <div class="forgot-password-footer">
        <div class="forgot-password-links">
          <n-button text @click="$router.push('/login')">
            返回登录
          </n-button>
          <span class="divider">|</span>
          <n-button text @click="$router.push('/register')">
            注册新账户
          </n-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import {
  NForm,
  NFormItem,
  NInput,
  NInputGroup,
  NButton,
  useMessage,
  type FormInst,
  type FormRules
} from 'naive-ui'
import { useAuthStore, type ForgotPasswordForm } from '@/stores/auth'

const router = useRouter()
const authStore = useAuthStore()
const message = useMessage()
const formRef = ref<FormInst | null>(null)

// 表单数据
const forgotPasswordForm = reactive<ForgotPasswordForm>({
  email: '',
  verificationCode: '',
  newPassword: ''
})

const confirmPassword = ref('')
const sendingCode = ref(false)
const countdown = ref(0)

// 表单验证规则
const rules: FormRules = {
  email: [
    {
      required: true,
      message: '请输入邮箱地址',
      trigger: ['input', 'blur']
    },
    {
      type: 'email',
      message: '请输入有效的邮箱地址',
      trigger: ['input', 'blur']
    }
  ],
  verificationCode: [
    {
      required: true,
      message: '请输入验证码',
      trigger: ['input', 'blur']
    },
    {
      len: 6,
      message: '验证码为6位数字',
      trigger: ['input', 'blur']
    }
  ],
  newPassword: [
    {
      required: true,
      message: '请输入新密码',
      trigger: ['input', 'blur']
    },
    {
      min: 6,
      message: '密码长度至少为6位',
      trigger: ['input', 'blur']
    }
  ],
  confirmPassword: [
    {
      required: true,
      message: '请确认新密码',
      trigger: ['input', 'blur']
    },
    {
      validator: (rule, value) => {
        return value === forgotPasswordForm.newPassword
      },
      message: '两次输入的密码不一致',
      trigger: ['input', 'blur']
    }
  ]
}

// 发送验证码
const handleSendCode = async () => {
  if (!forgotPasswordForm.email) return

  sendingCode.value = true
  const result = await authStore.sendVerificationCode(forgotPasswordForm.email, "reset_password")
  sendingCode.value = false

  if (result.success) {
    message.success(result.message)
    // 开始倒计时
    countdown.value = 60
    const timer = setInterval(() => {
      countdown.value--
      if (countdown.value <= 0) {
        clearInterval(timer)
      }
    }, 1000)
  } else {
    message.error(result.message)
  }
}

// 处理重置密码
const handleResetPassword = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    const result = await authStore.resetPassword(forgotPasswordForm)

    if (result.success) {
      message.success(result.message)
      // 重置成功，跳转到登录页面
      router.push('/login')
    } else {
      message.error(result.message)
    }
  } catch (error) {
    // 表单验证失败
    console.error('表单验证失败:', error)
  }
}
</script>

<style scoped>
.forgot-password-container {
  min-height: calc(100vh - 32px); /* 减去标题栏高度 */
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
}

.forgot-password-card {
  width: 100%;
  max-width: 400px;
  background: white;
  border-radius: 12px;
  padding: 40px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.forgot-password-header {
  text-align: center;
  margin-bottom: 32px;
}

.forgot-password-header h1 {
  margin: 0 0 8px 0;
  font-size: 28px;
  font-weight: 600;
  color: #333;
}

.forgot-password-header p {
  margin: 0;
  color: #666;
  font-size: 14px;
}

.forgot-password-footer {
  margin-top: 24px;
}

.forgot-password-links {
  text-align: center;
  color: #666;
}

.divider {
  margin: 0 12px;
  color: #ccc;
}
</style>
