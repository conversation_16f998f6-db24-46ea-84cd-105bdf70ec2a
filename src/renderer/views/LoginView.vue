<template>
  <div class="login-container">
    <div class="login-card">
      <div class="login-header">
        <h1>登录</h1>
        <p>欢迎回到聊天式笔记</p>
      </div>

      <n-form
        ref="formRef"
        :model="loginForm"
        :rules="rules"
        size="large"
        @submit.prevent="handleLogin"
      >
        <n-form-item path="email" label="邮箱">
          <n-input
            v-model:value="loginForm.email"
            placeholder="请输入邮箱地址"
            type="text"
            :disabled="authStore.isLoading"
          />
        </n-form-item>

        <n-form-item path="password" label="密码">
          <n-input
            v-model:value="loginForm.password"
            placeholder="请输入密码"
            type="password"
            show-password-on="click"
            :disabled="authStore.isLoading"
            @keyup.enter="handleLogin"
          />
        </n-form-item>

        <n-form-item>
          <n-button
            type="primary"
            size="large"
            block
            :loading="authStore.isLoading"
            @click="handleLogin"
          >
            登录
          </n-button>
        </n-form-item>
      </n-form>

      <div class="login-footer">
        <div class="login-links">
          <n-button text @click="$router.push('/forgot-password')">
            忘记密码？
          </n-button>
        </div>
        
        <n-divider>或</n-divider>
        
        <n-button
          size="large"
          block
          @click="$router.push('/register')"
        >
          注册新账户
        </n-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import {
    NButton,
    NDivider,
    NForm,
    NFormItem,
    NInput,
    useMessage,
    type FormInst,
    type FormRules
} from 'naive-ui'
import { reactive, ref } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore, type LoginForm } from '../stores/auth'

const router = useRouter()
const authStore = useAuthStore()
const message = useMessage()
const formRef = ref<FormInst | null>(null)

// 表单数据
const loginForm = reactive<LoginForm>({
  email: '<EMAIL>',
  password: '123456'
})

// 表单验证规则
const rules: FormRules = {
  email: [
    {
      required: true,
      message: '请输入邮箱地址',
      trigger: ['input', 'blur']
    },
    {
      type: 'email',
      message: '请输入有效的邮箱地址',
      trigger: ['input', 'blur']
    }
  ],
  password: [
    {
      required: true,
      message: '请输入密码',
      trigger: ['input', 'blur']
    }
  ]
}

// 处理登录
const handleLogin = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    const result = await authStore.login(loginForm)

    if (result.success) {
      message.success('登录成功')
      // 登录成功，跳转到主页
      router.push('/')
    } else {
      message.error(result.message || '登录失败，请检查邮箱和密码')
    }
  } catch (error) {
    // 表单验证失败
    console.error('表单验证失败:', error)
  }
}
</script>

<style scoped>
.login-container {
  min-height: calc(100vh - 32px); /* 减去标题栏高度 */
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
}

.login-card {
  width: 100%;
  max-width: 400px;
  background: white;
  border-radius: 12px;
  padding: 40px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.login-header {
  text-align: center;
  margin-bottom: 32px;
}

.login-header h1 {
  margin: 0 0 8px 0;
  font-size: 28px;
  font-weight: 600;
  color: #333;
}

.login-header p {
  margin: 0;
  color: #666;
  font-size: 14px;
}

.login-footer {
  margin-top: 24px;
}

.login-links {
  text-align: center;
  margin-bottom: 16px;
}

.n-divider {
  margin: 16px 0;
}
</style>
