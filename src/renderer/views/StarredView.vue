<template>
  <div class="starred-view">
    <div class="view-header">
      <h1>收藏的主题</h1>
      <p>查看您收藏的所有笔记主题</p>
    </div>

    <div class="view-content">
      <div v-if="starredThemes.length === 0" class="empty-state">
        <NEmpty description="暂无收藏的主题">
          <template #extra>
            <NButton @click="goToNotes">去添加收藏</NButton>
          </template>
        </NEmpty>
      </div>

      <div v-else class="starred-themes">
        <div
          v-for="theme in starredThemes"
          :key="theme.id"
          class="theme-card"
          @click="openTheme(theme.id)"
        >
          <div class="theme-header">
            <h3>{{ theme.title }}</h3>
            <NIcon class="star-icon" color="#f0a020">
              <StarOutline />
            </NIcon>
          </div>
          <p v-if="theme.description" class="theme-description">
            {{ theme.description }}
          </p>
          <div class="theme-meta">
            <span class="message-count">{{ theme.messageCount }} 条消息</span>
            <span class="last-time">
              {{ formatTime(theme.lastMessageTime || theme.updatedAt) }}
            </span>
          </div>
          <div v-if="theme.tags.length > 0" class="theme-tags">
            <NTag
              v-for="tag in theme.tags"
              :key="tag"
              size="small"
              type="info"
            >
              {{ tag }}
            </NTag>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useRouter } from 'vue-router'
import { NEmpty, NButton, NIcon, NTag } from 'naive-ui'
import { StarOutline } from '@vicons/ionicons5'
import { useNoteThemesStore } from '@/stores/noteThemes'

const router = useRouter()
const themesStore = useNoteThemesStore()

const starredThemes = computed(() => themesStore.starredThemes)

const goToNotes = () => {
  router.push('/')
}

const openTheme = (themeId: string) => {
  themesStore.setCurrentTheme(themeId)
  router.push('/')
}

const formatTime = (date: Date) => {
  const now = new Date()
  const diff = now.getTime() - date.getTime()
  const days = Math.floor(diff / (1000 * 60 * 60 * 24))

  if (days === 0) {
    return '今天'
  } else if (days === 1) {
    return '昨天'
  } else if (days < 7) {
    return `${days}天前`
  } else {
    return date.toLocaleDateString('zh-CN')
  }
}
</script>

<style scoped>
.starred-view {
  padding: 24px;
  width: 100%;
  height: 100vh;
  overflow-y: auto;
  box-sizing: border-box;
}

.view-header {
  margin-bottom: 24px;
}

.view-header h1 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: var(--n-text-color-1);
}

.view-header p {
  margin: 0;
  color: var(--n-text-color-2);
}

.empty-state {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 400px;
}

.starred-themes {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 16px;
}

.theme-card {
  padding: 20px;
  border: 1px solid var(--n-divider-color, var(--n-border-color, #e0e0e6));
  border-radius: 8px;
  background-color: var(--n-color-embedded);
  cursor: pointer;
  transition: all 0.2s ease;
}

.theme-card:hover {
  border-color: var(--n-color-primary);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.theme-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8px;
}

.theme-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--n-text-color-1);
}

.star-icon {
  font-size: 16px;
  flex-shrink: 0;
}

.theme-description {
  margin: 0 0 12px 0;
  color: var(--n-text-color-2);
  font-size: 14px;
  line-height: 1.4;
}

.theme-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  font-size: 12px;
  color: var(--n-text-color-3);
}

.theme-tags {
  display: flex;
  gap: 6px;
  flex-wrap: wrap;
}
</style>
