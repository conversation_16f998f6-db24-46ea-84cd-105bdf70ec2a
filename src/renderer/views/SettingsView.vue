<template>
  <div class="settings-view">
    <div class="view-header">
      <h1>设置</h1>
      <p>个性化您的笔记应用体验</p>
    </div>

    <div class="view-content">
      <NEmpty description="设置功能开发中" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { NEmpty } from 'naive-ui';
</script>

<style scoped>
.settings-view {
  padding: 24px;
  width: 100%;
  height: 100vh;
  overflow-y: auto;
  box-sizing: border-box;
}

.view-header {
  margin-bottom: 24px;
}

.view-header h1 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: var(--n-text-color-1);
}

.view-header p {
  margin: 0;
  color: var(--n-text-color-2);
}

.view-content {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 400px;
}
</style>
