<template>
  <div class="register-container">
    <div class="register-card">
      <div class="register-header">
        <h1>注册</h1>
        <p>创建您的聊天式笔记账户</p>
      </div>

      <n-form
        ref="formRef"
        :model="{ ...registerForm, confirmPassword }"
        :rules="rules"
        size="large"
        @submit.prevent="handleRegister"
      >
        <n-form-item path="email" label="邮箱">
          <n-input
            v-model:value="registerForm.email"
            placeholder="请输入邮箱地址"
            type="email"
            :disabled="authStore.isLoading"
          />
        </n-form-item>

        <n-form-item path="verificationCode" label="验证码">
          <n-input-group>
            <n-input
              v-model:value="registerForm.verificationCode"
              placeholder="请输入验证码"
              :disabled="authStore.isLoading"
              style="flex: 1"
            />
            <n-button
              type="primary"
              :disabled="!registerForm.email || authStore.isLoading || countdown > 0"
              :loading="sendingCode"
              @click="handleSendCode"
            >
              {{ countdown > 0 ? `${countdown}s` : '发送验证码' }}
            </n-button>
          </n-input-group>
        </n-form-item>

        <n-form-item path="nickName" label="昵称">
          <n-input
            v-model:value="registerForm.nickName"
            placeholder="请输入昵称"
            :disabled="authStore.isLoading"
          />
        </n-form-item>

        <n-form-item path="password" label="密码">
          <n-input
            v-model:value="registerForm.password"
            placeholder="请输入密码（至少6位）"
            type="password"
            show-password-on="click"
            :disabled="authStore.isLoading"
          />
        </n-form-item>

        <n-form-item path="confirmPassword" label="确认密码">
          <n-input
            v-model:value="confirmPassword"
            placeholder="请再次输入密码"
            type="password"
            show-password-on="click"
            :disabled="authStore.isLoading"
            @keyup.enter="handleRegister"
          />
        </n-form-item>

        <n-form-item>
          <n-button
            type="primary"
            size="large"
            block
            :loading="authStore.isLoading"
            @click="handleRegister"
          >
            注册
          </n-button>
        </n-form-item>
      </n-form>

      <div class="register-footer">
        <div class="register-links">
          <span>已有账户？</span>
          <n-button text @click="$router.push('/login')">
            立即登录
          </n-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import {
    NButton,
    NForm,
    NFormItem,
    NInput,
    NInputGroup,
    useMessage,
    type FormInst,
    type FormRules
} from 'naive-ui'
import { reactive, ref } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore, type RegisterForm } from '../stores/auth'

const router = useRouter()
const authStore = useAuthStore()
const message = useMessage()
const formRef = ref<FormInst | null>(null)

// 表单数据
const registerForm = reactive<RegisterForm>({
  email: '',
  verificationCode: '',
  password: '',
  nickName: '',
  avatar: 'https://resource.spotmaxtech.com/mrmax/icon/default.png' // 默认头像
})

const confirmPassword = ref('')
const sendingCode = ref(false)
const countdown = ref(0)

// 表单验证规则
const rules: FormRules = {
  email: [
    {
      required: true,
      message: '请输入邮箱地址',
      trigger: ['input', 'blur']
    },
    {
      type: 'email',
      message: '请输入有效的邮箱地址',
      trigger: ['input', 'blur']
    }
  ],
  verificationCode: [
    {
      required: true,
      message: '请输入验证码',
      trigger: ['input', 'blur']
    },
    {
      len: 6,
      message: '验证码为6位数字',
      trigger: ['input', 'blur']
    }
  ],
  nickName: [
    {
      required: true,
      message: '请输入昵称',
      trigger: ['input', 'blur']
    }
  ],
  password: [
    {
      required: true,
      message: '请输入密码',
      trigger: ['input', 'blur']
    },
    {
      min: 6,
      message: '密码长度至少为6位',
      trigger: ['input', 'blur']
    }
  ],
  confirmPassword: [
    {
      required: true,
      message: '请确认密码',
      trigger: ['input', 'blur']
    },
    {
      validator: (rule, value) => {
        return value === registerForm.password
      },
      message: '两次输入的密码不一致',
      trigger: ['input', 'blur']
    }
  ]
}

// 发送验证码
const handleSendCode = async () => {
  if (!registerForm.email) return

  sendingCode.value = true
  const result = await authStore.sendVerificationCode(registerForm.email, "register")
  sendingCode.value = false

  if (result.success) {
    message.success(result.message)
    // 开始倒计时
    countdown.value = 60
    const timer = setInterval(() => {
      countdown.value--
      if (countdown.value <= 0) {
        clearInterval(timer)
      }
    }, 1000)
  } else {
    message.error(result.message)
  }
}

// 处理注册
const handleRegister = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    const result = await authStore.register(registerForm)

    if (result.success) {
      message.success(result.message)
      // 注册成功，跳转到主页
      router.push('/')
    } else {
      message.error(result.message)
    }
  } catch (error) {
    // 表单验证失败
    console.error('表单验证失败:', error)
  }
}
</script>

<style scoped>
.register-container {
  min-height: calc(100vh - 32px); /* 减去标题栏高度 */
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
}

.register-card {
  width: 100%;
  max-width: 400px;
  background: white;
  border-radius: 12px;
  padding: 40px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.register-header {
  text-align: center;
  margin-bottom: 32px;
}

.register-header h1 {
  margin: 0 0 8px 0;
  font-size: 28px;
  font-weight: 600;
  color: #333;
}

.register-header p {
  margin: 0;
  color: #666;
  font-size: 14px;
}

.register-footer {
  margin-top: 24px;
}

.register-links {
  text-align: center;
  color: #666;
}

.register-links span {
  margin-right: 8px;
}
</style>
