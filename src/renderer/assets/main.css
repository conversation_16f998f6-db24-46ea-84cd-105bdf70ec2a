@import './base.css';

/* 移除限制页面宽度的样式，让页面占据全屏 */
#app {
  width: 100%;
  height: 100vh;
  margin: 0;
  padding: 0;
  font-weight: normal;
  overflow: hidden;
}

a,
.green {
  text-decoration: none;
  color: hsla(160, 100%, 37%, 1);
  transition: 0.4s;
  padding: 3px;
}

@media (hover: hover) {
  a:hover {
    background-color: hsla(160, 100%, 37%, 0.2);
  }
}

/* 全局 Mention 样式 - 使用正确的类名 */
.mention {
  background-color: #e3f2fd !important;
  color: #1976d2 !important;
  padding: 2px 6px !important;
  border-radius: 4px !important;
  font-weight: 500 !important;
  text-decoration: none !important;
  margin: 0 1px !important;
  border: 1px solid #bbdefb !important;
  cursor: pointer !important;
  display: inline-block !important;
}

.mention:hover {
  background-color: #bbdefb !important;
}

/* 兼容性：也支持ql-mention类名 */
.ql-mention {
  background-color: #e3f2fd !important;
  color: #1976d2 !important;
  padding: 2px 6px !important;
  border-radius: 4px !important;
  font-weight: 500 !important;
  text-decoration: none !important;
  margin: 0 1px !important;
  border: 1px solid #bbdefb !important;
  cursor: pointer !important;
  display: inline-block !important;
}

.ql-mention:hover {
  background-color: #bbdefb !important;
}

/* Mention 下拉列表样式 - 基于官方样式但增强 */
.ql-mention-list-container {
  z-index: 99999 !important;
  background-color: #fff !important;
  border: 1px solid #f0f0f0 !important;
  border-radius: 6px !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
  max-height: 200px !important;
  overflow: auto !important;
  width: 270px !important;
}

.ql-mention-list {
  list-style: none !important;
  margin: 0 !important;
  overflow: hidden !important;
  padding: 0 !important;
}

.ql-mention-list-item {
  cursor: pointer !important;
  font-size: 14px !important;
  line-height: 36px !important;
  padding: 0 16px !important;
  vertical-align: middle !important;
  border-bottom: 1px solid #f8f9fa !important;
}

.ql-mention-list-item:last-child {
  border-bottom: none !important;
}

.ql-mention-list-item.disabled {
  cursor: auto !important;
  opacity: 0.6 !important;
}

.ql-mention-list-item:hover,
.ql-mention-list-item.selected {
  background-color: #e3f2fd !important;
  text-decoration: none !important;
}
