import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { apiClient, API_ENDPOINTS } from '@/utils/api'

export interface User {
  id: string
  email: string
  nickName: string
  avatar: string
  createdAt: string
}

export interface LoginForm {
  email: string
  password: string
}

export interface RegisterForm {
  email: string
  verificationCode: string
  password: string
  nickName: string
  avatar: string
}

export interface ForgotPasswordForm {
  email: string
  verificationCode: string
  newPassword: string
}

export const useAuthStore = defineStore('auth', () => {
  
  // 状态
  const user = ref<User | null>(null)
  const isLoading = ref(false)
  const token = ref<string | null>(localStorage.getItem('auth_token'))

  // 计算属性
  const isAuthenticated = computed(() => !!token.value && !!user.value)

  // 模拟 API 调用的延迟
  const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms))

  // 发送验证码
  const sendVerificationCode = async (email: string, vc_type: string): Promise<{ success: boolean; message: string }> => {
    try {
      isLoading.value = true

      // 验证邮箱格式
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
      if (!emailRegex.test(email)) {
        return { success: false, message: '请输入有效的邮箱地址' }
      }

      // 调用发送验证码API
      const response = await apiClient.post(API_ENDPOINTS.SEND_VERIFICATION_CODE, { email, vc_type })

      // 根据状态码判断是否成功，200状态码表示发送成功
      if (response.status === 200) {
        return { success: true, message: '验证码已发送到您的邮箱' }
      } else {
        // 尝试解析错误信息
        let errorMessage = '发送验证码失败，请稍后重试'
        try {
          const data = await response.json()
          if (data.message) {
            errorMessage = data.message
          }
        } catch (parseError) {
          console.warn('无法解析错误响应:', parseError)
        }
        return { success: false, message: errorMessage }
      }
    } catch (error) {
      console.error('发送验证码请求失败:', error)
      return { success: false, message: '发送验证码失败，请检查网络连接' }
    } finally {
      isLoading.value = false
    }
  }

  // 注册
  const register = async (form: RegisterForm): Promise<{ success: boolean; message: string }> => {
    try {
      isLoading.value = true

      // 基本验证
      if (!form.email || !form.verificationCode || !form.password || !form.nickName) {
        return { success: false, message: '请填写所有必填字段' }
      }

      if (form.password.length < 6) {
        return { success: false, message: '密码长度至少为6位' }
      }

      // 调用真实API
      const response = await apiClient.post(API_ENDPOINTS.USER_REGISTER, {
        email: form.email,
        password: form.password,
        verification_code: form.verificationCode,
        nick_name: form.nickName,
        avatar: form.avatar
      })

      const data = await response.json()

      if (!response.ok) {
        return { success: false, message: data.message || '注册失败，请稍后重试' }
      }

      // 注册成功，保存token和用户信息
      const authToken = data.data.token
      const newUser: User = {
        id: data.data.user.id.toString(),
        email: data.data.user.email,
        nickName: data.data.user.nick_name,
        avatar: data.data.user.avatar,
        createdAt: new Date().toISOString()
      }

      user.value = newUser
      token.value = authToken
      // 使用API客户端的方法设置token
      apiClient.setAuthToken(authToken)
      localStorage.setItem('user', JSON.stringify(newUser))

      return { success: true, message: '注册成功！' }
    } catch (error) {
      console.error('注册请求失败:', error)
      return { success: false, message: '注册失败，请检查网络连接' }
    } finally {
      isLoading.value = false
    }
  }

  // 登录
  const login = async (form: LoginForm): Promise<{ success: boolean; message: string }> => {
    try {
      isLoading.value = true

      if (!form.email || !form.password) {
        return { success: false, message: '请输入邮箱和密码' }
      }

      // 调用真实API
      const response = await apiClient.post(API_ENDPOINTS.USER_LOGIN, {
        email: form.email,
        password: form.password
      })

      const data = await response.json()

      if (!response.ok) {
        // 处理错误情况
        if (response.status === 401) {
          return { success: false, message: '邮箱或密码错误' }
        }
        return { success: false, message: data.message || '登录失败，请稍后重试' }
      }

      // 登录成功，保存token和用户信息
      const authToken = data.data.token
      const loginUser: User = {
        id: data.data.user.id.toString(),
        email: data.data.user.email,
        nickName: data.data.user.nick_name,
        avatar: data.data.user.avatar,
        createdAt: new Date().toISOString()
      }

      user.value = loginUser
      token.value = authToken
      // 使用API客户端的方法设置token
      apiClient.setAuthToken(authToken)
      localStorage.setItem('user', JSON.stringify(loginUser))

      return { success: true, message: '登录成功！' }
    } catch (error) {
      console.error('登录请求失败:', error)
      return { success: false, message: '登录失败，请检查网络连接' }
    } finally {
      isLoading.value = false
    }
  }

  // 找回密码
  const resetPassword = async (form: ForgotPasswordForm): Promise<{ success: boolean; message: string }> => {
    try {
      isLoading.value = true

      if (!form.email || !form.verificationCode || !form.newPassword) {
        return { success: false, message: '请填写所有必填字段' }
      }

      if (form.newPassword.length < 6) {
        return { success: false, message: '新密码长度至少为6位' }
      }

      // 调用真实API
      const response = await apiClient.post(API_ENDPOINTS.RESET_PASSWORD, {
        email: form.email,
        verification_code: form.verificationCode,
        new_password: form.newPassword
      })

      const data = await response.json()

      if (!response.ok) {
        return { success: false, message: data.message || '密码重置失败，请稍后重试' }
      }

      return { success: true, message: '密码重置成功，请使用新密码登录' }
    } catch (error) {
      return { success: false, message: '密码重置失败，请稍后重试' }
    } finally {
      isLoading.value = false
    }
  }

  // 登出
  const logout = () => {
    user.value = null
    token.value = null
    // 使用API客户端的方法清除token
    apiClient.clearAuthToken()
    localStorage.removeItem('user')
  }

  // 初始化用户信息（从本地存储恢复）
  const initializeAuth = () => {
    const savedToken = apiClient.getAuthToken()
    const savedUser = localStorage.getItem('user')

    if (savedToken && savedUser) {
      try {
        token.value = savedToken
        user.value = JSON.parse(savedUser)
        // 确保API客户端也有token
        apiClient.setAuthToken(savedToken)
      } catch (error) {
        // 如果解析失败，清除无效数据
        apiClient.clearAuthToken()
        localStorage.removeItem('user')
      }
    }
  }

  return {
    // 状态
    user,
    isLoading,
    token,
    
    // 计算属性
    isAuthenticated,
    
    // 方法
    sendVerificationCode,
    register,
    login,
    resetPassword,
    logout,
    initializeAuth
  }
})
