import { isFeatureEnabled } from '@/config'
import { themeApi } from '@/services/api'
import type { NoteTheme } from '@/types'
import { defineStore } from 'pinia'
import { computed, ref } from 'vue'

export const useNoteThemesStore = defineStore('noteThemes', () => {
  // 状态
  const themes = ref<NoteTheme[]>([])
  const currentThemeId = ref<string | null>(null)
  const isLoading = ref(false)

  // 计算属性
  const currentTheme = computed(() => {
    if (!currentThemeId.value) return null
    return themes.value.find(theme => theme.id === currentThemeId.value) || null
  })

  const sortedThemes = computed(() => {
    return [...themes.value].sort((a, b) => {
      // 先按是否收藏排序，再按最后消息时间排序
      if (a.isStarred && !b.isStarred) return -1
      if (!a.isStarred && b.isStarred) return 1

      const aTime = a.lastMessageTime || a.updatedAt
      const bTime = b.lastMessageTime || b.updatedAt
      return bTime.getTime() - aTime.getTime()
    })
  })

  const starredThemes = computed(() => {
    return themes.value.filter(theme => theme.isStarred)
  })

  // 动作
  const createTheme = async (title: string, description?: string, avatar?: string): Promise<NoteTheme> => {
    isLoading.value = true

    try {
      // 如果启用了API同步，调用服务端接口
      if (isFeatureEnabled('apiSync')) {
        const newTheme = await themeApi.create({
          title,
          description,
          avatar
        })

        // 将服务端返回的主题添加到本地
        themes.value.unshift(newTheme)
        saveToStorage()
        return newTheme
      } else {
        // 离线模式，使用本地生成
        const newTheme: NoteTheme = {
          id: generateId(),
          title,
          description,
          avatar,
          createdAt: new Date(),
          updatedAt: new Date(),
          tags: [],
          isStarred: false,
          messageCount: 0
        }

        themes.value.unshift(newTheme)
        saveToStorage()
        return newTheme
      }
    } catch (error) {
      console.error('创建主题失败:', error)

      // 如果服务端调用失败，回退到本地创建
      const fallbackTheme: NoteTheme = {
        id: generateId(),
        title,
        description,
        avatar,
        createdAt: new Date(),
        updatedAt: new Date(),
        tags: [],
        isStarred: false,
        messageCount: 0
      }

      themes.value.unshift(fallbackTheme)
      saveToStorage()

      // 重新抛出错误，让调用方处理
      throw error
    } finally {
      isLoading.value = false
    }
  }

  const updateTheme = (id: string, updates: Partial<NoteTheme>) => {
    const index = themes.value.findIndex(theme => theme.id === id)
    if (index !== -1) {
      themes.value[index] = {
        ...themes.value[index],
        ...updates,
        updatedAt: new Date()
      }
      saveToStorage()
    }
  }

  const deleteTheme = (id: string) => {
    const index = themes.value.findIndex(theme => theme.id === id)
    if (index !== -1) {
      themes.value.splice(index, 1)
      if (currentThemeId.value === id) {
        currentThemeId.value = null
      }
      saveToStorage()
    }
  }

  const toggleStar = (id: string) => {
    const theme = themes.value.find(theme => theme.id === id)
    if (theme) {
      theme.isStarred = !theme.isStarred
      theme.updatedAt = new Date()
      saveToStorage()
    }
  }

  const setCurrentTheme = (id: string | null) => {
    currentThemeId.value = id
  }

  const addTag = (themeId: string, tag: string) => {
    const theme = themes.value.find(theme => theme.id === themeId)
    if (theme && !theme.tags.includes(tag)) {
      theme.tags.push(tag)
      theme.updatedAt = new Date()
      saveToStorage()
    }
  }

  const removeTag = (themeId: string, tag: string) => {
    const theme = themes.value.find(theme => theme.id === themeId)
    if (theme) {
      const index = theme.tags.indexOf(tag)
      if (index !== -1) {
        theme.tags.splice(index, 1)
        theme.updatedAt = new Date()
        saveToStorage()
      }
    }
  }

  const updateLastMessage = (themeId: string, message: string, time: Date) => {
    const theme = themes.value.find(theme => theme.id === themeId)
    if (theme) {
      theme.lastMessage = message
      theme.lastMessageTime = time
      theme.messageCount += 1
      theme.updatedAt = time
      saveToStorage()
    }
  }

  const searchThemes = (query: string): NoteTheme[] => {
    if (!query.trim()) return themes.value

    const lowerQuery = query.toLowerCase()
    return themes.value.filter(theme =>
      theme.title.toLowerCase().includes(lowerQuery) ||
      theme.description?.toLowerCase().includes(lowerQuery) ||
      theme.tags.some(tag => tag.toLowerCase().includes(lowerQuery))
    )
  }

  // 从服务端加载主题列表
  const loadThemesFromServer = async (): Promise<void> => {
    if (!isFeatureEnabled('apiSync')) {
      return
    }

    isLoading.value = true
    try {
      const serverThemes = await themeApi.getAll()

      // 将服务端主题与本地主题合并，避免重复
      const existingIds = new Set(themes.value.map(theme => theme.id))
      const newThemes = serverThemes.filter(theme => !existingIds.has(theme.id))

      themes.value = [...themes.value, ...newThemes]
      saveToStorage()
    } catch (error) {
      console.error('从服务端加载主题失败:', error)
      // 加载失败时不抛出错误，继续使用本地数据
    } finally {
      isLoading.value = false
    }
  }

  // 工具函数
  const generateId = (): string => {
    return Date.now().toString(36) + Math.random().toString(36).substring(2)
  }

  const saveToStorage = () => {
    try {
      localStorage.setItem('noteThemes', JSON.stringify(themes.value))
    } catch (error) {
      console.error('Failed to save themes to storage:', error)
    }
  }

  const loadFromStorage = () => {
    try {
      const stored = localStorage.getItem('noteThemes')
      if (stored) {
        const parsed = JSON.parse(stored)
        themes.value = parsed.map((theme: any) => ({
          ...theme,
          createdAt: new Date(theme.createdAt),
          updatedAt: new Date(theme.updatedAt),
          lastMessageTime: theme.lastMessageTime ? new Date(theme.lastMessageTime) : undefined
        }))
      }
    } catch (error) {
      console.error('Failed to load themes from storage:', error)
    }
  }

  const initializeWithSampleData = () => {
    if (themes.value.length === 0) {
      const sampleThemes: NoteTheme[] = []

      themes.value = sampleThemes
      saveToStorage()
    }
  }

  // 初始化
  loadFromStorage()
  initializeWithSampleData()

  // 如果启用了API同步，尝试从服务端加载主题
  if (isFeatureEnabled('apiSync')) {
    loadThemesFromServer()
  }

  return {
    // 状态
    themes,
    currentThemeId,
    isLoading,

    // 计算属性
    currentTheme,
    sortedThemes,
    starredThemes,

    // 动作
    createTheme,
    updateTheme,
    deleteTheme,
    toggleStar,
    setCurrentTheme,
    addTag,
    removeTag,
    updateLastMessage,
    searchThemes,
    loadFromStorage,
    saveToStorage,
    loadThemesFromServer
  }
})
