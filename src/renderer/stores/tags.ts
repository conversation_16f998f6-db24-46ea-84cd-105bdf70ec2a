import { defineStore } from 'pinia'
import { computed, ref } from 'vue'
import { tagApi } from '../services/api'
import type { Tag } from '../types'

export const useTagsStore = defineStore('tags', () => {
  // 状态
  const tags = ref<Tag[]>([])

  // 计算属性 - 按使用次数排序的标签
  const tagsSortedByUsage = computed(() => {
    return [...tags.value].sort((a, b) => b.usageCount - a.usageCount)
  })

  // 获取标签建议（用于mention功能）
  const getTagSuggestions = computed(() => {
    return (query: string) => {
      const lowerQuery = query.toLowerCase()
      return tagsSortedByUsage.value
        .filter(tag => tag.name.toLowerCase().includes(lowerQuery))
        .map(tag => ({
          id: tag.id,
          value: tag.name,
          denotationChar: '#'
        }))
    }
  })

  // 动作
  const createTag = (name: string, color: string = '#18a058'): Tag => {
    // 检查是否已存在同名标签
    const existingTag = tags.value.find(tag => tag.name === name)
    if (existingTag) {
      // 如果已存在，增加使用次数
      existingTag.usageCount++
      saveToStorage()
      return existingTag
    }

    const newTag: Tag = {
      id: generateId(),
      name,
      color,
      createdAt: new Date(),
      usageCount: 1
    }

    tags.value.push(newTag)
    saveToStorage()
    return newTag
  }

  const updateTag = (id: string, updates: Partial<Omit<Tag, 'id' | 'createdAt'>>) => {
    const tag = tags.value.find(t => t.id === id)
    if (tag) {
      Object.assign(tag, updates)
      saveToStorage()
    }
  }

  const deleteTag = (id: string) => {
    const index = tags.value.findIndex(t => t.id === id)
    if (index !== -1) {
      tags.value.splice(index, 1)
      saveToStorage()
    }
  }

  const incrementTagUsage = (tagName: string) => {
    const tag = tags.value.find(t => t.name === tagName)
    if (tag) {
      tag.usageCount++
      saveToStorage()
    } else {
      // 如果标签不存在，创建新标签
      createTag(tagName)
    }
  }

  const getTagByName = (name: string): Tag | undefined => {
    return tags.value.find(tag => tag.name === name)
  }

  const searchTags = (query: string): Tag[] => {
    if (!query.trim()) return tagsSortedByUsage.value

    const lowerQuery = query.toLowerCase()
    return tagsSortedByUsage.value.filter(tag =>
      tag.name.toLowerCase().includes(lowerQuery)
    )
  }

  // 工具函数
  const generateId = (): string => {
    return Date.now().toString(36) + Math.random().toString(36).substring(2)
  }

  const saveToStorage = () => {
    try {
      localStorage.setItem('noteTags', JSON.stringify(tags.value))
    } catch (error) {
      console.error('Failed to save tags to storage:', error)
    }
  }

  const loadFromStorage = () => {
    try {
      const stored = localStorage.getItem('noteTags')
      if (stored) {
        const parsed = JSON.parse(stored)
        tags.value = parsed.map((tag: any) => ({
          ...tag,
          createdAt: new Date(tag.createdAt)
        }))
      }
    } catch (error) {
      console.error('Failed to load tags from storage:', error)
    }
  }

  const initializeWithSampleData = () => {
    // 只有在没有标签时才初始化示例数据
    if (tags.value.length === 0) {
      const sampleTags: Tag[] = [
        {
          id: 't1',
          name: '工作',
          color: '#18a058',
          createdAt: new Date('2024-01-01'),
          usageCount: 15
        },
        {
          id: 't2',
          name: '学习',
          color: '#2080f0',
          createdAt: new Date('2024-01-02'),
          usageCount: 12
        },
        {
          id: 't3',
          name: '生活',
          color: '#f0a020',
          createdAt: new Date('2024-01-03'),
          usageCount: 8
        },
        {
          id: 't4',
          name: '项目',
          color: '#d03050',
          createdAt: new Date('2024-01-04'),
          usageCount: 6
        },
        {
          id: 't5',
          name: '想法',
          color: '#722ed1',
          createdAt: new Date('2024-01-05'),
          usageCount: 4
        }
      ]

      tags.value = sampleTags
      saveToStorage()
    }
  }

  // API 相关方法
  const syncToServer = async (): Promise<boolean> => {
    try {
      // 将本地标签同步到服务器
      const localTags = tags.value
      for (const tag of localTags) {
        await tagApi.create({
          name: tag.name,
          color: tag.color
        })
      }
      return true
    } catch (error) {
      console.error('Failed to sync tags to server:', error)
      return false
    }
  }

  const loadFromServer = async (): Promise<boolean> => {
    try {
      const serverTags = await tagApi.getAll()
      tags.value = serverTags
      saveToStorage()
      return true
    } catch (error) {
      console.error('Failed to load tags from server:', error)
      return false
    }
  }

  const createTagOnServer = async (name: string, color: string = '#18a058'): Promise<Tag | null> => {
    try {
      const newTag = await tagApi.create({ name, color })
      // 更新本地数据
      tags.value.push(newTag)
      saveToStorage()
      return newTag
    } catch (error) {
      console.error('Failed to create tag on server:', error)
      // 如果服务器创建失败，回退到本地创建
      return createTag(name, color)
    }
  }

  const batchCreateOrUpdateTags = async (tagNames: string[]): Promise<Tag[]> => {
    try {
      // 尝试批量创建或更新标签
      const updatedTags = await tagApi.batchCreateOrUpdate(tagNames)

      // 更新本地数据
      updatedTags.forEach(serverTag => {
        const localIndex = tags.value.findIndex(t => t.name === serverTag.name)
        if (localIndex !== -1) {
          // 更新现有标签
          tags.value[localIndex] = serverTag
        } else {
          // 添加新标签
          tags.value.push(serverTag)
        }
      })

      saveToStorage()
      return updatedTags
    } catch (error) {
      console.error('Failed to batch create/update tags on server:', error)

      // 如果服务器操作失败，回退到本地操作
      const localTags: Tag[] = []
      tagNames.forEach(tagName => {
        const existingTag = getTagByName(tagName)
        if (existingTag) {
          existingTag.usageCount++
          localTags.push(existingTag)
        } else {
          const newTag = createTag(tagName)
          localTags.push(newTag)
        }
      })

      return localTags
    }
  }

  // 初始化
  loadFromStorage()
  initializeWithSampleData()

  return {
    // 状态
    tags,

    // 计算属性
    tagsSortedByUsage,
    getTagSuggestions,

    // 本地动作
    createTag,
    updateTag,
    deleteTag,
    incrementTagUsage,
    getTagByName,
    searchTags,
    loadFromStorage,
    saveToStorage,

    // API 动作
    syncToServer,
    loadFromServer,
    createTagOnServer,
    batchCreateOrUpdateTags
  }
})
