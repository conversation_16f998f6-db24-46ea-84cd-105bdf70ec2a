import { defineStore } from 'pinia'
import { computed, ref } from 'vue'
import { messageApi } from '../services/api'
import type { NoteMessage } from '../types'
import { useNoteThemesStore } from './noteThemes'

// Topic缓存接口定义
interface TopicCache {
  themeId: string
  messages: NoteMessage[]
  scrollPosition: number
  paginationState: {
    lasterId: number
    hasMore: boolean
    isLoadingMore: boolean
  }
  lastAccessTime: number
  isLoaded: boolean
}

// LRU缓存管理器
interface TopicCacheManager {
  caches: Map<string, TopicCache>
  maxCacheSize: number
  accessOrder: string[]
}

export const useNoteMessagesStore = defineStore('noteMessages', () => {
  // 状态
  const messages = ref<NoteMessage[]>([])
  const isLoading = ref(false)

  // Topic缓存管理器
  const topicCacheManager = ref<TopicCacheManager>({
    caches: new Map(),
    maxCacheSize: 10, // 最多缓存10个topic
    accessOrder: []
  })

  // 分页加载相关状态（保留兼容性）
  const paginationState = ref<Record<string, {
    lasterId: number
    hasMore: boolean
    isLoadingMore: boolean
  }>>({})

  // 创建新的topic缓存
  const createTopicCache = (themeId: string): TopicCache => {
    return {
      themeId,
      messages: [],
      scrollPosition: 0,
      paginationState: {
        lasterId: 0,
        hasMore: true,
        isLoadingMore: false
      },
      lastAccessTime: Date.now(),
      isLoaded: false
    }
  }

  // 获取或创建topic缓存
  const getTopicCache = (themeId: string): TopicCache => {
    const manager = topicCacheManager.value

    if (!manager.caches.has(themeId)) {
      // 如果缓存已满，移除最久未访问的缓存
      if (manager.caches.size >= manager.maxCacheSize) {
        const oldestThemeId = manager.accessOrder[0]
        if (oldestThemeId) {
          manager.caches.delete(oldestThemeId)
          manager.accessOrder = manager.accessOrder.filter(id => id !== oldestThemeId)
          console.log('🗑️ 移除最久未访问的topic缓存:', oldestThemeId)
        }
      }

      // 创建新缓存
      const newCache = createTopicCache(themeId)
      manager.caches.set(themeId, newCache)
      console.log('✨ 创建新的topic缓存:', themeId)
    }

    // 更新访问时间和顺序
    const cache = manager.caches.get(themeId)!
    cache.lastAccessTime = Date.now()

    // 更新访问顺序（移到最后）
    manager.accessOrder = manager.accessOrder.filter(id => id !== themeId)
    manager.accessOrder.push(themeId)

    return cache
  }

  // 更新topic缓存的滚动位置
  const updateTopicScrollPosition = (themeId: string, scrollPosition: number) => {
    const cache = getTopicCache(themeId)
    cache.scrollPosition = scrollPosition
    console.log('📍 更新topic滚动位置:', { themeId, scrollPosition })
  }

  // 获取主题的分页状态（兼容旧接口）
  const getThemePaginationState = (themeId: string) => {
    const cache = getTopicCache(themeId)
    return cache.paginationState
  }

  // 计算属性
  const getMessagesByTheme = computed(() => {
    return (themeId: string) => {
      // 优先从缓存获取消息
      const cache = topicCacheManager.value.caches.get(themeId)
      if (cache && cache.isLoaded && cache.messages.length > 0) {
        return cache.messages.sort((a, b) => a.createdAt.getTime() - b.createdAt.getTime())
      }

      // 回退到全局消息列表（兼容性）
      return messages.value
        .filter(message => message.themeId === themeId)
        .sort((a, b) => a.createdAt.getTime() - b.createdAt.getTime())
    }
  })

  const getLatestMessage = computed(() => {
    return (themeId: string) => {
      // 优先从缓存获取最新消息
      const cache = topicCacheManager.value.caches.get(themeId)
      if (cache && cache.isLoaded && cache.messages.length > 0) {
        return cache.messages.reduce((latest, current) =>
          current.createdAt > latest.createdAt ? current : latest
        )
      }

      // 回退到全局消息列表（兼容性）
      const themeMessages = messages.value.filter(message => message.themeId === themeId)
      if (themeMessages.length === 0) return null
      return themeMessages.reduce((latest, current) =>
        current.createdAt > latest.createdAt ? current : latest
      )
    }
  })

  // 动作
  const createMessage = (themeId: string, content: string, type: NoteMessage['type'] = 'text', metadata?: NoteMessage['metadata']): NoteMessage => {
    // 对于普通文本，直接使用内容作为纯文本
    let plainText = content
    let isRichText = false

    if (type === 'rich-text') {
      try {
        const delta = JSON.parse(content)
        // 从 Quill Delta 中提取纯文本
        plainText = extractPlainTextFromDelta(delta)
        isRichText = true
      } catch (error) {
        // 如果解析失败，当作普通文本处理
        plainText = content
        type = 'text'
      }
    }

    const newMessage: NoteMessage = {
      id: generateId(),
      themeId,
      content,
      type,
      createdAt: new Date(),
      isEdited: false,
      metadata: {
        ...metadata,
        isRichText,
        plainText
      }
    }

    messages.value.push(newMessage)

    // 同时更新topic缓存
    const cache = topicCacheManager.value.caches.get(themeId)
    if (cache && cache.isLoaded) {
      cache.messages.push(newMessage)
      cache.lastAccessTime = Date.now()
      console.log('✅ 同步更新topic缓存:', { themeId, messageId: newMessage.id })
    }

    // 更新主题的最后消息信息（使用纯文本）
    const themesStore = useNoteThemesStore()
    themesStore.updateLastMessage(themeId, plainText, newMessage.createdAt)

    saveToStorage()
    return newMessage
  }

  const updateMessage = (id: string, content: string) => {
    const message = messages.value.find(msg => msg.id === id)
    if (message) {
      message.content = content
      message.updatedAt = new Date()
      message.isEdited = true

      // 同时更新topic缓存中的消息
      const cache = topicCacheManager.value.caches.get(message.themeId)
      if (cache && cache.isLoaded) {
        const cachedMessage = cache.messages.find(msg => msg.id === id)
        if (cachedMessage) {
          cachedMessage.content = content
          cachedMessage.updatedAt = new Date()
          cachedMessage.isEdited = true
          cache.lastAccessTime = Date.now()
          console.log('✅ 同步更新topic缓存中的消息:', { themeId: message.themeId, messageId: id })
        }
      }

      saveToStorage()
    }
  }

  const deleteMessage = async (id: string) => {
    const index = messages.value.findIndex(msg => msg.id === id)
    if (index !== -1) {
      const message = messages.value[index]

      try {
        // 先调用服务器删除接口
        console.log('🌐 准备删除服务器端消息:', { id, themeId: message.themeId })
        await messageApi.delete(id, message.themeId)
        console.log('✅ 服务器端消息删除成功')

        // 服务器删除成功后，再删除本地数据
        messages.value.splice(index, 1)

        // 同时从topic缓存中删除消息
        const cache = topicCacheManager.value.caches.get(message.themeId)
        if (cache && cache.isLoaded) {
          const cachedIndex = cache.messages.findIndex(msg => msg.id === id)
          if (cachedIndex !== -1) {
            cache.messages.splice(cachedIndex, 1)
            cache.lastAccessTime = Date.now()
            console.log('✅ 同步删除topic缓存中的消息:', { themeId: message.themeId, messageId: id })
          }
        }

        // 如果删除的是最后一条消息，需要更新主题的最后消息信息
        const themesStore = useNoteThemesStore()
        const latestMessage = getLatestMessage.value(message.themeId)
        if (latestMessage) {
          themesStore.updateLastMessage(message.themeId, latestMessage.content, latestMessage.createdAt)
        } else {
          // 如果没有消息了，清空最后消息信息
          themesStore.updateTheme(message.themeId, {
            lastMessage: undefined,
            lastMessageTime: undefined,
            messageCount: 0
          })
        }

        saveToStorage()
        console.log('✅ 本地消息删除成功')
      } catch (error) {
        console.error('❌ 服务器端消息删除失败:', error)
        console.error('❌ 错误详情:', {
          message: (error as any)?.message,
          status: (error as any)?.status,
          response: (error as any)?.response
        })

        // 如果服务器删除失败，仍然删除本地数据（可选择是否这样做）
        console.log('🔄 服务器删除失败，但仍删除本地数据')
        messages.value.splice(index, 1)

        // 更新主题的最后消息信息
        const themesStore = useNoteThemesStore()
        const latestMessage = getLatestMessage.value(message.themeId)
        if (latestMessage) {
          themesStore.updateLastMessage(message.themeId, latestMessage.content, latestMessage.createdAt)
        } else {
          themesStore.updateTheme(message.themeId, {
            lastMessage: undefined,
            lastMessageTime: undefined,
            messageCount: 0
          })
        }

        saveToStorage()
        console.log('✅ 本地消息删除完成（服务器删除失败）')
      }
    }
  }

  const deleteMessagesByTheme = (themeId: string) => {
    messages.value = messages.value.filter(msg => msg.themeId !== themeId)
    saveToStorage()
  }

  const searchMessages = (query: string, themeId?: string): NoteMessage[] => {
    if (!query.trim()) return []

    const lowerQuery = query.toLowerCase()
    let filteredMessages = messages.value

    if (themeId) {
      filteredMessages = filteredMessages.filter(msg => msg.themeId === themeId)
    }

    return filteredMessages.filter(message => {
      // 对于富文本消息，搜索纯文本版本
      const searchText = message.metadata?.plainText || message.content
      return searchText.toLowerCase().includes(lowerQuery)
    })
  }

  const exportMessages = (themeId: string): string => {
    const themeMessages = getMessagesByTheme.value(themeId)
    const themesStore = useNoteThemesStore()
    const theme = themesStore.themes.find(t => t.id === themeId)

    let exportText = `# ${theme?.title || '未知主题'}\n\n`

    themeMessages.forEach(message => {
      const timestamp = message.createdAt.toLocaleString()
      exportText += `**${timestamp}**\n\n${message.content}\n\n---\n\n`
    })

    return exportText
  }

  const importMessages = (themeId: string, content: string) => {
    // 简单的导入功能，按行分割内容
    const lines = content.split('\n').filter(line => line.trim())

    lines.forEach(line => {
      if (line.trim()) {
        createMessage(themeId, line.trim())
      }
    })
  }

  // 工具函数
  const generateId = (): string => {
    return Date.now().toString(36) + Math.random().toString(36).substring(2)
  }

  const extractPlainTextFromDelta = (delta: any): string => {
    if (!delta || !delta.ops) return ''

    return delta.ops
      .map((op: any) => {
        if (typeof op.insert === 'string') {
          return op.insert
        }
        return ''
      })
      .join('')
      .trim()
  }

  const saveToStorage = () => {
    try {
      localStorage.setItem('noteMessages', JSON.stringify(messages.value))
    } catch (error) {
      console.error('Failed to save messages to storage:', error)
    }
  }

  const loadFromStorage = () => {
    try {
      const stored = localStorage.getItem('noteMessages')
      if (stored) {
        const parsed = JSON.parse(stored)
        messages.value = parsed.map((message: any) => ({
          ...message,
          createdAt: new Date(message.createdAt),
          updatedAt: message.updatedAt ? new Date(message.updatedAt) : undefined
        }))
      }
    } catch (error) {
      console.error('Failed to load messages from storage:', error)
    }
  }

  const initializeWithSampleData = () => {
    // 只有在没有消息时才初始化示例数据
    if (messages.value.length === 0) {
      const sampleMessages: NoteMessage[] = [
        {
          id: 'm1',
          themeId: '1',
          content: '192765459342525235',
          type: 'text',
          createdAt: new Date('2024-05-29T17:15:00'),
          isEdited: false
        },
        {
          id: 'm2',
          themeId: '1',
          content: '【丰巢】先取件码143127771至龙腾苑六区1号楼东侧丰巢柜距3号柜取件。有疑问联系快递员15201571165',
          type: 'text',
          createdAt: new Date('2024-05-29T16:43:00'),
          isEdited: false
        },
        {
          id: 'm3',
          themeId: '1',
          content: 'https://github.com/vber/free-augmentcode',
          type: 'link',
          createdAt: new Date('2024-05-29T22:42:00'),
          isEdited: false,
          metadata: {
            linkUrl: 'https://github.com/vber/free-augmentcode',
            linkTitle: 'Free Augment Code'
          }
        }
      ]

      messages.value = sampleMessages
      saveToStorage()
    }
  }

  // 初始化
  loadFromStorage()
  initializeWithSampleData()

  // API 相关方法
  const createMessageOnServer = async (
    themeId: string,
    content: string,
    type: NoteMessage['type'] = 'text',
    metadata?: NoteMessage['metadata']
  ): Promise<NoteMessage | null> => {
    console.log('🌐 createMessageOnServer 开始执行')
    console.log('🌐 参数:', { themeId, contentLength: content?.length, type, metadata })

    try {
      console.log('🌐 准备调用 messageApi.create')
      const messageData = {
        themeId,
        content,
        type,
        metadata
      }
      console.log('🌐 发送的数据:', messageData)

      const newMessage = await messageApi.create(messageData)
      console.log('✅ 服务器创建消息成功:', newMessage)

      // 更新本地数据，避免重复 ID
      console.log('🌐 准备更新本地数据')

      // 检查是否已存在相同 ID 的消息
      const existingIndex = messages.value.findIndex(m => m.id === newMessage.id)
      if (existingIndex !== -1) {
        // 如果存在，替换现有消息
        messages.value[existingIndex] = newMessage
        console.log('🌐 替换现有消息:', newMessage.id)
      } else {
        // 如果不存在，添加新消息
        messages.value.push(newMessage)
        console.log('🌐 添加新消息:', newMessage.id)
      }

      // 同时更新topic缓存
      const cache = topicCacheManager.value.caches.get(themeId)
      if (cache && cache.isLoaded) {
        const cachedIndex = cache.messages.findIndex(m => m.id === newMessage.id)
        if (cachedIndex !== -1) {
          cache.messages[cachedIndex] = newMessage
          console.log('🌐 替换缓存中的消息:', newMessage.id)
        } else {
          cache.messages.push(newMessage)
          console.log('🌐 添加消息到缓存:', newMessage.id)
        }
        cache.lastAccessTime = Date.now()
      }

      console.log('🌐 本地消息数组长度:', messages.value.length)

      console.log('🌐 准备保存到本地存储')
      saveToStorage()
      console.log('✅ 本地存储保存完成')

      return newMessage
    } catch (error) {
      console.error('❌ 服务器创建消息失败:', error)
      console.error('❌ 错误详情:', {
        message: (error as any)?.message,
        status: (error as any)?.status,
        response: (error as any)?.response
      })

      console.log('🔄 回退到本地创建消息')
      const fallbackMessage = createMessage(themeId, content, type, metadata)
      console.log('✅ 本地创建消息成功:', fallbackMessage)

      return fallbackMessage
    }
  }

  const syncToServer = async (): Promise<boolean> => {
    try {
      // 将本地消息同步到服务器
      for (const message of messages.value) {
        await messageApi.create({
          themeId: message.themeId,
          content: message.content,
          type: message.type,
          metadata: message.metadata
        })
      }
      return true
    } catch (error) {
      console.error('Failed to sync messages to server:', error)
      return false
    }
  }

  const loadFromServer = async (themeId?: string, forceReload = false): Promise<boolean> => {
    try {
      if (!themeId) {
        console.warn('loadFromServer: themeId is required')
        return false
      }

      // 检查缓存是否已存在且已加载
      const cache = getTopicCache(themeId)
      if (!forceReload && cache.isLoaded && cache.messages.length > 0) {
        console.log('📋 使用缓存的消息:', { themeId, count: cache.messages.length })
        return true
      }

      console.log('🔄 从服务器加载消息:', themeId)

      // 重置分页状态
      cache.paginationState.lasterId = 0
      cache.paginationState.hasMore = true

      const result = await messageApi.getByTheme(themeId, 0)

      // 更新缓存
      cache.messages = [...result.messages]
      cache.paginationState.lasterId = result.lasterId
      cache.paginationState.hasMore = result.hasMore
      cache.isLoaded = true
      cache.lastAccessTime = Date.now()

      // 同时更新全局消息列表（兼容性）
      messages.value = messages.value.filter(m => m.themeId !== themeId)
      messages.value.push(...result.messages)

      saveToStorage()
      console.log('✅ 消息加载成功:', {
        themeId,
        count: result.messages.length,
        lasterId: result.lasterId,
        hasMore: result.hasMore,
        cached: true
      })
      return true
    } catch (error) {
      console.error('Failed to load messages from server:', error)
      return false
    }
  }

  // 加载更多消息（分页加载）
  const loadMoreMessages = async (themeId: string): Promise<boolean> => {
    try {
      const cache = getTopicCache(themeId)
      const paginationState = cache.paginationState

      if (!paginationState.hasMore || paginationState.isLoadingMore) {
        console.log('🚫 无需加载更多消息:', {
          hasMore: paginationState.hasMore,
          isLoadingMore: paginationState.isLoadingMore
        })
        return false
      }

      console.log('🔄 加载更多消息:', { themeId, lasterId: paginationState.lasterId })

      paginationState.isLoadingMore = true

      const result = await messageApi.getByTheme(themeId, paginationState.lasterId)

      // 创建一个 Map 来去重，避免重复的消息 ID
      const messageMap = new Map<string, NoteMessage>()

      // 先添加缓存中的现有消息
      cache.messages.forEach(msg => messageMap.set(msg.id, msg))

      // 再添加新消息（如果 ID 相同，新消息会覆盖旧消息）
      result.messages.forEach(msg => messageMap.set(msg.id, msg))

      // 按时间排序，更新缓存
      cache.messages = Array.from(messageMap.values())
        .sort((a, b) => a.createdAt.getTime() - b.createdAt.getTime())

      // 同时更新全局消息列表（兼容性）
      const otherMessages = messages.value.filter(m => m.themeId !== themeId)
      messages.value = [...otherMessages, ...cache.messages]

      // 更新分页状态
      paginationState.lasterId = result.lasterId
      paginationState.hasMore = result.hasMore
      paginationState.isLoadingMore = false
      cache.lastAccessTime = Date.now()

      saveToStorage()
      console.log('✅ 更多消息加载成功:', {
        themeId,
        newCount: result.messages.length,
        totalCount: cache.messages.length,
        lasterId: result.lasterId,
        hasMore: result.hasMore,
        cached: true
      })
      return true
    } catch (error) {
      console.error('Failed to load more messages:', error)
      const cache = getTopicCache(themeId)
      cache.paginationState.isLoadingMore = false
      return false
    }
  }

  return {
    // 状态
    messages,
    isLoading,
    paginationState,
    topicCacheManager,

    // 计算属性
    getMessagesByTheme,
    getLatestMessage,
    getThemePaginationState,

    // 缓存管理
    getTopicCache,
    updateTopicScrollPosition,

    // 本地动作
    createMessage,
    updateMessage,
    deleteMessage,
    deleteMessagesByTheme,
    searchMessages,
    exportMessages,
    importMessages,
    loadFromStorage,
    saveToStorage,

    // API 动作
    createMessageOnServer,
    syncToServer,
    loadFromServer,
    loadMoreMessages
  }
})
