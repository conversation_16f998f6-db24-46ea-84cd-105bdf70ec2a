import { createRouter, createWebHashHistory, createWebHistory } from 'vue-router'
import { useAuthStore } from '../stores/auth'

// 在 Electron 环境下使用 hash 路由，在 Web 环境下使用 history 路由
// 使用更可靠的 Electron 环境检测方法
const isElectron = typeof window !== 'undefined' && (
  // 检查 electronAPI 是否存在（由 preload 脚本注入）
  !!(window as any).electronAPI ||
  // 检查 process 对象
  (window.process && window.process.type === 'renderer') ||
  // 检查 navigator.userAgent
  /electron/i.test(navigator.userAgent)
)

console.log('Router configuration:', {
  isElectron,
  baseUrl: import.meta.env.BASE_URL,
  historyMode: isElectron ? 'hash' : 'history'
})

const router = createRouter({
  history: isElectron ? createWebHashHistory() : createWebHistory(import.meta.env.BASE_URL),
  routes: [
    // 认证相关路由（无需登录）
    {
      path: '/login',
      name: 'login',
      component: () => import('../views/LoginView.vue'),
      meta: { requiresGuest: true }
    },
    {
      path: '/register',
      name: 'register',
      component: () => import('../views/RegisterView.vue'),
      meta: { requiresGuest: true }
    },
    {
      path: '/forgot-password',
      name: 'forgot-password',
      component: () => import('../views/ForgotPasswordView.vue'),
      meta: { requiresGuest: true }
    },

    // 主应用路由（需要登录）
    {
      path: '/',
      name: 'home',
      redirect: '/notes',
      meta: { requiresAuth: true }
    },

    {
      path: '/notes',
      name: 'notes',
      component: () => import('../views/NotesView.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/tags',
      name: 'tags',
      component: () => import('../views/TagsView.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/starred',
      name: 'starred',
      component: () => import('../views/StarredView.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/settings',
      name: 'settings',
      component: () => import('../views/SettingsView.vue'),
      meta: { requiresAuth: true }
    },

  ],
})

// 路由守卫
router.beforeEach((to, _from, next) => {
  console.log('Route navigation:', {
    to: to.path,
    from: _from.path,
    meta: to.meta
  })

  const authStore = useAuthStore()

  const isAuthenticated = authStore.isAuthenticated
  const requiresAuth = to.meta.requiresAuth
  const requiresGuest = to.meta.requiresGuest

  console.log('Auth state:', {
    isAuthenticated,
    requiresAuth,
    requiresGuest,
    user: authStore.user,
    token: !!authStore.token
  })

  if (requiresAuth && !isAuthenticated) {
    // 需要登录但未登录，跳转到登录页
    console.log('Redirecting to login: requires auth but not authenticated')
    next('/login')
  } else if (requiresGuest && isAuthenticated) {
    // 已登录用户访问登录/注册页面，跳转到主页
    console.log('Redirecting to home: guest route but authenticated')
    next('/')
  } else {
    // 正常访问
    console.log('Allowing navigation')
    next()
  }
})

export default router
