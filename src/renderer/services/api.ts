import { config, needsAuth } from '../config'
import type {
  CreateNoteRequest,
  CreateNoteResponse,
  CreateNoteTopicRequest,
  CreateNoteTopicResponse,
  ListNoteResponse,
  NoteMessage,
  NoteTheme,
  Tag,
  User
} from '../types'

// HTTP 请求工具类
class ApiClient {
  private baseURL: string

  constructor(baseURL: string) {
    this.baseURL = baseURL
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${this.baseURL}${endpoint}`

    console.log('🌐 API请求开始')
    console.log('🌐 URL:', url)
    console.log('🌐 Method:', options.method || 'GET')
    console.log('🌐 Body:', options.body)

    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
      ...(options.headers as Record<string, string> || {}),
    }

    // 检查是否需要添加认证token
    if (needsAuth(endpoint)) {
      const token = localStorage.getItem('auth_token')
      console.log('🌐 需要认证，token存在:', !!token)
      if (token) {
        headers.Authorization = `${token}`
      }
    } else {
      console.log('🌐 不需要认证')
    }

    const config: RequestInit = {
      ...options,
      headers,
    }

    console.log('🌐 请求配置:', {
      method: config.method,
      headers: config.headers,
      bodyLength: config.body ? (config.body as string).length : 0
    })

    try {
      console.log('🌐 发送请求...')
      const response = await fetch(url, config)

      console.log('🌐 收到响应')
      console.log('🌐 响应状态:', response.status)
      console.log('🌐 响应状态文本:', response.statusText)
      console.log('🌐 响应头:', Object.fromEntries(response.headers.entries()))

      if (!response.ok) {
        console.error('❌ HTTP响应错误')
        console.error('❌ 状态码:', response.status)
        console.error('❌ 状态文本:', response.statusText)

        // 尝试读取错误响应体
        try {
          const errorText = await response.text()
          console.error('❌ 错误响应体:', errorText)
        } catch (e) {
          console.error('❌ 无法读取错误响应体:', e)
        }

        // 如果是401错误且当前接口需要认证，可能是token过期
        if (response.status === 401 && needsAuth(endpoint)) {
          console.warn('❌ 认证失败，可能需要重新登录')
        }
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      console.log('✅ 响应成功，准备解析JSON')
      const result = await response.json()
      console.log('✅ JSON解析成功:', result)
      return result
    } catch (error) {
      console.error('❌ API请求失败:', error)
      console.error('❌ 错误类型:', (error as any)?.name)
      console.error('❌ 错误消息:', (error as any)?.message)
      console.error('❌ 错误堆栈:', (error as any)?.stack)
      throw error
    }
  }

  // GET 请求
  async get<T>(endpoint: string): Promise<T> {
    return this.request<T>(endpoint, { method: 'GET' })
  }

  // POST 请求
  async post<T>(endpoint: string, data?: any): Promise<T> {
    return this.request<T>(endpoint, {
      method: 'POST',
      body: data ? JSON.stringify(data) : undefined,
    })
  }

  // PUT 请求
  async put<T>(endpoint: string, data?: any): Promise<T> {
    return this.request<T>(endpoint, {
      method: 'PUT',
      body: data ? JSON.stringify(data) : undefined,
    })
  }

  // DELETE 请求
  async delete<T>(endpoint: string, data?: any): Promise<T> {
    return this.request<T>(endpoint, {
      method: 'DELETE',
      body: data ? JSON.stringify(data) : undefined,
    })
  }


}

// 创建 API 客户端实例
const apiClient = new ApiClient(config.api.baseUrl)

// 数据转换工具函数
const convertServerResponseToNoteTheme = (serverResponse: CreateNoteTopicResponse): NoteTheme => {
  return {
    id: serverResponse.id,
    title: serverResponse.title,
    description: serverResponse.description || undefined,
    avatar: serverResponse.avatar || undefined,
    createdAt: new Date(serverResponse.created_time * 1000),
    updatedAt: new Date(serverResponse.updated_time * 1000),
    tags: [],
    isStarred: false,
    messageCount: 0
  }
}

// 将后端创建笔记响应转换为前端 NoteMessage 格式
const convertServerResponseToNoteMessage = (serverResponse: CreateNoteResponse): NoteMessage => {
  return {
    id: serverResponse.id.toString(), // 后端是 number，前端是 string
    themeId: serverResponse.topic_id,
    content: serverResponse.content,
    type: serverResponse.note_type as NoteMessage['type'],
    createdAt: new Date(serverResponse.created_time * 1000), // 后端是秒，前端是毫秒
    updatedAt: new Date(serverResponse.updated_time * 1000),
    isEdited: false,
    metadata: {
      isRichText: serverResponse.note_type === 'rich-text',
      plainText: serverResponse.note_type === 'rich-text' ? extractPlainTextFromContent(serverResponse.content) : serverResponse.content
    }
  }
}

// 将后端列表笔记响应转换为前端 NoteMessage 格式
const convertListNoteResponseToNoteMessage = (serverResponse: ListNoteResponse): NoteMessage => {
  return {
    id: serverResponse.id.toString(), // 后端是 number，前端是 string
    themeId: serverResponse.topic_id,
    content: serverResponse.content,
    type: serverResponse.note_type as NoteMessage['type'],
    createdAt: new Date(serverResponse.created_time * 1000), // 后端是秒，前端是毫秒
    updatedAt: new Date(serverResponse.updated_time * 1000),
    isEdited: false,
    metadata: {
      isRichText: serverResponse.note_type === 'rich-text',
      plainText: serverResponse.note_type === 'rich-text' ? extractPlainTextFromContent(serverResponse.content) : serverResponse.content
    }
  }
}

// 从富文本内容中提取纯文本
const extractPlainTextFromContent = (content: string): string => {
  try {
    const delta = JSON.parse(content)
    if (delta && delta.ops) {
      return delta.ops
        .map((op: any) => typeof op.insert === 'string' ? op.insert : '')
        .join('')
        .trim()
    }
  } catch (error) {
    // 如果不是 JSON 格式，直接返回原内容
  }
  return content
}

// 笔记主题相关 API
export const themeApi = {
  // 获取所有主题
  getAll: async (): Promise<NoteTheme[]> => {
    const response = await apiClient.get<{ data: CreateNoteTopicResponse[] }>('/api/v1/note_topic/list')
    return response.data.map(convertServerResponseToNoteTheme)
  },

  // 获取单个主题
  getById: (id: string): Promise<NoteTheme> =>
    apiClient.get<NoteTheme>(`/themes/${id}`),

  // 创建主题
  create: async (themeData: { title: string; description?: string; avatar?: string }): Promise<NoteTheme> => {
    const request: CreateNoteTopicRequest = {
      title: themeData.title,
      description: themeData.description || '',
      avatar: themeData.avatar || ''
    }

    const response = await apiClient.post<{ data: CreateNoteTopicResponse }>('/api/v1/note_topic/create', request)
    return convertServerResponseToNoteTheme(response.data)
  },

  // 更新主题
  update: (id: string, theme: Partial<NoteTheme>): Promise<NoteTheme> =>
    apiClient.put<NoteTheme>(`/themes/${id}`, theme),

  // 删除主题
  delete: (id: string): Promise<void> =>
    apiClient.delete<void>(`/themes/${id}`),
}

// 笔记消息相关 API
export const messageApi = {
  // 获取主题下的消息列表（支持分页）
  getByTheme: async (themeId: string, lasterId?: number): Promise<{ messages: NoteMessage[], lasterId: number, hasMore: boolean }> => {
    const params = new URLSearchParams({ topic_id: themeId })
    if (lasterId !== undefined && lasterId > 0) {
      params.append('laster_id', lasterId.toString())
    }

    console.log('🌐 调用 /api/v1/note/list 接口，参数:', { topic_id: themeId, laster_id: lasterId })

    // 使用临时类型定义，因为导入可能失败
    const response = await apiClient.get<{
      status: number
      message: string
      data: {
        notes: ListNoteResponse[]
        laster_id: string
      }
      time: number
      request_id?: string
    }>(`/api/v1/note/list?${params.toString()}`)

    console.log('🌐 后端响应:', response)

    const messages = response.data.notes.map(convertListNoteResponseToNoteMessage)
    const newLasterId = parseInt(response.data.laster_id)
    const hasMore = newLasterId > 0

    console.log('✅ 消息列表获取成功:', {
      messagesCount: messages.length,
      lasterId: newLasterId,
      hasMore
    })

    return {
      messages,
      lasterId: newLasterId,
      hasMore
    }
  },

  // 获取单个消息
  getById: (id: string): Promise<NoteMessage> =>
    apiClient.get<NoteMessage>(`/messages/${id}`),

  // 创建消息 - 对接后端 /api/v1/note/create 接口
  create: async (message: Omit<NoteMessage, 'id' | 'createdAt' | 'updatedAt' | 'isEdited'>): Promise<NoteMessage> => {
    // 构造后端接口需要的请求参数
    const request: CreateNoteRequest = {
      topic_id: message.themeId,
      note_type: message.type,
      content: message.content
    }

    console.log('🌐 调用 /api/v1/note/create 接口，请求参数:', request)

    // 调用后端接口创建笔记
    const response = await apiClient.post<{
      status: number
      message: string
      data: CreateNoteResponse
      time: number
      request_id?: string
    }>('/api/v1/note/create', request)
    console.log('🌐 后端响应:', response)

    // 使用转换函数将后端响应转换为前端 NoteMessage 格式
    const createdMessage = convertServerResponseToNoteMessage(response.data)

    // 合并前端传入的 metadata
    if (message.metadata) {
      createdMessage.metadata = {
        ...createdMessage.metadata,
        ...message.metadata
      }
    }

    console.log('✅ 笔记创建成功，转换后的对象:', createdMessage)
    return createdMessage
  },

  // 更新消息
  update: (id: string, message: Partial<NoteMessage>): Promise<NoteMessage> =>
    apiClient.put<NoteMessage>(`/messages/${id}`, message),

  // 删除消息 - 对接后端 /api/v1/note/delete 接口
  delete: async (id: string, themeId: string): Promise<void> => {
    // 构造后端接口需要的请求参数
    const request = {
      topic_id: themeId,
      note_id: id
    }

    console.log('🌐 调用 /api/v1/note/delete 接口，请求参数:', request)

    // 调用后端接口删除笔记
    const response = await apiClient.delete<{
      status: number
      message: string
      data: string
      time: number
      request_id?: string
    }>('/api/v1/note/delete', request)

    console.log('🌐 后端响应:', response)
    console.log('✅ 笔记删除成功')
  },

  // 搜索消息
  search: (query: string): Promise<NoteMessage[]> =>
    apiClient.get<NoteMessage[]>(`/messages/search?q=${encodeURIComponent(query)}`),
}

// 标签相关 API
export const tagApi = {
  // 获取所有标签
  getAll: (): Promise<Tag[]> =>
    apiClient.get<Tag[]>('/tags'),

  // 获取单个标签
  getById: (id: string): Promise<Tag> =>
    apiClient.get<Tag>(`/tags/${id}`),

  // 创建标签
  create: (tag: Omit<Tag, 'id' | 'createdAt' | 'usageCount'>): Promise<Tag> =>
    apiClient.post<Tag>('/tags', tag),

  // 更新标签
  update: (id: string, tag: Partial<Tag>): Promise<Tag> =>
    apiClient.put<Tag>(`/tags/${id}`, tag),

  // 删除标签
  delete: (id: string): Promise<void> =>
    apiClient.delete<void>(`/tags/${id}`),

  // 增加标签使用次数
  incrementUsage: (id: string): Promise<Tag> =>
    apiClient.post<Tag>(`/tags/${id}/increment-usage`),

  // 批量创建或更新标签
  batchCreateOrUpdate: (tags: string[]): Promise<Tag[]> =>
    apiClient.post<Tag[]>('/tags/batch', { tags }),
}

// 用户相关 API
export const userApi = {
  // 获取当前用户信息
  getCurrentUser: (): Promise<User> =>
    apiClient.get<User>('/user/profile'),

  // 更新用户信息
  updateProfile: (user: Partial<User>): Promise<User> =>
    apiClient.put<User>('/user/profile', user),

  // 用户登录
  login: (credentials: { email: string; password: string }): Promise<{ user: User; token: string }> =>
    apiClient.post<{ user: User; token: string }>('/auth/login', credentials),

  // 用户注册
  register: (userData: { username: string; email: string; password: string }): Promise<{ user: User; token: string }> =>
    apiClient.post<{ user: User; token: string }>('/auth/register', userData),

  // 用户登出
  logout: (): Promise<void> =>
    apiClient.post<void>('/auth/logout'),
}

// 同步相关 API
export const syncApi = {
  // 同步所有数据到服务器
  syncToServer: (data: {
    themes: NoteTheme[]
    messages: NoteMessage[]
    tags: Tag[]
  }): Promise<{ success: boolean; message: string }> =>
    apiClient.post<{ success: boolean; message: string }>('/sync/upload', data),

  // 从服务器同步数据
  syncFromServer: (): Promise<{
    themes: NoteTheme[]
    messages: NoteMessage[]
    tags: Tag[]
  }> =>
    apiClient.get<{
      themes: NoteTheme[]
      messages: NoteMessage[]
      tags: Tag[]
    }>('/sync/download'),

  // 检查同步状态
  getSyncStatus: (): Promise<{
    lastSyncTime: string
    hasChanges: boolean
  }> =>
    apiClient.get<{
      lastSyncTime: string
      hasChanges: boolean
    }>('/sync/status'),
}

// 导出 API 客户端
export { apiClient }

// 导出默认配置
export default {
  themeApi,
  messageApi,
  tagApi,
  userApi,
  syncApi,
}
