<template>
  <div>
    <!-- 发现更新通知 -->
    <n-modal
      v-model:show="updateState.updateAvailable"
      preset="dialog"
      title="发现新版本"
      :positive-text="updateState.downloading ? '下载中...' : '立即更新'"
      negative-text="稍后提醒"
      :positive-button-props="{ disabled: updateState.downloading }"
      @positive-click="handleDownloadUpdate"
      @negative-click="handleLater"
    >
      <div class="update-content">
        <p>发现新版本 {{ updateState.updateInfo?.version }}，是否立即下载更新？</p>
        <div v-if="updateState.updateInfo?.releaseNotes" class="release-notes">
          <h4>更新内容：</h4>
          <div v-html="updateState.updateInfo.releaseNotes"></div>
        </div>
      </div>
    </n-modal>

    <!-- 下载进度 -->
    <n-modal
      v-model:show="updateState.downloading"
      preset="card"
      title="下载更新"
      :closable="false"
      :mask-closable="false"
      style="width: 400px"
    >
      <div class="download-progress">
        <n-progress
          type="line"
          :percentage="updateState.downloadProgress.percent"
          :show-indicator="true"
        />
        <div class="progress-info">
          <p>下载进度: {{ Math.round(updateState.downloadProgress.percent) }}%</p>
          <p>下载速度: {{ formatBytes(updateState.downloadProgress.bytesPerSecond) }}/s</p>
          <p>已下载: {{ formatBytes(updateState.downloadProgress.transferred) }} / {{ formatBytes(updateState.downloadProgress.total) }}</p>
        </div>
      </div>
    </n-modal>

    <!-- 下载完成，准备安装 -->
    <n-modal
      v-model:show="updateState.readyToInstall"
      preset="dialog"
      title="更新已下载"
      positive-text="立即重启安装"
      negative-text="稍后安装"
      @positive-click="handleInstallUpdate"
      @negative-click="handleInstallLater"
    >
      <p>更新已下载完成，需要重启应用来安装更新。</p>
      <p>重启后将自动安装新版本。</p>
    </n-modal>

    <!-- 错误提示 -->
    <n-modal
      v-model:show="updateState.showError"
      preset="dialog"
      title="更新失败"
      positive-text="确定"
      @positive-click="clearError"
    >
      <p>{{ updateState.error }}</p>
    </n-modal>
  </div>
</template>

<script setup lang="ts">
import { NModal, NProgress } from 'naive-ui'
import { onMounted, onUnmounted, reactive } from 'vue'

interface UpdateInfo {
  version: string
  releaseNotes?: string
  releaseDate?: string
}

interface DownloadProgress {
  percent: number
  bytesPerSecond: number
  transferred: number
  total: number
}

interface UpdateState {
  checking: boolean
  updateAvailable: boolean
  downloading: boolean
  readyToInstall: boolean
  showError: boolean
  error: string | null
  updateInfo: UpdateInfo | null
  downloadProgress: DownloadProgress
}

const updateState = reactive<UpdateState>({
  checking: false,
  updateAvailable: false,
  downloading: false,
  readyToInstall: false,
  showError: false,
  error: null,
  updateInfo: null,
  downloadProgress: {
    percent: 0,
    bytesPerSecond: 0,
    transferred: 0,
    total: 0
  }
})

// 格式化字节数
const formatBytes = (bytes: number): string => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 检查更新
const checkForUpdates = async (showErrorDialog = false) => {
  if (!window.electronAPI) {
    if (showErrorDialog) {
      updateState.error = 'ElectronAPI 不可用'
      updateState.showError = true
    }
    return
  }

  try {
    // 传递 showErrorDialog 作为 isManual 参数
    const result = await window.electronAPI.checkForUpdates(showErrorDialog)
    if (result?.error && showErrorDialog) {
      updateState.error = result.error
      updateState.showError = true
    }
  } catch (error) {
    if (showErrorDialog) {
      updateState.error = '检查更新失败'
      updateState.showError = true
    }
    console.error('Check for updates error:', error)
  }
}

// 下载更新
const handleDownloadUpdate = async () => {
  if (!window.electronAPI) {
    updateState.error = 'ElectronAPI 不可用'
    updateState.showError = true
    return
  }

  updateState.updateAvailable = false
  updateState.downloading = true

  try {
    const result = await window.electronAPI.downloadUpdate()
    if (result?.error) {
      updateState.error = result.error
      updateState.showError = true
      updateState.downloading = false
    }
  } catch (error) {
    updateState.error = '下载更新失败'
    updateState.showError = true
    updateState.downloading = false
    console.error('Download update error:', error)
  }
}

// 安装更新
const handleInstallUpdate = async () => {
  if (!window.electronAPI) {
    updateState.error = 'ElectronAPI 不可用'
    updateState.showError = true
    return
  }

  try {
    await window.electronAPI.installUpdate()
  } catch (error) {
    updateState.error = '安装更新失败'
    updateState.showError = true
    console.error('Install update error:', error)
  }
}

// 稍后提醒
const handleLater = () => {
  updateState.updateAvailable = false
}

// 稍后安装
const handleInstallLater = () => {
  updateState.readyToInstall = false
}

// 清除错误
const clearError = () => {
  updateState.error = null
  updateState.showError = false
}

// 监听更新事件
const setupUpdateListeners = () => {
  if (!window.electronAPI) {
    console.warn('ElectronAPI 不可用，无法设置更新监听器')
    return
  }

  // 检查更新中
  window.electronAPI.onUpdateChecking(() => {
    updateState.checking = true
  })

  // 发现更新
  window.electronAPI.onUpdateAvailable((info: UpdateInfo) => {
    updateState.checking = false
    updateState.updateAvailable = true
    updateState.updateInfo = info
  })

  // 没有更新
  window.electronAPI.onUpdateNotAvailable(() => {
    updateState.checking = false
  })

  // 下载进度
  window.electronAPI.onUpdateDownloadProgress((progress: DownloadProgress) => {
    updateState.downloadProgress = progress
  })

  // 下载完成
  window.electronAPI.onUpdateDownloaded(() => {
    updateState.downloading = false
    updateState.readyToInstall = true
  })

  // 更新错误
  window.electronAPI.onUpdateError((error: string, showDialog = false) => {
    updateState.checking = false
    updateState.downloading = false
    updateState.error = error
    updateState.showError = showDialog
    // 只在控制台记录错误，不弹出对话框（除非明确要求）
    console.error('Update error:', error)
  })
}

// 移除监听器
const removeUpdateListeners = () => {
  // 这里应该移除所有监听器，但由于 electronAPI 的实现方式，
  // 我们暂时不实现移除逻辑
}

onMounted(() => {
  setupUpdateListeners()
})

onUnmounted(() => {
  removeUpdateListeners()
})

// 暴露检查更新方法
defineExpose({
  checkForUpdates,
  // 用户主动检查更新（显示错误对话框）
  checkForUpdatesWithDialog: () => checkForUpdates(true)
})
</script>

<style scoped>
.update-content {
  padding: 16px 0;
}

.release-notes {
  margin-top: 16px;
  padding: 12px;
  background-color: var(--n-color-target);
  border-radius: 6px;
}

.release-notes h4 {
  margin: 0 0 8px 0;
  font-size: 14px;
  font-weight: 600;
}

.download-progress {
  padding: 16px 0;
}

.progress-info {
  margin-top: 16px;
}

.progress-info p {
  margin: 4px 0;
  font-size: 12px;
  color: var(--n-text-color-2);
}
</style>
