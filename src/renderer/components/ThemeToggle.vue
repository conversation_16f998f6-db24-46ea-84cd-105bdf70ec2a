<template>
  <div class="theme-toggle">
    <NButton
      :type="isDark ? 'primary' : 'default'"
      @click="toggleTheme"
      circle
      size="tiny"
    >
      <template #icon>
        <NIcon>
          <SunnyOutline v-if="isDark" />
          <MoonOutline v-else />
        </NIcon>
      </template>
    </NButton>
  </div>
</template>

<script setup lang="ts">
import { inject } from 'vue'
import { NButton, NIcon } from 'naive-ui'
import { SunnyOutline, MoonOutline } from '@vicons/ionicons5'

// 从父组件注入主题状态
const isDark = inject<{ value: boolean }>('isDark')
const toggleTheme = inject<() => void>('toggleTheme')

if (!isDark || !toggleTheme) {
  console.warn('ThemeToggle: 主题状态或切换函数未正确注入')
}
</script>

<style scoped>
.theme-toggle {
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
