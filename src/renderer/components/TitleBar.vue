<template>
  <div class="title-bar">
    <!-- 可拖动区域 -->
    <div class="drag-region">
      <div class="app-title">ChatNote</div>
    </div>

    <!-- 右侧控制按钮区域（不可拖动） -->
    <div class="window-controls">
      <!-- 主题切换按钮 -->
      <div class="control-button theme-toggle">
        <ThemeToggle />
      </div>

      <!-- Windows/Linux 窗口控制按钮 -->
      <template v-if="!isMacOS">
        <div class="control-button minimize" @click="minimizeWindow">
          <NIcon size="12">
            <RemoveOutline />
          </NIcon>
        </div>
        <div class="control-button maximize" @click="maximizeWindow">
          <NIcon size="12">
            <StopOutline />
          </NIcon>
        </div>
        <div class="control-button close" @click="closeWindow">
          <NIcon size="12">
            <CloseOutline />
          </NIcon>
        </div>
      </template>
    </div>
  </div>
</template>

<script setup lang="ts">
import { CloseOutline, RemoveOutline, StopOutline } from '@vicons/ionicons5'
import { NIcon } from 'naive-ui'
import { computed } from 'vue'
import ThemeToggle from './ThemeToggle.vue'

// 检测操作系统
const isMacOS = computed(() => {
  return navigator.userAgent.toUpperCase().indexOf('MAC') >= 0
})

// 窗口控制函数（仅在 Electron 环境中有效）
const minimizeWindow = () => {
  if (window.electronAPI) {
    window.electronAPI.minimizeWindow()
  }
}

const maximizeWindow = () => {
  if (window.electronAPI) {
    window.electronAPI.maximizeWindow()
  }
}

const closeWindow = () => {
  if (window.electronAPI) {
    window.electronAPI.closeWindow()
  }
}
</script>

<style scoped>
.title-bar {
  display: flex;
  align-items: center;
  height: 32px;
  background-color: var(--n-color);
  border-bottom: 1px solid var(--n-border-color);
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  user-select: none;
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
}

.drag-region {
  flex: 1;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  padding-left: 80px; /* 为 macOS 红绿灯按钮留出空间 */
  padding-right: 80px; /* 保持居中 */
  /* 使这个区域可拖动 */
  -webkit-app-region: drag;
}

.app-title {
  font-size: 13px;
  font-weight: 500;
  color: var(--n-text-color-1);
  opacity: 0.8;
}

.window-controls {
  display: flex;
  align-items: center;
  height: 100%;
  padding-right: 16px;
  /* 确保控制按钮区域不可拖动 */
  -webkit-app-region: no-drag;
}

.control-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

.control-button:hover {
  background-color: var(--n-color-hover);
}

.theme-toggle {
  width: auto;
  padding: 0 8px;
}

/* Windows/Linux 窗口控制按钮样式 */
.control-button.minimize,
.control-button.maximize,
.control-button.close {
  width: 46px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 0;
}

.control-button.minimize:hover,
.control-button.maximize:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.control-button.close:hover {
  background-color: #e81123;
  color: white;
}

/* 根据平台调整样式 - 默认样式适用于所有平台 */
</style>
