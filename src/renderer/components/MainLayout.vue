<template>
  <div class="main-layout">
    <!-- 左侧导航栏 -->
    <div class="sidebar">
      <SideNavigation />
    </div>

    <!-- 右侧主内容区域 -->
    <div class="main-content">
      <router-view />
    </div>

    <!-- 更新管理器 -->
    <UpdateManager ref="updateManagerRef" />
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref } from 'vue'
import SideNavigation from './SideNavigation.vue'
import UpdateManager from './UpdateManager.vue'

const updateManagerRef = ref()

onMounted(() => {
  // 应用启动后延迟检查更新
  setTimeout(() => {
    updateManagerRef.value?.checkForUpdates()
  }, 3000)
})
</script>

<style scoped>
.main-layout {
  display: flex;
  width: 100vw;
  height: calc(100vh - 32px); /* 减去标题栏高度 */
  background-color: var(--n-color);
  position: fixed;
  top: 32px; /* 从标题栏下方开始 */
  left: 0;
  overflow: hidden;
}

.sidebar {
  width: 70px;
  background-color: var(--n-color-embedded);
  flex-shrink: 0;
  position: fixed;
  left: 0;
  top: 32px; /* 从标题栏下方开始 */
  height: calc(100vh - 32px);
  z-index: 100;
}

.main-content {
  flex: 1;
  margin-left: 70px;
  width: calc(100vw - 70px);
  height: calc(100vh - 32px);
  overflow: hidden;
}
</style>
