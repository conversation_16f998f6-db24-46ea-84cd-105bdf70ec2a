<template>
  <div class="themes-list">
    <!-- 顶部搜索和创建 -->
    <div class="header">
      <div class="search-bar">
        <NInput
          v-model:value="searchQuery"
          placeholder="搜索"
          clearable
          @input="handleSearch"
        >
          <template #prefix>
            <NIcon>
              <SearchOutline />
            </NIcon>
          </template>
        </NInput>
      </div>
      <NButton
        type="primary"
        circle
        size="small"
        @click="showCreateDialog = true"
      >
        <template #icon>
          <NIcon>
            <AddOutline />
          </NIcon>
        </template>
      </NButton>
    </div>

    <!-- 主题列表 -->
    <div class="themes-container">
      <NScrollbar style="max-height: calc(100vh - 80px)">
        <div class="themes-list-content">
          <div
            v-for="theme in filteredThemes"
            :key="theme.id"
            class="theme-item"
            :class="{ active: theme.id === currentThemeId }"
            @click="selectTheme(theme.id)"
          >
            <div class="theme-avatar">
              <NAvatar
                round
                size="medium"
                :src="theme.avatar"
                :style="{ backgroundColor: getAvatarColor(theme.title) }"
              >
                {{ theme.title.charAt(0) }}
              </NAvatar>
            </div>

            <div class="theme-content">
              <div class="theme-header">
                <h3 class="theme-title">{{ theme.title }}</h3>
                <!-- {{ theme.id }} -->
                <div class="theme-meta">
                  <span class="time">{{ formatTime(theme.lastMessageTime || theme.updatedAt) }}</span>
                  <NIcon v-if="theme.isStarred" class="star-icon" color="#f0a020">
                    <StarOutline />
                  </NIcon>
                </div>
              </div>

              <div class="theme-preview">
                <p class="last-message">{{ theme.lastMessage || '暂无消息' }}</p>
                <!-- <div v-if="theme.tags.length > 0" class="tags">
                  <NTag
                    v-for="tag in theme.tags.slice(0, 2)"
                    :key="tag"
                    size="small"
                    type="info"
                  >
                    {{ tag }}
                  </NTag>
                </div> -->
              </div>
            </div>

            <!-- 右键菜单 -->
            <NDropdown
              :options="getContextMenuOptions(theme)"
              @select="handleContextMenu"
              trigger="manual"
              :show="contextMenuTheme?.id === theme.id && showContextMenu"
              :x="contextMenuX"
              :y="contextMenuY"
              @clickoutside="showContextMenu = false"
            />
          </div>
        </div>
      </NScrollbar>
    </div>

    <!-- 创建主题对话框 -->
    <NModal v-model:show="showCreateDialog" preset="dialog" title="创建新主题">
      <template #default>
        <NForm ref="createFormRef" :model="createForm" :rules="createRules">
          <NFormItem label="主题标题" path="title">
            <NInput
              v-model:value="createForm.title"
              placeholder="请输入主题标题"
              @keyup.enter="handleCreate"
            />
          </NFormItem>
          <NFormItem label="描述" path="description">
            <NInput
              v-model:value="createForm.description"
              type="textarea"
              placeholder="请输入主题描述（可选）"
              :rows="3"
            />
          </NFormItem>
        </NForm>
      </template>
      <template #action>
        <NSpace>
          <NButton @click="showCreateDialog = false">取消</NButton>
          <NButton type="primary" @click="handleCreate">创建</NButton>
        </NSpace>
      </template>
    </NModal>
  </div>
</template>

<script setup lang="ts">
import {
    AddOutline,
    BookmarkOutline,
    CreateOutline,
    SearchOutline,
    StarOutline,
    TrashOutline
} from '@vicons/ionicons5'
import {
    NAvatar,
    NButton,
    NDropdown,
    NForm,
    NFormItem,
    NIcon,
    NInput,
    NModal,
    NScrollbar,
    NSpace,
    useMessage
} from 'naive-ui'
import { computed, h, onMounted, ref } from 'vue'
import { useNoteThemesStore } from '../stores/noteThemes'
import type { NoteTheme } from '../types'

const message = useMessage()
const themesStore = useNoteThemesStore()

// 搜索
const searchQuery = ref('')
const filteredThemes = computed(() => {
  if (!searchQuery.value.trim()) {
    return themesStore.sortedThemes
  }
  return themesStore.searchThemes(searchQuery.value)
})

// 当前选中的主题
const currentThemeId = computed(() => themesStore.currentThemeId)

// 创建主题对话框
const showCreateDialog = ref(false)
const createFormRef = ref()
const createForm = ref({
  title: '',
  description: ''
})

const createRules = {
  title: {
    required: true,
    message: '请输入主题标题',
    trigger: 'blur'
  }
}

// 右键菜单
const showContextMenu = ref(false)
const contextMenuX = ref(0)
const contextMenuY = ref(0)
const contextMenuTheme = ref<NoteTheme | null>(null)

// 方法
const selectTheme = (themeId: string) => {
  themesStore.setCurrentTheme(themeId)
}

const handleSearch = () => {
  // 搜索逻辑已在计算属性中处理
}

const handleCreate = async () => {
  try {
    await createFormRef.value?.validate()
    const newTheme = await themesStore.createTheme(createForm.value.title, createForm.value.description)
    themesStore.setCurrentTheme(newTheme.id)

    // 重置表单
    createForm.value = { title: '', description: '' }
    showCreateDialog.value = false

    message.success('主题创建成功')
  } catch (error) {
    console.error('创建主题失败:', error)
  }
}

const formatTime = (date: Date) => {
  const now = new Date()
  const diff = now.getTime() - date.getTime()
  const days = Math.floor(diff / (1000 * 60 * 60 * 24))

  if (days === 0) {
    return date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' })
  } else if (days === 1) {
    return '昨天'
  } else if (days < 7) {
    return `${days}天前`
  } else {
    return date.toLocaleDateString('zh-CN', { month: '2-digit', day: '2-digit' })
  }
}

const getAvatarColor = (title: string) => {
  const colors = ['#f56565', '#ed8936', '#ecc94b', '#48bb78', '#38b2ac', '#4299e1', '#667eea', '#9f7aea']
  const index = title.charCodeAt(0) % colors.length
  return colors[index]
}

const getContextMenuOptions = (theme: NoteTheme) => [
  {
    label: theme.isStarred ? '取消收藏' : '收藏',
    key: 'star',
    icon: () => h(NIcon, null, { default: () => h(BookmarkOutline) }),
    theme
  },
  {
    label: '编辑',
    key: 'edit',
    icon: () => h(NIcon, null, { default: () => h(CreateOutline) }),
    theme
  },
  {
    label: '删除',
    key: 'delete',
    icon: () => h(NIcon, null, { default: () => h(TrashOutline) }),
    theme
  }
]

const handleContextMenu = (key: string, option: any) => {
  const theme = option.theme

  switch (key) {
    case 'star':
      themesStore.toggleStar(theme.id)
      message.success(theme.isStarred ? '已取消收藏' : '已收藏')
      break
    case 'edit':
      // TODO: 实现编辑功能
      message.info('编辑功能开发中')
      break
    case 'delete':
      themesStore.deleteTheme(theme.id)
      message.success('主题已删除')
      break
  }

  showContextMenu.value = false
}



// 初始化时选择第一个主题
onMounted(() => {
  if (themesStore.themes.length > 0 && !themesStore.currentThemeId) {
    themesStore.setCurrentTheme(themesStore.themes[0].id)
  }
})
</script>

<style scoped>
.themes-list {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: var(--n-color);
}

.header {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  /* border-bottom: 1px solid var(--n-border-color); */
}

.search-bar {
  flex: 1;
}

.themes-container {
  flex: 1;
  overflow: hidden;
}

.themes-list-content {
  padding: 8px 0;
}

.theme-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 12px 16px;
  cursor: pointer;
  transition: background-color 0.2s ease;
  border-left: 3px solid transparent;
}

.theme-item:hover {
  background-color: var(--n-color-hover);
}

.theme-item.active {
  background-color: var(--n-color-pressed);
  /* border-left-color: var(--n-color-primary); */
}

.theme-avatar {
  flex-shrink: 0;
  margin-top: 2px;
}

.theme-content {
  flex: 1;
  min-width: 0;
}

.theme-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 4px;
}

.theme-title {
  font-size: 14px;
  font-weight: 600;
  margin: 0;
  color: var(--n-text-color-1);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.theme-meta {
  display: flex;
  align-items: center;
  gap: 4px;
  flex-shrink: 0;
}

.time {
  font-size: 12px;
  color: var(--n-text-color-3);
}

.star-icon {
  font-size: 12px;
}

.theme-preview {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.last-message {
  font-size: 13px;
  color: var(--n-text-color-2);
  margin: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.tags {
  display: flex;
  gap: 4px;
  flex-wrap: wrap;
}
</style>
