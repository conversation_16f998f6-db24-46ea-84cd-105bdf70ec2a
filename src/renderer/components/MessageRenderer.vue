<template>
  <div class="message-renderer">
    <!-- {{ message.id }} -->
    <!-- 普通文本消息 -->
    <div
      v-if="message.type === 'text'"
      class="text-content"
    >
      {{ message.content }}
    </div>

    <!-- 链接消息 -->
    <div
      v-else-if="message.type === 'link'"
      class="link-content"
    >
      <a
        :href="message.metadata?.linkUrl || message.content"
        target="_blank"
        rel="noopener noreferrer"
        class="link"
      >
        {{ message.metadata?.linkTitle || message.content }}
      </a>
    </div>

    <!-- 其他类型消息 -->
    <div
      v-else
      class="other-content"
    >
      {{ message.content }}
    </div>
  </div>
</template>

<script setup lang="ts">
import type { NoteMessage } from '../types';

// Props
interface Props {
  message: NoteMessage
}

const props = defineProps<Props>()
</script>

<style scoped>
.message-renderer {
  width: 100%;
}

.text-content {
  white-space: pre-wrap;
  word-wrap: break-word;
  line-height: 1.4;
}

.link-content {
  word-wrap: break-word;
}

.link {
  color: var(--n-primary-color);
  text-decoration: none;
  border-bottom: 1px solid transparent;
  transition: border-color 0.2s ease;
}

.link:hover {
  border-bottom-color: var(--n-primary-color);
}

.other-content {
  white-space: pre-wrap;
  word-wrap: break-word;
  line-height: 1.4;
}




</style>
