<template>
  <div class="side-navigation">
    <!-- 用户头像 -->
    <div class="user-avatar" @click="showUserPanel = true">
      <NAvatar
        round
        size="large"
        :src="userAvatar"
        fallback-src="/default-avatar.png"
      >
        <NIcon size="20">
          <PersonOutline />
        </NIcon>
      </NAvatar>
    </div>

    <!-- 导航菜单 -->
    <div class="nav-menu">
      <div
        v-for="item in navItems"
        :key="item.id"
        class="nav-item"
        :class="{ active: item.active }"
        @click="handleNavClick(item)"
      >
        <NTooltip placement="right" :delay="500">
          <template #trigger>
            <div class="nav-icon">
              <NIcon size="23">
                <component :is="iconMap[item.icon]" />
              </NIcon>
            </div>
          </template>
          {{ item.label }}
        </NTooltip>
      </div>
    </div>

    <!-- 用户信息面板 -->
    <NModal v-model:show="showUserPanel" preset="card" style="width: 400px" title="用户信息">
      <div class="user-panel">
        <div class="user-info">
          <NAvatar size="large" :src="authStore.user?.avatar || '/default-avatar.png'">
            <NIcon size="24">
              <PersonOutline />
            </NIcon>
          </NAvatar>
          <div class="user-details">
            <h3>{{ authStore.user?.nickName || '用户' }}</h3>
            <p>{{ authStore.user?.email }}</p>
          </div>
        </div>

        <NDivider />

        <div class="user-stats">
          <div class="stat-item">
            <span class="label">注册时间</span>
            <span class="value">{{ formatDate(authStore.user?.createdAt) }}</span>
          </div>
        </div>

        <NDivider />

        <div class="user-actions">
          <NButton
            type="error"
            size="large"
            block
            @click="handleLogout"
          >
            退出登录
          </NButton>
        </div>
      </div>
    </NModal>
  </div>
</template>

<script setup lang="ts">
import {
    BookmarkOutline,
    ChatbubbleEllipsesOutline,
    PersonOutline,
    PricetagsOutline,
    SettingsOutline
} from '@vicons/ionicons5'
import {
    NAvatar,
    NButton,
    NDivider,
    NIcon,
    NModal,
    NTooltip,
    useMessage
} from 'naive-ui'
import { computed, ref } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '../stores/auth'

const router = useRouter()
const authStore = useAuthStore()
const message = useMessage()

const userAvatar = computed(() => '/default-avatar.png')
const showUserPanel = ref(false)

// 导航菜单项
const navItems = ref([
  {
    id: 'notes',
    label: '笔记',
    icon: 'ChatbubbleEllipsesOutline',
    path: '/notes',
    active: true
  },
  {
    id: 'tags',
    label: '标签',
    icon: 'PricetagsOutline',
    path: '/tags',
    active: false
  },
  {
    id: 'starred',
    label: '收藏',
    icon: 'BookmarkOutline',
    path: '/starred',
    active: false
  },
  {
    id: 'settings',
    label: '设置',
    icon: 'SettingsOutline',
    path: '/settings',
    active: false
  }
])

// 图标映射
const iconMap = {
  ChatbubbleEllipsesOutline,
  PricetagsOutline,
  BookmarkOutline,
  SettingsOutline
}

// 处理导航点击
const handleNavClick = (item: typeof navItems.value[0]) => {
  // 更新激活状态
  navItems.value.forEach(nav => {
    nav.active = nav.id === item.id
  })

  // 路由跳转
  if (item.path) {
    router.push(item.path)
  }
}

// 格式化日期
const formatDate = (dateString?: string) => {
  if (!dateString) return '-'
  return new Date(dateString).toLocaleDateString('zh-CN')
}

// 处理退出登录
const handleLogout = () => {
  authStore.logout()
  message.success('已退出登录')
  showUserPanel.value = false
  router.push('/login')
}

// 监听路由变化，更新导航状态
router.afterEach((to) => {
  navItems.value.forEach(item => {
    item.active = item.path === to.path
  })
})
</script>

<style scoped>
.side-navigation {
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: 8px;
  padding-top: 16px; /* 标题栏已经处理了顶部间距 */
  background-color: var(--n-color-embedded);
  border-right: 1px solid var(--n-border-color); /* 添加右侧分隔线 */
}

.user-avatar {
  display: flex;
  justify-content: center;
  margin-bottom: 24px;
  cursor: pointer;
  transition: transform 0.2s ease;
}

.user-avatar:hover {
  transform: scale(1.05);
}

.nav-menu {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.nav-item {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 44px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
}

.nav-item:hover {
  background-color: var(--n-divider-color);
  color: var(--n-color-hover);
  color: var(--n-color-primary);
}

.nav-item.active {
  /* background-color: var(--n-color-pressed); */
  color: var(--n-color-primary);
}

.nav-item.active::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 3px;
  height: 20px;
  background-color: var(--n-color-primary);
  border-radius: 0 2px 2px 0;
}

.nav-icon {
  display: flex;
  align-items: center;
  justify-content: center;
}

.user-panel {
  padding: 16px 0;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 16px;
}

.user-details h3 {
  margin: 0 0 4px 0;
  font-size: 16px;
  font-weight: 600;
}

.user-details p {
  margin: 0 0 8px 0;
  color: var(--n-text-color-2);
  font-size: 14px;
}

.user-stats {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.stat-item .label {
  color: var(--n-text-color-2);
  font-size: 14px;
}

.stat-item .value {
  font-size: 14px;
  font-weight: 500;
}

.user-actions {
  margin-top: 16px;
}
</style>
